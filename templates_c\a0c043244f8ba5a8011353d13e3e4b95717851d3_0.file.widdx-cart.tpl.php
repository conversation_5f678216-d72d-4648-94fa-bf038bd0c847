<?php
/* Smarty version 3.1.48, created on 2025-06-14 02:44:20
  from 'C:\xampp\htdocs\Whmcs\templates\widdx\backend\widget\widdx-cart.tpl' */

/* @var Smarty_Internal_Template $_smarty_tpl */
if ($_smarty_tpl->_decodeProperties($_smarty_tpl, array (
  'version' => '3.1.48',
  'unifunc' => 'content_684cc5e46ea6e1_57222449',
  'has_nocache_code' => false,
  'file_dependency' => 
  array (
    'a0c043244f8ba5a8011353d13e3e4b95717851d3' => 
    array (
      0 => 'C:\\xampp\\htdocs\\Whmcs\\templates\\widdx\\backend\\widget\\widdx-cart.tpl',
      1 => 1747863117,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
  ),
),false)) {
function content_684cc5e46ea6e1_57222449 (Smarty_Internal_Template $_smarty_tpl) {
?>
<a class="nav-link nav-btn" href="<?php echo $_smarty_tpl->tpl_vars['WEB_ROOT']->value;?>
/cart.php?a=view">
    <div class="nav-icon-circle">
        <i class="fas fa-shopping-cart"></i>
        <?php if ($_smarty_tpl->tpl_vars['cartitemcount']->value > 0) {?>
            <span id="cartItemCount" class="badge bg-danger badge-number"><?php echo $_smarty_tpl->tpl_vars['cartitemcount']->value;?>
</span>
        <?php }?>
        <span class="visually-hidden"><?php echo call_user_func_array( $_smarty_tpl->smarty->registered_plugins[Smarty::PLUGIN_FUNCTION]['lang'][0], array( array('key'=>"carttitle"),$_smarty_tpl ) );?>
</span>
    </div>
</a>
<?php }
}
