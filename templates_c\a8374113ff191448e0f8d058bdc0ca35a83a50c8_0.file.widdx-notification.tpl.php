<?php
/* Smarty version 3.1.48, created on 2025-06-14 02:44:20
  from 'C:\xampp\htdocs\Whmcs\templates\widdx\backend\widget\widdx-notification.tpl' */

/* @var Smarty_Internal_Template $_smarty_tpl */
if ($_smarty_tpl->_decodeProperties($_smarty_tpl, array (
  'version' => '3.1.48',
  'unifunc' => 'content_684cc5e481b618_45781902',
  'has_nocache_code' => false,
  'file_dependency' => 
  array (
    'a8374113ff191448e0f8d058bdc0ca35a83a50c8' => 
    array (
      0 => 'C:\\xampp\\htdocs\\Whmcs\\templates\\widdx\\backend\\widget\\widdx-notification.tpl',
      1 => 1747863155,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
  ),
),false)) {
function content_684cc5e481b618_45781902 (Smarty_Internal_Template $_smarty_tpl) {
?><li class="nav-item dropdown">
    <a class="nav-link nav-btn dropdown-toggle" href="#" id="notifDropdown" role="button" data-bs-toggle="dropdown"
       aria-haspopup="true" aria-expanded="false">
        <div class="nav-icon-circle">
            <i class="fas fa-bell"></i>
            <?php if (count($_smarty_tpl->tpl_vars['clientAlerts']->value) > 0) {?>
                <span class="badge bg-danger badge-number"><?php echo count($_smarty_tpl->tpl_vars['clientAlerts']->value);?>
</span>
            <?php }?>
        </div>
    </a>
    <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="notifDropdown">
        <li>
            <div class="dropdown-header">
                <?php if (count($_smarty_tpl->tpl_vars['clientAlerts']->value) > 0) {?>
                    You have <?php echo count($_smarty_tpl->tpl_vars['clientAlerts']->value);?>
 new notifications
                <?php } else { ?>
                    <span class="notification">You have 0 new notifications</span>
                <?php }?>
            </div>
        </li>
        <li>
            <div class="dropdown-divider"></div>
        </li>
        <li>
            <div class="notif-scroll" style="max-height: 400px; overflow-y: auto;">
                <div class="notif-center">
                    <?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, $_smarty_tpl->tpl_vars['clientAlerts']->value, 'alert');
$_smarty_tpl->tpl_vars['alert']->do_else = true;
if ($_from !== null) foreach ($_from as $_smarty_tpl->tpl_vars['alert']->value) {
$_smarty_tpl->tpl_vars['alert']->do_else = false;
?>
                        <a class="dropdown-item" href="<?php echo $_smarty_tpl->tpl_vars['alert']->value->getLink();?>
">
                            <div class="d-flex align-items-center">
                                <div class="me-2">
                                    <i class="fas fa-<?php if ($_smarty_tpl->tpl_vars['alert']->value->getSeverity() == 'danger') {?>exclamation-circle<?php } elseif ($_smarty_tpl->tpl_vars['alert']->value->getSeverity() == 'warning') {?>exclamation-triangle<?php } elseif ($_smarty_tpl->tpl_vars['alert']->value->getSeverity() == 'info') {?>info-circle<?php } else { ?>check-circle<?php }?> fa-fw"></i>
                                </div>
                                <div>
                                    <span><?php echo $_smarty_tpl->tpl_vars['alert']->value->getMessage();?>
</span>
                                </div>
                            </div>
                        </a>
                    <?php
}
if ($_smarty_tpl->tpl_vars['alert']->do_else) {
?>
                        <div class="dropdown-item text-center">
                            <?php echo call_user_func_array( $_smarty_tpl->smarty->registered_plugins[Smarty::PLUGIN_FUNCTION]['lang'][0], array( array('key'=>'notificationsnone'),$_smarty_tpl ) );?>

                        </div>
                    <?php
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
                </div>
            </div>
        </li>
    </ul>
</li>
<?php }
}
