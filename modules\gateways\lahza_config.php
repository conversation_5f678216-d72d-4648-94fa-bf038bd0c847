<?php
/**
 * Lahza.io Payment Gateway Advanced Configuration
 * 
 * This file contains advanced configuration options for the Lahza.io payment gateway
 * These settings can be customized without modifying the main gateway file
 * 
 * <AUTHOR> Development Team
 * @version 3.0.0
 */

if (!defined("WHMCS")) {
    die("This file cannot be accessed directly");
}

/**
 * Advanced Configuration Constants
 */

// API Configuration
define('LAHZA_API_BASE_URL', 'https://api.lahza.io/');
define('LAHZA_JS_LIBRARY_URL', 'https://js.lahza.io/inline.min.js');
define('LAHZA_API_TIMEOUT', 30);
define('LAHZA_API_USER_AGENT', 'WHMCS-Lahza/3.0');

// Webhook Configuration
define('LAHZA_WEBHOOK_TIMEOUT', 72); // hours
define('LAHZA_WEBHOOK_RETRY_INTERVAL', 3); // minutes for first 4 attempts
define('LAHZA_WEBHOOK_RETRY_INTERVAL_EXTENDED', 60); // minutes after first 4 attempts

// Default IP Whitelist (Lahza.io official IPs)
define('LAHZA_DEFAULT_IP_WHITELIST', '*************,**************');

// Supported Currencies with their smallest units
define('LAHZA_SUPPORTED_CURRENCIES', [
    'ILS' => ['name' => 'Israeli Shekel', 'unit' => 'agora', 'symbol' => '₪'],
    'USD' => ['name' => 'US Dollar', 'unit' => 'cents', 'symbol' => '$'],
    'JOD' => ['name' => 'Jordanian Dinar', 'unit' => 'qirsh', 'symbol' => 'JD']
]);

// Default Payment Channels
define('LAHZA_DEFAULT_CHANNELS', 'card,bank,mobile_money');

// Available Payment Channels
define('LAHZA_AVAILABLE_CHANNELS', [
    'card' => 'Credit/Debit Cards',
    'bank' => 'Bank Transfer',
    'ussd' => 'USSD',
    'qr' => 'QR Code',
    'mobile_money' => 'Mobile Money',
    'bank_transfer' => 'Direct Bank Transfer'
]);

/**
 * Custom CSS Themes
 */
class LahzaThemes
{
    /**
     * Get WIDDX theme CSS
     */
    public static function getWiddxTheme()
    {
        return '
        .lahza-payment-container {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        
        .payment-header {
            background: transparent;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }
        
        .payment-details {
            background: rgba(255,255,255,0.95);
            backdrop-filter: blur(10px);
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }
        ';
    }
    
    /**
     * Get modern dark theme CSS
     */
    public static function getDarkTheme()
    {
        return '
        .lahza-payment-container {
            background: #1a202c;
            border: 1px solid #2d3748;
        }
        
        .payment-header {
            background: linear-gradient(135deg, #2d3748 0%, #4a5568 100%);
        }
        
        .payment-details {
            background: #2d3748;
            color: #e2e8f0;
        }
        
        .text-muted {
            color: #a0aec0 !important;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
        }
        ';
    }
    
    /**
     * Get minimal theme CSS
     */
    public static function getMinimalTheme()
    {
        return '
        .lahza-payment-container {
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            box-shadow: none;
        }
        
        .payment-header {
            background: #f7fafc;
            border-bottom: 1px solid #e2e8f0;
        }
        
        .payment-header h4 {
            color: #2d3748;
        }
        
        .btn-primary {
            background: #3182ce;
            border-radius: 6px;
        }
        ';
    }
}

/**
 * Utility Functions
 */
class LahzaUtils
{
    /**
     * Format amount for display
     */
    public static function formatAmount($amount, $currency)
    {
        $currencies = LAHZA_SUPPORTED_CURRENCIES;
        $symbol = $currencies[$currency]['symbol'] ?? $currency;
        
        return $symbol . ' ' . number_format($amount, 2);
    }
    
    /**
     * Convert amount to smallest currency unit
     */
    public static function convertToSmallestUnit($amount, $currency)
    {
        // All supported currencies use 100 as the conversion factor
        return (int)($amount * 100);
    }
    
    /**
     * Convert amount from smallest currency unit
     */
    public static function convertFromSmallestUnit($amount, $currency)
    {
        return $amount / 100;
    }
    
    /**
     * Validate currency
     */
    public static function isValidCurrency($currency)
    {
        return array_key_exists($currency, LAHZA_SUPPORTED_CURRENCIES);
    }
    
    /**
     * Get currency info
     */
    public static function getCurrencyInfo($currency)
    {
        return LAHZA_SUPPORTED_CURRENCIES[$currency] ?? null;
    }
    
    /**
     * Generate secure reference
     */
    public static function generateSecureReference($invoiceId, $prefix = 'WHMCS')
    {
        $timestamp = time();
        $random = bin2hex(random_bytes(4));
        $hash = substr(hash('sha256', $invoiceId . $timestamp . $random), 0, 8);
        
        return $prefix . '_' . $invoiceId . '_' . $timestamp . '_' . $hash;
    }
    
    /**
     * Validate webhook IP
     */
    public static function isValidWebhookIP($ip, $whitelist = null)
    {
        if ($whitelist === null) {
            $whitelist = LAHZA_DEFAULT_IP_WHITELIST;
        }
        
        $allowedIPs = array_map('trim', explode(',', $whitelist));
        return in_array($ip, $allowedIPs);
    }
    
    /**
     * Get card brand icon
     */
    public static function getCardBrandIcon($brand)
    {
        $icons = [
            'visa' => 'fab fa-cc-visa',
            'mastercard' => 'fab fa-cc-mastercard',
            'amex' => 'fab fa-cc-amex',
            'discover' => 'fab fa-cc-discover',
            'jcb' => 'fab fa-cc-jcb',
            'diners' => 'fab fa-cc-diners-club',
            'default' => 'fas fa-credit-card'
        ];
        
        return $icons[strtolower($brand)] ?? $icons['default'];
    }
    
    /**
     * Format card display string
     */
    public static function formatCardDisplay($cardData)
    {
        if (empty($cardData['last4'])) {
            return 'Card Payment';
        }
        
        $brand = ucfirst($cardData['brand'] ?? 'Card');
        $last4 = $cardData['last4'];
        $bank = $cardData['bank'] ?? '';
        
        $display = $brand . ' ending in ' . $last4;
        
        if (!empty($bank) && $bank !== 'Unknown') {
            $display .= ' (' . $bank . ')';
        }
        
        return $display;
    }
}

/**
 * Error Messages in Multiple Languages
 */
class LahzaMessages
{
    public static function get($key, $lang = 'en')
    {
        $messages = [
            'en' => [
                'gateway_not_configured' => 'Lahza.io gateway not configured properly. Please contact administrator.',
                'invalid_public_key' => 'Invalid Public Key format.',
                'invalid_secret_key' => 'Invalid Secret Key format.',
                'currency_not_supported' => 'Currency %s is not supported. Supported currencies: %s',
                'payment_cancelled' => 'Payment was cancelled by user',
                'payment_failed' => 'Payment verification failed',
                'processing_payment' => 'Processing payment...',
                'secure_payment' => 'Secure Payment',
                'powered_by' => 'Powered by Lahza.io',
                'ssl_secured' => 'Your payment is secured with 256-bit SSL encryption'
            ],
            'ar' => [
                'gateway_not_configured' => 'بوابة Lahza.io غير مكونة بشكل صحيح. يرجى الاتصال بالمسؤول.',
                'invalid_public_key' => 'صيغة المفتاح العام غير صحيحة.',
                'invalid_secret_key' => 'صيغة المفتاح السري غير صحيحة.',
                'currency_not_supported' => 'العملة %s غير مدعومة. العملات المدعومة: %s',
                'payment_cancelled' => 'تم إلغاء الدفع من قبل المستخدم',
                'payment_failed' => 'فشل في التحقق من الدفع',
                'processing_payment' => 'جاري معالجة الدفع...',
                'secure_payment' => 'دفع آمن',
                'powered_by' => 'مدعوم من Lahza.io',
                'ssl_secured' => 'دفعتك محمية بتشفير SSL 256-bit'
            ]
        ];
        
        return $messages[$lang][$key] ?? $messages['en'][$key] ?? $key;
    }
}
