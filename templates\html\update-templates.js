const fs = require('fs');
const path = require('path');

// List of template files to update
const templateFiles = [
    'about.html',
    'services.html',
    'portfolio.html',
    'contact.html',
    'client-area.html',
    'knowledgebase.html',
    'client-dashboard.html',
    'optimize.html'
];

// CSS and JS includes to add
const cssInclude = `
    <!-- Background System CSS -->
    <link rel="preload" href="background/background.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
    <noscript><link rel="stylesheet" href="background/background.css"></noscript>`;

const jsInclude = `
    <!-- Background System -->
    <script src="background/background.js"></script>`;

// Process each template file
templateFiles.forEach(file => {
    const filePath = path.join(__dirname, file);
    
    // Read the file
    let content = fs.readFileSync(filePath, 'utf8');
    
    // Add CSS include after the main style.css include
    content = content.replace(
        /<link rel="preload" href="css\/style.css".*?<\/noscript>/s,
        match => match + cssInclude
    );
    
    // Add JS include before the closing body tag
    content = content.replace(
        /<\/body>/,
        jsInclude + '\n</body>'
    );
    
    // Write the updated content back to the file
    fs.writeFileSync(filePath, content, 'utf8');
    
    console.log(`Updated ${file}`);
}); 