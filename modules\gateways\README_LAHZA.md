# Lahza.io Payment Gateway for WHMCS

## نظرة عامة

بوابة دفع Lahza.io المطورة خصيصاً لـ WHMCS مع دعم كامل لـ API v2024 وتكامل شامل مع قالب WIDDX.

## الميزات الجديدة

### ✅ تكامل API كامل
- دعم كامل لـ Lahza.io API v2024
- معالجة شاملة للأخطاء والاستثناءات
- تسجيل مفصل للمعاملات
- دعم Webhooks مع التحقق من التوقيع

### ✅ معلومات البطاقة المفصلة
- عرض نوع البطاقة (Visa, Mastercard, إلخ)
- معلومات البنك المصدر
- آخر 4 أرقام من البطاقة
- تاريخ انتهاء الصلاحية
- العلامة التجارية للبطاقة

### ✅ أمان محسن
- التحقق من توقيع Webhook
- قائمة بيضاء لعناوين IP
- تشفير SSL إجباري
- منع المعاملات المكررة

### ✅ واجهة مستخدم حديثة
- تصميم متجاوب وحديث
- دعم الوضع المظلم
- دعم RTL للغة العربية
- رسوم متحركة وتأثيرات بصرية

## التكوين

### المتطلبات
- WHMCS 8.0+
- PHP 7.4+
- cURL extension
- SSL certificate

### الإعدادات المتاحة

| الإعداد | الوصف | القيم المقبولة |
|---------|--------|----------------|
| Public Key | مفتاح Lahza العام | pk_test_... أو pk_live_... |
| Secret Key | مفتاح Lahza السري | sk_test_... أو sk_live_... |
| Test Mode | وضع الاختبار | نعم/لا |
| Payment Method | طريقة الدفع | Popup/Redirect |
| Payment Channels | قنوات الدفع المسموحة | card,bank,mobile_money |
| Webhook URL | رابط Webhook | تلقائي أو مخصص |
| Callback URL | رابط العودة | تلقائي أو مخصص |
| Enable Logging | تفعيل السجلات | نعم/لا |
| Show Card Type | عرض نوع البطاقة | نعم/لا |
| Custom CSS | CSS مخصص | كود CSS |
| IP Whitelist | قائمة IP البيضاء | عناوين IP مفصولة بفاصلة |

## العملات المدعومة

- **ILS** - الشيكل الإسرائيلي (أغورة)
- **USD** - الدولار الأمريكي (سنت)
- **JOD** - الدينار الأردني (قرش)

## قنوات الدفع

- `card` - بطاقات الائتمان/الخصم
- `bank` - التحويل البنكي
- `mobile_money` - المحافظ الرقمية
- `ussd` - USSD
- `qr` - رمز QR
- `bank_transfer` - تحويل بنكي

## التثبيت

1. رفع الملفات إلى مجلد `modules/gateways/`
2. تفعيل البوابة من لوحة تحكم WHMCS
3. إدخال مفاتيح Lahza.io
4. تكوين الإعدادات حسب الحاجة

## الاختبار

### بيانات الاختبار
```
Public Key: pk_test_xxxxxxxxxx
Secret Key: sk_test_xxxxxxxxxx
Test Mode: نعم
```

### بطاقات الاختبار
```
Visa: ****************
Mastercard: ****************
CVV: 123
Expiry: 12/28
```

## Webhooks

### الأحداث المدعومة
- `charge.success` - نجح الدفع
- `refund.processed` - تم الاسترداد
- `refund.failed` - فشل الاسترداد

### التحقق من التوقيع
```php
$signature = $_SERVER['HTTP_X_LAHZA_SIGNATURE'];
$payload = file_get_contents('php://input');
$expectedSignature = hash_hmac('sha256', $payload, $secretKey);
$isValid = hash_equals($expectedSignature, $signature);
```

## السجلات والتتبع

### أنواع السجلات
- معاملات ناجحة مع تفاصيل البطاقة
- أخطاء API مع رسائل مفصلة
- محاولات Webhook مع التحقق
- أخطاء التكوين والتحقق

### مثال على سجل المعاملة
```json
{
  "Transaction ID": "WHMCS_123_1234567890_abc123",
  "Invoice ID": "123",
  "Amount": "50.00",
  "Fee": "1.50",
  "Status": "success",
  "Method": "POST",
  "Card Type": "visa DEBIT",
  "Bank": "Bank of Palestine",
  "Card Display": "visa ending in 1234 (Bank of Palestine)",
  "Last 4 Digits": "1234"
}
```

## استكشاف الأخطاء

### مشاكل شائعة

#### 1. خطأ في المفاتيح
```
Error: Invalid Public Key format
```
**الحل:** تأكد من صيغة المفتاح `pk_test_` أو `pk_live_`

#### 2. فشل Webhook
```
Error: Invalid webhook signature
```
**الحل:** تحقق من Secret Key وتكوين Webhook URL

#### 3. عملة غير مدعومة
```
Error: Currency XXX is not supported
```
**الحل:** استخدم ILS, USD, أو JOD فقط

### تفعيل السجلات المفصلة
1. اذهب إلى إعدادات البوابة
2. فعّل "Enable Logging"
3. راجع سجلات WHMCS في `Utilities > Logs > Gateway Log`

## الدعم الفني

### معلومات الاتصال
- **المطور:** فريق WIDDX
- **الإصدار:** 3.0.0
- **التوافق:** WHMCS 8.0+
- **آخر تحديث:** 2024

### الموارد المفيدة
- [وثائق Lahza.io](https://docs.lahza.io/)
- [API Documentation](https://api-docs.lahza.io/)
- [حالة الخدمة](https://statuspage.freshping.io/61673-Lahza)

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف LICENSE للتفاصيل.

## حقوق الطبع والنشر

© 2024 WIDDX. جميع الحقوق محفوظة.
