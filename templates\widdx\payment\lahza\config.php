<?php
/**
 * Lahza.io Payment Gateway Configuration for WHMCS WIDDX Template
 * 
 * Additional configuration and helper functions for enhanced integration
 * 
 * <AUTHOR> Development Team
 * @version 2.0.0
 */

if (!defined("WHMCS")) {
    die("This file cannot be accessed directly");
}

/**
 * Lahza Payment Gateway Configuration Class
 */
class LahzaPaymentConfig {
    
    /**
     * Supported currencies with their details
     */
    const SUPPORTED_CURRENCIES = [
        'ILS' => [
            'name' => 'Israeli Shekel',
            'symbol' => '₪',
            'decimal_places' => 2,
            'min_amount' => 1.00
        ],
        'USD' => [
            'name' => 'US Dollar',
            'symbol' => '$',
            'decimal_places' => 2,
            'min_amount' => 1.00
        ],
        'JOD' => [
            'name' => 'Jordanian Dinar',
            'symbol' => 'د.ا',
            'decimal_places' => 3,
            'min_amount' => 1.000
        ]
    ];
    
    /**
     * Available payment channels
     */
    const PAYMENT_CHANNELS = [
        'card' => [
            'name' => 'Credit/Debit Card',
            'icon' => 'fas fa-credit-card',
            'description' => 'Pay with your credit or debit card'
        ],
        'bank' => [
            'name' => 'Bank Transfer',
            'icon' => 'fas fa-university',
            'description' => 'Direct bank transfer'
        ],
        'mobile_money' => [
            'name' => 'Mobile Money',
            'icon' => 'fas fa-mobile-alt',
            'description' => 'Pay with mobile wallet'
        ],
        'qr' => [
            'name' => 'QR Code',
            'icon' => 'fas fa-qrcode',
            'description' => 'Scan QR code to pay'
        ],
        'ussd' => [
            'name' => 'USSD',
            'icon' => 'fas fa-phone',
            'description' => 'Pay via USSD code'
        ],
        'bank_transfer' => [
            'name' => 'Bank Transfer',
            'icon' => 'fas fa-exchange-alt',
            'description' => 'Online bank transfer'
        ]
    ];
    
    /**
     * Default configuration values
     */
    const DEFAULT_CONFIG = [
        'payment_method' => 'popup',
        'allowed_channels' => 'card,bank,mobile_money',
        'enable_logging' => false,
        'retry_attempts' => 3,
        'timeout' => 30,
        'webhook_timeout' => 72, // hours
    ];
    
    /**
     * API endpoints
     */
    const API_ENDPOINTS = [
        'base_url' => 'https://api.lahza.io',
        'initialize' => '/transaction/initialize',
        'verify' => '/transaction/verify',
        'refund' => '/refund',
        'js_library' => 'https://js.lahza.io/inline.min.js'
    ];
    
    /**
     * Webhook IP addresses for whitelisting
     */
    const WEBHOOK_IPS = [
        '*************',
        '**************'
    ];
    
    /**
     * Get currency information
     */
    public static function getCurrencyInfo($currency) {
        return self::SUPPORTED_CURRENCIES[$currency] ?? null;
    }
    
    /**
     * Check if currency is supported
     */
    public static function isCurrencySupported($currency) {
        return array_key_exists($currency, self::SUPPORTED_CURRENCIES);
    }
    
    /**
     * Get payment channel information
     */
    public static function getChannelInfo($channel) {
        return self::PAYMENT_CHANNELS[$channel] ?? null;
    }
    
    /**
     * Validate payment channels
     */
    public static function validateChannels($channels) {
        if (is_string($channels)) {
            $channels = array_map('trim', explode(',', $channels));
        }
        
        $validChannels = [];
        foreach ($channels as $channel) {
            if (array_key_exists($channel, self::PAYMENT_CHANNELS)) {
                $validChannels[] = $channel;
            }
        }
        
        return $validChannels;
    }
    
    /**
     * Convert amount to cents (lowest currency unit)
     */
    public static function convertToCents($amount, $currency) {
        $currencyInfo = self::getCurrencyInfo($currency);
        if (!$currencyInfo) {
            throw new InvalidArgumentException("Unsupported currency: $currency");
        }
        
        $multiplier = pow(10, $currencyInfo['decimal_places']);
        return (int)($amount * $multiplier);
    }
    
    /**
     * Convert amount from cents to currency unit
     */
    public static function convertFromCents($amount, $currency) {
        $currencyInfo = self::getCurrencyInfo($currency);
        if (!$currencyInfo) {
            throw new InvalidArgumentException("Unsupported currency: $currency");
        }
        
        $divisor = pow(10, $currencyInfo['decimal_places']);
        return $amount / $divisor;
    }
    
    /**
     * Format amount for display
     */
    public static function formatAmount($amount, $currency) {
        $currencyInfo = self::getCurrencyInfo($currency);
        if (!$currencyInfo) {
            return $amount;
        }
        
        return number_format($amount, $currencyInfo['decimal_places']) . ' ' . $currencyInfo['symbol'];
    }
    
    /**
     * Generate transaction reference
     */
    public static function generateTransactionRef($invoiceId, $prefix = 'WHMCS') {
        return $prefix . '_' . $invoiceId . '_' . time() . '_' . substr(md5(uniqid()), 0, 8);
    }
    
    /**
     * Validate webhook IP
     */
    public static function isValidWebhookIP($ip) {
        return in_array($ip, self::WEBHOOK_IPS);
    }
    
    /**
     * Get API endpoint URL
     */
    public static function getApiUrl($endpoint) {
        if (!array_key_exists($endpoint, self::API_ENDPOINTS)) {
            throw new InvalidArgumentException("Unknown API endpoint: $endpoint");
        }
        
        if ($endpoint === 'base_url') {
            return self::API_ENDPOINTS[$endpoint];
        }
        
        return self::API_ENDPOINTS['base_url'] . self::API_ENDPOINTS[$endpoint];
    }
    
    /**
     * Validate configuration
     */
    public static function validateConfig($config) {
        $errors = [];
        
        // Check required fields
        if (empty($config['publicKey'])) {
            $errors[] = 'Public Key is required';
        }
        
        if (empty($config['secretKey'])) {
            $errors[] = 'Secret Key is required';
        }
        
        // Validate key format
        if (!empty($config['publicKey']) && !preg_match('/^pk_(test_|live_)?[a-zA-Z0-9]+$/', $config['publicKey'])) {
            $errors[] = 'Invalid Public Key format';
        }
        
        if (!empty($config['secretKey']) && !preg_match('/^sk_(test_|live_)?[a-zA-Z0-9]+$/', $config['secretKey'])) {
            $errors[] = 'Invalid Secret Key format';
        }
        
        // Validate channels
        if (!empty($config['allowedChannels'])) {
            $channels = self::validateChannels($config['allowedChannels']);
            if (empty($channels)) {
                $errors[] = 'At least one valid payment channel is required';
            }
        }
        
        return $errors;
    }
    
    /**
     * Get default configuration merged with custom config
     */
    public static function getConfig($customConfig = []) {
        return array_merge(self::DEFAULT_CONFIG, $customConfig);
    }
    
    /**
     * Log activity (if logging is enabled)
     */
    public static function log($message, $data = null, $level = 'info') {
        // This would integrate with WHMCS logging system
        if (function_exists('logActivity')) {
            $logMessage = '[Lahza.io] ' . $message;
            if ($data) {
                $logMessage .= ' - ' . json_encode($data);
            }
            logActivity($logMessage);
        }
    }
    
    /**
     * Get localized strings
     */
    public static function getLocalizedStrings($language = 'english') {
        $strings = [
            'english' => [
                'secure_payment' => 'Secure Payment',
                'powered_by' => 'Powered by Lahza.io',
                'invoice' => 'Invoice',
                'amount' => 'Amount',
                'description' => 'Description',
                'client' => 'Client',
                'pay_now' => 'Pay Now',
                'processing' => 'Processing...',
                'payment_cancelled' => 'Payment was cancelled',
                'payment_error' => 'Payment error occurred',
                'ssl_secured' => 'Your payment is secured with 256-bit SSL encryption',
                'need_help' => 'Need help?',
                'contact_us' => 'Contact us'
            ],
            'arabic' => [
                'secure_payment' => 'دفع آمن',
                'powered_by' => 'مدعوم بواسطة Lahza.io',
                'invoice' => 'رقم الفاتورة',
                'amount' => 'المبلغ',
                'description' => 'الوصف',
                'client' => 'العميل',
                'pay_now' => 'ادفع الآن',
                'processing' => 'جاري المعالجة...',
                'payment_cancelled' => 'تم إلغاء الدفع',
                'payment_error' => 'حدث خطأ في الدفع',
                'ssl_secured' => 'دفعتك محمية بتشفير SSL 256-bit',
                'need_help' => 'تحتاج مساعدة؟',
                'contact_us' => 'اتصل بنا'
            ]
        ];
        
        return $strings[$language] ?? $strings['english'];
    }
}

/**
 * Helper function to get Lahza config
 */
function getLahzaConfig($customConfig = []) {
    return LahzaPaymentConfig::getConfig($customConfig);
}

/**
 * Helper function to validate Lahza payment data
 */
function validateLahzaPayment($amount, $currency, $channels = null) {
    $errors = [];
    
    // Validate currency
    if (!LahzaPaymentConfig::isCurrencySupported($currency)) {
        $errors[] = "Currency $currency is not supported";
    }
    
    // Validate amount
    $currencyInfo = LahzaPaymentConfig::getCurrencyInfo($currency);
    if ($currencyInfo && $amount < $currencyInfo['min_amount']) {
        $errors[] = "Amount must be at least {$currencyInfo['min_amount']} $currency";
    }
    
    // Validate channels
    if ($channels) {
        $validChannels = LahzaPaymentConfig::validateChannels($channels);
        if (empty($validChannels)) {
            $errors[] = "No valid payment channels specified";
        }
    }
    
    return $errors;
}
