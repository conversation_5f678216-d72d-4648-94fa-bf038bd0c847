/*
 * WHMCS WIDDX Theme System
 * Consolidated Theme Variables and Dark/Light Mode Support
 */

@import url("https://fonts.googleapis.com/css2?family=Cairo:wght@200..1000&display=swap");

/* Light Theme Variables (Default) */
:root {
  /* Primary Colors */
  --primary: #a27ac0 !important;
  --primary-dark: #905bb9 !important;
  --primary-darker: #5e1b92 !important;
  --primary-light: #d1dcfe !important;
  --primary-darker-rgb: 94, 27, 146;

  /* Background Colors */
  --body-bg: transparent !important;
  --header-bg: transparent !important;
  --sidebar-bg: transparent !important;
  --card-bg: transparent !important;
  --modal-bg: transparent !important;
  --dropdown-bg: transparent !important;
  --header-light: transparent !important;

  /* Text Colors */
  --text-color: #404040 !important;
  --text-muted: #6c757d !important;
  --heading-color: #1d1d1f !important;

  /* UI Element Colors */
  --btn-bg: transparent !important;
  --btn-color: #1d1d1f !important;
  --btn-color-hover: #ffffff !important;
  --btn-color-custom: #1d1d1f !important;
  --box-hover: transparent !important;

  /* Status Colors */
  --success: #83d617 !important;
  --info: #2fd9ef !important;
  --warning: #f9951b !important;
  --danger: #e83737 !important;
  --success-rgb: 131, 214, 23;
  --info-rgb: 47, 217, 239;
  --warning-rgb: 249, 149, 27;
  --danger-rgb: 232, 55, 55;

  /* Status Light Colors */
  --success-light: #f1fccf !important;
  --danger-light: #fde4d7 !important;
  --warning-light: #fef2d1 !important;

  /* Neutral Colors */
  --gray: #878c8f !important;
  --gray-light: transparent !important;
  --gray-dark: #343a40 !important;
  --dark: #1d1d1f !important;

  /* Border Colors */
  --border-color: #e9eaec !important;
  --border-dark-color: #5e1b92 !important;
  --border-primary-light: #905bb9 !important;

  /* UI Properties */
  --custom-radius: 0.375rem !important;
  --box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075) !important;
  --box-shadow-lg: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;

  /* Typography */
  --font-family-sans-serif: "Cairo", sans-serif !important;
  --font-family-monospace: "Cairo", monospace !important;
}

/* Dark Theme Variables */
:root[data-bs-theme="dark"] {
  /* Primary Colors - Keep consistent with light theme */
  --primary: #a27ac0 !important;
  --primary-dark: #905bb9 !important;
  --primary-darker: #5e1b92 !important;
  --primary-light: #d1dcfe !important;
  --primary-darker-rgb: 94, 27, 146;

  /* Background Colors */
  --body-bg: #0f172a !important;
  --header-bg: #1e293b !important;
  --sidebar-bg: #1e293b !important;
  --card-bg: #1e293b !important;
  --modal-bg: #1e293b !important;
  --dropdown-bg: #1e293b !important;
  --header-light: #1e293b !important;

  /* Text Colors */
  --text-color: #f8fafc !important;
  --text-muted: #94a3b8 !important;
  --heading-color: #f8fafc !important;

  /* UI Element Colors */
  --btn-bg: #1d1d1f !important;
  --btn-color: #ffffff !important;
  --btn-color-hover: #ffffff !important;
  --btn-color-custom: #000000 !important;
  --box-hover: #cbabe4 !important;

  /* Status Colors - Keep consistent with light theme */
  --success: #83d617 !important;
  --info: #2fd9ef !important;
  --warning: #f9951b !important;
  --danger: #e83737 !important;
  --success-rgb: 131, 214, 23;
  --info-rgb: 47, 217, 239;
  --warning-rgb: 249, 149, 27;
  --danger-rgb: 232, 55, 55;

  /* Status Light Colors - Slightly darker for dark theme */
  --success-light: #d1e7a3 !important;
  --danger-light: #f8c4b4 !important;
  --warning-light: #f9e2b2 !important;

  /* Neutral Colors */
  --gray: #334155 !important;
  --gray-light: #1e293b !important;
  --gray-dark: #0f172a !important;
  --dark: #0f172a !important;

  /* Border Colors */
  --border-color: #334155 !important;
  --border-dark-color: #5e1b92 !important;
  --border-primary-light: #905bb9 !important;

  /* UI Properties */
  --custom-radius: 0.375rem !important;
  --box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.2) !important;
  --box-shadow-lg: 0 0.5rem 1rem rgba(0, 0, 0, 0.4) !important;
}

/* Theme Toggle Button Styles */
/* Floating button styles - kept for backward compatibility but hidden by default */
.ww-color-switch {
  display: none; /* Hide the floating button */
  position: fixed;
  right: 20px;
  bottom: 20px;
  z-index: 9999;
}

/* Common styles for both navbar and floating toggle */
.ww-theme-toggle {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
  cursor: pointer;
  border: none;
  outline: none;
}

/* Styles specific to the floating toggle button */
.ww-color-switch .ww-theme-toggle {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: var(--primary);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  padding: 0;
}

.ww-color-switch .ww-theme-toggle:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

/* Styles specific to the navbar toggle button */
.nav-item .ww-theme-toggle {
  width: auto;
  height: auto;
  background-color: transparent;
  box-shadow: none;
  padding: 0.5rem;
}

.nav-item .ww-theme-toggle:hover {
  transform: scale(1.1);
}

/* Common styles for theme icons */
.ww-theme-light, .ww-theme-dark {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: opacity 0.3s ease, transform 0.3s ease;
}

/* Light theme icon (moon) - shown when in light mode */
.ww-theme-light {
  opacity: 1;
  transform: translateY(0);
}

/* Dark theme icon (sun) - shown when in dark mode */
.ww-theme-dark {
  opacity: 0;
  transform: translateY(50px);
}

/* When in dark mode, switch the visibility of icons */
:root[data-bs-theme="dark"] .ww-theme-light {
  opacity: 0;
  transform: translateY(-50px);
}

:root[data-bs-theme="dark"] .ww-theme-dark {
  opacity: 1;
  transform: translateY(0);
}

/* Icon sizes */
.ww-color-switch .ww-theme-toggle i {
  font-size: 24px;
  color: var(--btn-bg);
}

.nav-item .ww-theme-toggle i {
  font-size: 18px;
  color: var(--text-color);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  /* Show floating button on mobile */
  .ww-color-switch {
    display: block;
    right: 15px;
    bottom: 15px;
  }

  .ww-color-switch .ww-theme-toggle {
    width: 45px;
    height: 45px;
  }

  .ww-color-switch .ww-theme-toggle i {
    font-size: 20px;
  }
}

/* =========================================
   COMPREHENSIVE DARK MODE STYLES
   ========================================= */

/* Base Elements */
[data-bs-theme="dark"] {
  color-scheme: dark;
}

[data-bs-theme="dark"] body {
  background-color: var(--body-bg);
  color: var(--text-color);
}

/* Typography */
[data-bs-theme="dark"] h1,
[data-bs-theme="dark"] h2,
[data-bs-theme="dark"] h3,
[data-bs-theme="dark"] h4,
[data-bs-theme="dark"] h5,
[data-bs-theme="dark"] h6,
[data-bs-theme="dark"] .h1,
[data-bs-theme="dark"] .h2,
[data-bs-theme="dark"] .h3,
[data-bs-theme="dark"] .h4,
[data-bs-theme="dark"] .h5,
[data-bs-theme="dark"] .h6 {
  color: var(--heading-color) !important;
}

[data-bs-theme="dark"] a:not(.btn):not(.nav-link) {
  color: var(--primary);
}

[data-bs-theme="dark"] a:not(.btn):not(.nav-link):hover {
  color: var(--primary-dark);
}

[data-bs-theme="dark"] .text-muted {
  color: var(--text-muted) !important;
}

[data-bs-theme="dark"] small,
[data-bs-theme="dark"] .small {
  color: var(--text-muted);
}

/* Layout Components */
[data-bs-theme="dark"] .header,
[data-bs-theme="dark"] .navbar,
[data-bs-theme="dark"] .layout-navbar {
  background-color: var(--header-bg) !important;
  color: var(--text-color) !important;
  border-color: var(--border-color) !important;
}

[data-bs-theme="dark"] .sidebar,
[data-bs-theme="dark"] .widdxsidebar,
[data-bs-theme="dark"] .bg-menu-theme {
  background-color: var(--sidebar-bg) !important;
  border-color: var(--border-color) !important;
}

[data-bs-theme="dark"] .footer {
  background-color: var(--header-bg) !important;
  color: var(--text-color) !important;
  border-color: var(--border-color) !important;
}

/* Cards & Panels */
[data-bs-theme="dark"] .card,
[data-bs-theme="dark"] .panel,
[data-bs-theme="dark"] .container-fluid,
[data-bs-theme="dark"] .mc-promo-manage,
[data-bs-theme="dark"] .mc-promo-login {
  background-color: var(--card-bg) !important;
  color: var(--text-color) !important;
  border-color: var(--border-color) !important;
}

[data-bs-theme="dark"] .card-header,
[data-bs-theme="dark"] .panel-heading {
  background-color: var(--gray-light) !important;
  border-color: var(--border-color) !important;
  color: var(--heading-color) !important;
}

[data-bs-theme="dark"] .card-footer,
[data-bs-theme="dark"] .panel-footer {
  background-color: var(--gray-light) !important;
  border-color: var(--border-color) !important;
}

/* Navigation */
[data-bs-theme="dark"] .nav-link,
[data-bs-theme="dark"] .dropdown-item {
  color: var(--text-color) !important;
}

[data-bs-theme="dark"] .nav-link:hover,
[data-bs-theme="dark"] .dropdown-item:hover {
  background-color: var(--gray-light) !important;
}

[data-bs-theme="dark"] .nav-tabs {
  border-color: var(--border-color) !important;
}

[data-bs-theme="dark"] .nav-tabs .nav-link.active,
[data-bs-theme="dark"] .nav-tabs .nav-item.show .nav-link {
  background-color: var(--card-bg) !important;
  color: var(--primary) !important;
  border-color: var(--border-color) var(--border-color) var(--card-bg) !important;
}

[data-bs-theme="dark"] .dropdown-menu {
  background-color: var(--dropdown-bg) !important;
  border-color: var(--border-color) !important;
}

[data-bs-theme="dark"] .dropdown-divider {
  border-color: var(--border-color) !important;
}

/* Forms */
[data-bs-theme="dark"] .form-control,
[data-bs-theme="dark"] .input-group-text,
[data-bs-theme="dark"] .custom-select,
[data-bs-theme="dark"] .custom-file-label {
  background-color: var(--card-bg) !important;
  border-color: var(--border-color) !important;
  color: var(--text-color) !important;
}

[data-bs-theme="dark"] .form-control:focus {
  background-color: var(--card-bg) !important;
  border-color: var(--primary) !important;
  box-shadow: 0 0 0 0.2rem rgba(var(--primary-darker-rgb), 0.25) !important;
}

[data-bs-theme="dark"] .form-control::placeholder {
  color: var(--text-muted) !important;
  opacity: 0.7;
}

[data-bs-theme="dark"] .form-control:disabled,
[data-bs-theme="dark"] .form-control[readonly] {
  background-color: var(--gray-light) !important;
  opacity: 0.7;
}

[data-bs-theme="dark"] .form-check-input {
  background-color: var(--card-bg) !important;
  border-color: var(--border-color) !important;
}

[data-bs-theme="dark"] .form-check-input:checked {
  background-color: var(--primary) !important;
  border-color: var(--primary) !important;
}

/* Tables */
[data-bs-theme="dark"] .table {
  color: var(--text-color) !important;
  border-color: var(--border-color) !important;
}

[data-bs-theme="dark"] .table thead th {
  background-color: var(--gray-light) !important;
  color: var(--heading-color) !important;
  border-color: var(--border-color) !important;
}

[data-bs-theme="dark"] .table td,
[data-bs-theme="dark"] .table th {
  border-color: var(--border-color) !important;
}

[data-bs-theme="dark"] .table-striped tbody tr:nth-of-type(odd) {
  background-color: rgba(255, 255, 255, 0.05) !important;
}

[data-bs-theme="dark"] .table-hover tbody tr:hover {
  background-color: rgba(255, 255, 255, 0.075) !important;
  color: var(--text-color) !important;
}

/* DataTables Specific */
[data-bs-theme="dark"] .dataTables_wrapper .dataTables_length,
[data-bs-theme="dark"] .dataTables_wrapper .dataTables_filter,
[data-bs-theme="dark"] .dataTables_wrapper .dataTables_info,
[data-bs-theme="dark"] .dataTables_wrapper .dataTables_processing,
[data-bs-theme="dark"] .dataTables_wrapper .dataTables_paginate {
  color: var(--text-color) !important;
}

[data-bs-theme="dark"] .dataTables_wrapper .dataTables_filter input {
  background-color: var(--card-bg) !important;
  border-color: var(--border-color) !important;
  color: var(--text-color) !important;
}

[data-bs-theme="dark"] table.dataTable thead th.sorting:before,
[data-bs-theme="dark"] table.dataTable thead th.sorting_asc:before,
[data-bs-theme="dark"] table.dataTable thead th.sorting_desc:before {
  color: var(--primary) !important;
}

[data-bs-theme="dark"] .dataTables_wrapper .dataTables_paginate .paginate_button {
  color: var(--text-color) !important;
}

[data-bs-theme="dark"] .dataTables_wrapper .dataTables_paginate .paginate_button.current,
[data-bs-theme="dark"] .dataTables_wrapper .dataTables_paginate .paginate_button.current:hover {
  background: var(--primary) !important;
  color: white !important;
  border-color: var(--primary-dark) !important;
}

[data-bs-theme="dark"] .dataTables_wrapper .dataTables_paginate .paginate_button:hover {
  background: var(--gray-light) !important;
  color: var(--text-color) !important;
  border-color: var(--border-color) !important;
}

/* Buttons */
[data-bs-theme="dark"] .btn-default,
[data-bs-theme="dark"] .btn-secondary {
  background-color: var(--gray) !important;
  border-color: var(--gray-dark) !important;
  color: var(--text-color) !important;
}

[data-bs-theme="dark"] .btn-default:hover,
[data-bs-theme="dark"] .btn-secondary:hover {
  background-color: var(--gray-dark) !important;
  border-color: var(--dark) !important;
}

[data-bs-theme="dark"] .btn-primary {
  background-color: var(--primary) !important;
  border-color: var(--primary-dark) !important;
}

[data-bs-theme="dark"] .btn-primary:hover {
  background-color: var(--primary-dark) !important;
  border-color: var(--primary-darker) !important;
}

[data-bs-theme="dark"] .btn-outline-primary {
  color: var(--primary) !important;
  border-color: var(--primary) !important;
}

[data-bs-theme="dark"] .btn-outline-primary:hover {
  background-color: var(--primary) !important;
  color: white !important;
}

[data-bs-theme="dark"] .btn-link {
  color: var(--primary) !important;
}

[data-bs-theme="dark"] .btn-link:hover {
  color: var(--primary-dark) !important;
}

/* Alerts & Notifications */
[data-bs-theme="dark"] .alert {
  border-color: var(--border-color) !important;
}

[data-bs-theme="dark"] .alert-info {
  background-color: rgba(var(--info), 0.15) !important;
  color: var(--text-color) !important;
}

[data-bs-theme="dark"] .alert-success {
  background-color: rgba(var(--success), 0.15) !important;
  color: var(--text-color) !important;
}

[data-bs-theme="dark"] .alert-warning {
  background-color: rgba(var(--warning), 0.15) !important;
  color: var(--text-color) !important;
}

[data-bs-theme="dark"] .alert-danger {
  background-color: rgba(var(--danger), 0.15) !important;
  color: var(--text-color) !important;
}

/* Modals */
[data-bs-theme="dark"] .modal-content {
  background-color: var(--modal-bg) !important;
  border-color: var(--border-color) !important;
}

[data-bs-theme="dark"] .modal-header,
[data-bs-theme="dark"] .modal-footer {
  border-color: var(--border-color) !important;
}

[data-bs-theme="dark"] .modal-backdrop.show {
  opacity: 0.7 !important;
}

/* Pagination */
[data-bs-theme="dark"] .pagination .page-link {
  background-color: var(--card-bg) !important;
  border-color: var(--border-color) !important;
  color: var(--text-color) !important;
}

[data-bs-theme="dark"] .pagination .page-item.active .page-link {
  background-color: var(--primary) !important;
  border-color: var(--primary) !important;
  color: white !important;
}

[data-bs-theme="dark"] .pagination .page-item.disabled .page-link {
  background-color: var(--gray-light) !important;
  color: var(--text-muted) !important;
}

/* Borders */
[data-bs-theme="dark"] .border,
[data-bs-theme="dark"] .border-top,
[data-bs-theme="dark"] .border-right,
[data-bs-theme="dark"] .border-bottom,
[data-bs-theme="dark"] .border-left {
  border-color: var(--border-color) !important;
}

/* List Groups */
[data-bs-theme="dark"] .list-group-item {
  background-color: var(--card-bg) !important;
  border-color: var(--border-color) !important;
  color: var(--text-color) !important;
}

[data-bs-theme="dark"] .list-group-item.active {
  background-color: var(--primary) !important;
  border-color: var(--primary) !important;
  color: white !important;
}

[data-bs-theme="dark"] .list-group-item-action:hover {
  background-color: var(--gray-light) !important;
}

/* Badges */
[data-bs-theme="dark"] .badge {
  background-color: var(--gray-light) !important;
  color: var(--text-color) !important;
}

[data-bs-theme="dark"] .badge-primary {
  background-color: var(--primary) !important;
  color: white !important;
}

[data-bs-theme="dark"] .badge-success {
  background-color: var(--success) !important;
  color: white !important;
}

[data-bs-theme="dark"] .badge-info {
  background-color: var(--info) !important;
  color: white !important;
}

[data-bs-theme="dark"] .badge-warning {
  background-color: var(--warning) !important;
  color: white !important;
}

[data-bs-theme="dark"] .badge-danger {
  background-color: var(--danger) !important;
  color: white !important;
}

/* Progress Bars */
[data-bs-theme="dark"] .progress {
  background-color: var(--gray-light) !important;
}

/* Tooltips & Popovers */
[data-bs-theme="dark"] .tooltip-inner {
  background-color: var(--dark) !important;
  color: var(--text-color) !important;
}

[data-bs-theme="dark"] .popover {
  background-color: var(--card-bg) !important;
  border-color: var(--border-color) !important;
}

[data-bs-theme="dark"] .popover-header {
  background-color: var(--gray-light) !important;
  border-color: var(--border-color) !important;
}

[data-bs-theme="dark"] .popover-body {
  color: var(--text-color) !important;
}

/* Backend-specific dark mode styles */
[data-bs-theme="dark"] .layout-menu {
  background-color: var(--sidebar-bg) !important;
}

[data-bs-theme="dark"] .menu-link {
  color: var(--text-color) !important;
}

[data-bs-theme="dark"] .menu-item.open > .menu-toggle,
[data-bs-theme="dark"] .menu-item.active > .menu-link {
  background-color: rgba(255, 255, 255, 0.1) !important;
  color: var(--primary) !important;
}

[data-bs-theme="dark"] .app-brand-link {
  color: var(--text-color) !important;
}

[data-bs-theme="dark"] .layout-navbar {
  background-color: var(--header-bg) !important;
}

[data-bs-theme="dark"] .content-wrapper {
  background-color: var(--body-bg) !important;
}

[data-bs-theme="dark"] .menu-inner-shadow {
  background: linear-gradient(var(--sidebar-bg) 41%, rgba(30, 41, 59, 0.11) 95%, rgba(30, 41, 59, 0)) !important;
}

[data-bs-theme="dark"] .menu-vertical .menu-item .menu-link:hover {
  background-color: rgba(255, 255, 255, 0.1) !important;
}

[data-bs-theme="dark"] .menu-vertical .menu-item.open:not(.menu-item-closing) > .menu-toggle,
[data-bs-theme="dark"] .menu-vertical .menu-item.active > .menu-link {
  color: var(--primary) !important;
}

[data-bs-theme="dark"] .menu-vertical .menu-sub {
  background-color: transparent !important;
}

[data-bs-theme="dark"] .menu-vertical .menu-sub .menu-link {
  color: var(--text-muted) !important;
}

[data-bs-theme="dark"] .menu-vertical .menu-sub .menu-link:hover {
  color: var(--text-color) !important;
}

[data-bs-theme="dark"] .menu-vertical .menu-sub .active > .menu-link {
  color: var(--primary) !important;
}

/* Dashboard Cards */
[data-bs-theme="dark"] .card-equal-height {
  background-color: var(--card-bg) !important;
}

[data-bs-theme="dark"] .card-equal-height:hover {
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.3) !important;
}

[data-bs-theme="dark"] .card-icon {
  color: var(--primary) !important;
}

[data-bs-theme="dark"] .card-border-success:hover {
  background-color: rgba(var(--success), 0.2) !important;
}

[data-bs-theme="dark"] .card-border-info:hover {
  background-color: rgba(var(--info), 0.2) !important;
}

[data-bs-theme="dark"] .card-border-warning:hover {
  background-color: rgba(var(--warning), 0.2) !important;
}

[data-bs-theme="dark"] .card-border-danger:hover {
  background-color: rgba(var(--danger), 0.2) !important;
}

/* Charts */
[data-bs-theme="dark"] .apexcharts-canvas .apexcharts-title-text,
[data-bs-theme="dark"] .apexcharts-canvas .apexcharts-legend-text {
  color: var(--text-color) !important;
}

[data-bs-theme="dark"] .apexcharts-canvas .apexcharts-xaxis-label,
[data-bs-theme="dark"] .apexcharts-canvas .apexcharts-yaxis-label {
  fill: var(--text-muted) !important;
}

[data-bs-theme="dark"] .apexcharts-canvas .apexcharts-grid line {
  stroke: var(--border-color) !important;
}

[data-bs-theme="dark"] .apexcharts-canvas .apexcharts-tooltip {
  background-color: var(--card-bg) !important;
  border-color: var(--border-color) !important;
  box-shadow: 0 0.25rem 1rem rgba(0, 0, 0, 0.4) !important;
}

[data-bs-theme="dark"] .apexcharts-canvas .apexcharts-tooltip-title {
  background-color: var(--gray-light) !important;
  border-color: var(--border-color) !important;
}

/* Perfect Scrollbar */
[data-bs-theme="dark"] .ps__rail-y {
  background-color: transparent !important;
}

[data-bs-theme="dark"] .ps__thumb-y {
  background-color: var(--gray) !important;
}

/* Miscellaneous Backend Elements */
[data-bs-theme="dark"] .app-brand {
  background-color: var(--sidebar-bg) !important;
}

[data-bs-theme="dark"] .app-brand-logo {
  filter: brightness(1.2) !important;
}

[data-bs-theme="dark"] .layout-menu-toggle {
  background-color: var(--primary) !important;
  color: white !important;
}

[data-bs-theme="dark"] .navbar-nav .nav-item.dropdown .dropdown-menu {
  box-shadow: 0 0.25rem 1rem rgba(0, 0, 0, 0.4) !important;
}

[data-bs-theme="dark"] .navbar-nav .nav-link i {
  color: var(--text-color) !important;
}

[data-bs-theme="dark"] .search-input-wrapper .search-input {
  background-color: var(--card-bg) !important;
  color: var(--text-color) !important;
  border-color: var(--border-color) !important;
}

[data-bs-theme="dark"] .search-input-wrapper .search-input::placeholder {
  color: var(--text-muted) !important;
}

[data-bs-theme="dark"] .search-input-wrapper .search-input-clear {
  color: var(--text-muted) !important;
}
