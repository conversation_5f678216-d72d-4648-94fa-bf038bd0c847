<?php

require 'vendor/autoload.php';

use Dompdf\Dompdf;
use Dompdf\Options;

function isArabic($text) {
    // Detect Arabic characters
    return preg_match('/[\x{0600}-\x{06FF}\x{0750}-\x{077F}\x{08A0}-\x{08FF}\x{FB50}-\x{FDFF}\x{FE70}-\x{FEFF}\x{1EE00}-\x{1EEFF}]/u', $text);
}

function generatePDF($analysisResult, $seoScore, $url)
{
    // Detect if content is Arabic for RTL
    $isRTL = isArabic($analysisResult['SeoPreview']['title'] . $analysisResult['SeoPreview']['description']);

    // Initialize Dompdf
    $options = new Options();
    $options->setIsHtml5ParserEnabled(true);
    $options->setIsPhpEnabled(true);
    $options->set('defaultFont', 'DejaVu Sans');
    $dompdf = new Dompdf($options);

    // Header/Footer HTML
    $header = '<div style="width:100%;padding:5px 0;border-bottom:1px solid #3498db;text-align:center;font-size:13px;color:#2980b9;">WIDDX SEO Analyzer Report</div>';
    $footer = '<div style="width:100%;padding:5px 0;border-top:1px solid #3498db;text-align:center;font-size:11px;color:#888;">Generated by WIDDX.com | {PAGE_NUM} / {PAGE_COUNT}</div>';

    // Start HTML
    $html = '<!DOCTYPE html><html><head><meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>';
    $html .= '<style>
        @page { margin: 30px 20px; }
        body { font-family: "DejaVu Sans", Arial, sans-serif; line-height: 1.5; color: #222; margin: 0; padding: 0;'.($isRTL ? 'direction:rtl;text-align:right;' : '').' }
        .cover { display: flex; flex-direction: column; justify-content: center; align-items: center; height: 90vh; background: #f8f9fa; }
        .cover-logo { width: 90px; margin-bottom: 20px; }
        .cover-title { font-size: 32px; color: #2c3e50; font-weight: bold; margin-bottom: 10px; }
        .cover-desc { font-size: 18px; color: #555; margin-bottom: 20px; }
        .cover-date { font-size: 14px; color: #888; }
        .score { font-size: 22px; font-weight: bold; color: #27ae60; margin: 20px 0; }
        h1 { color: #2c3e50; border-bottom: 1px solid #3498db; padding-bottom: 5px; margin-top: 0; font-size: 22px; }
        h2 { color: #2980b9; margin-top: 5px; font-size: 18px; }
        .section { margin-bottom: 18px; }
        .subsection { margin-left: 5px; }
        .status-success { color: #27ae60; }
        .status-warning { color: #f39c12; }
        .status-error { color: #c0392b; }
        table { width: 100%; border-collapse: collapse; margin-top: 5px; font-size: 11px; }
        th, td { border: 1px solid #bdc3c7; padding: 4px; text-align: left; }
        th { background-color: #ecf0f1; }
        .card { border: 1px solid #ddd; border-radius: 0.25rem; box-shadow: 0 0.125rem 0.25rem rgba(0,0,0,.075); margin-bottom: 8px; page-break-inside: avoid; }
        .card-header { background-color: #f7f7f7; padding: 0.5rem 1rem; border-bottom: 1px solid #ddd; font-weight: bold; font-size: 13px; }
        .card-body { padding: 1rem; font-size: 13px; }
        .alert { margin-bottom: 0.5rem; }
        .text-danger { color: #dc3545; }
        .text-success { color: #28a745; }
        .text-warning { color: #ffc107; }
        .summary-box { background: #eafaf1; border: 1px solid #b2f2d7; border-radius: 6px; padding: 12px; margin-bottom: 12px; }
        .recommend-box { background: #f9f6e7; border: 1px solid #ffe082; border-radius: 6px; padding: 12px; margin-bottom: 12px; }
    </style></head><body>';

    // Header
    $html .= $header;

    // Cover Page
    $html .= '<div class="cover">
        <img src="https://widdx.com/templates/widdx/frontend/assets/img/logo.png" class="cover-logo" alt="Logo" />
        <div class="cover-title">'.htmlspecialchars($analysisResult['SeoPreview']['title'], ENT_QUOTES, 'UTF-8').'</div>
        <div class="cover-desc">'.htmlspecialchars($analysisResult['SeoPreview']['description'], ENT_QUOTES, 'UTF-8').'</div>
        <div class="score">SEO Score: '.($seoScore['scorePercentage'] ?? '--').'%</div>
        <div class="cover-date">'.date('Y-m-d H:i').'</div>
    </div><div style="page-break-after:always;"></div>';

    // Table of Contents
    $html .= '<h1>'.($isRTL ? 'فهرس المحتويات' : 'Table of Contents').'</h1><ul style="font-size:15px;">
        <li><a href="#summary">'.($isRTL ? 'الملخص والتوصيات' : 'Summary & Recommendations').'</a></li>
        <li><a href="#basic-seo">'.($isRTL ? 'تحليل السيو الأساسي' : 'Basic SEO Analysis').'</a></li>
        <li><a href="#advanced-seo">'.($isRTL ? 'تحليل السيو المتقدم' : 'Advanced SEO Analysis').'</a></li>
        <li><a href="#performance">'.($isRTL ? 'تحليل الأداء' : 'Performance Analysis').'</a></li>
        <li><a href="#security">'.($isRTL ? 'تحليل الأمان' : 'Security Analysis').'</a></li>
    </ul><div style="page-break-after:always;"></div>';

    // Summary & Recommendations
    $html .= '<div class="section" id="summary">
        <h1>'.($isRTL ? 'الملخص والتوصيات' : 'Summary & Recommendations').'</h1>';
    // Summary box
    $html .= '<div class="summary-box">'.($isRTL ? 'نسبة السيو الكلية: ' : 'Overall SEO Score: ').($seoScore['scorePercentage'] ?? '--').'%<br>';
    $html .= ($isRTL ? 'عدد الفحوصات: ' : 'Total Checks: ').($seoScore['totalChecks'] ?? '--').'<br>';
    $html .= ($isRTL ? 'النتائج الجيدة: ' : 'Passed Checks: ').($seoScore['passedChecks'] ?? '--').'<br>';
    $html .= ($isRTL ? 'بحاجة لتحسين: ' : 'Improvements Needed: ').($seoScore['improveChecks'] ?? '--').'<br>';
    $html .= ($isRTL ? 'مشاكل حرجة: ' : 'Critical Issues: ').($seoScore['failedChecks'] ?? '--').'</div>';
    // Recommendations
    $html .= '<div class="recommend-box">'.($isRTL ? 'أهم التوصيات:' : 'Top Recommendations:').'<ul>';
    if (!empty($analysisResult['basicSeo']['h1Tags']) && count($analysisResult['basicSeo']['h1Tags']) != 1) {
        $html .= '<li>'.($isRTL ? 'يجب أن يكون هناك عنوان H1 واحد فقط في الصفحة.' : 'There should be only one H1 tag on the page.').'</li>';
    }
    if (empty($analysisResult['basicSeo']['metaDescription'])) {
        $html .= '<li>'.($isRTL ? 'أضف وصف ميتا للصفحة.' : 'Add a meta description to the page.').'</li>';
    }
    if (!empty($analysisResult['basicSeo']['imagesWithoutAlt'])) {
        $html .= '<li>'.($isRTL ? 'أضف نص بديل لجميع الصور.' : 'Add alt text to all images.').'</li>';
    }
    if (!empty($analysisResult['basicSeo']['brokenLinks'])) {
        $html .= '<li>'.($isRTL ? 'يوجد روابط مكسورة يجب إصلاحها.' : 'There are broken links that should be fixed.').'</li>';
    }
    if (!empty($analysisResult['basicSeo']['brokenImages'])) {
        $html .= '<li>'.($isRTL ? 'يوجد صور مكسورة يجب إصلاحها.' : 'There are broken images that should be fixed.').'</li>';
    }
    $html .= '</ul></div></div><div style="page-break-after:always;"></div>';

    // Basic SEO Analysis
    $html .= '<div class="section" id="basic-seo"><h1>'.($isRTL ? 'تحليل السيو الأساسي' : 'Basic SEO Analysis').'</h1>';
    $html .= '<table><tr><th>'.($isRTL ? 'العنصر' : 'Element').'</th><th>'.($isRTL ? 'القيمة' : 'Value').'</th></tr>';
    foreach ($analysisResult['basicSeo'] as $k => $v) {
        $html .= '<tr><td>'.htmlspecialchars($k, ENT_QUOTES, 'UTF-8').'</td><td>';
        if (is_array($v)) {
            // If array is associative or multidimensional, use json_encode
            if (array_keys($v) !== range(0, count($v) - 1)) {
                $html .= htmlspecialchars(json_encode($v, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT), ENT_QUOTES, 'UTF-8');
            } else {
                $html .= htmlspecialchars(implode(', ', $v), ENT_QUOTES, 'UTF-8');
            }
        } else {
            $html .= htmlspecialchars((string)$v, ENT_QUOTES, 'UTF-8');
        }
        $html .= '</td></tr>';
    }
    $html .= '</table></div><div style="page-break-after:always;"></div>';

    // Advanced SEO Analysis
    $html .= '<div class="section" id="advanced-seo"><h1>'.($isRTL ? 'تحليل السيو المتقدم' : 'Advanced SEO Analysis').'</h1>';
    $html .= '<table><tr><th>'.($isRTL ? 'العنصر' : 'Element').'</th><th>'.($isRTL ? 'القيمة' : 'Value').'</th></tr>';
    foreach ($analysisResult['advancedSeo'] as $k => $v) {
        $html .= '<tr><td>'.htmlspecialchars($k, ENT_QUOTES, 'UTF-8').'</td><td>';
        if (is_array($v)) {
            if (isset($v['value'])) {
                if (is_array($v['value'])) {
                    $html .= htmlspecialchars(json_encode($v['value'], JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT), ENT_QUOTES, 'UTF-8');
                } else {
                    $html .= htmlspecialchars((string)$v['value'], ENT_QUOTES, 'UTF-8');
                }
            } else {
                $html .= htmlspecialchars(json_encode($v, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT), ENT_QUOTES, 'UTF-8');
            }
        } else {
            $html .= htmlspecialchars((string)$v, ENT_QUOTES, 'UTF-8');
        }
        $html .= '</td></tr>';
    }
    $html .= '</table></div><div style="page-break-after:always;"></div>';

    // Performance Analysis
    $html .= '<div class="section" id="performance"><h1>'.($isRTL ? 'تحليل الأداء' : 'Performance Analysis').'</h1>';
    $html .= '<table><tr><th>'.($isRTL ? 'العنصر' : 'Element').'</th><th>'.($isRTL ? 'القيمة' : 'Value').'</th></tr>';
    foreach ($analysisResult['performance'] as $k => $v) {
        $html .= '<tr><td>'.htmlspecialchars($k, ENT_QUOTES, 'UTF-8').'</td><td>';
        if (is_array($v)) {
            $html .= htmlspecialchars(json_encode($v, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT), ENT_QUOTES, 'UTF-8');
        } else {
            $html .= htmlspecialchars((string)$v, ENT_QUOTES, 'UTF-8');
        }
        $html .= '</td></tr>';
    }
    $html .= '</table></div><div style="page-break-after:always;"></div>';

    // Security Analysis
    $html .= '<div class="section" id="security"><h1>'.($isRTL ? 'تحليل الأمان' : 'Security Analysis').'</h1>';
    $html .= '<table><tr><th>'.($isRTL ? 'العنصر' : 'Element').'</th><th>'.($isRTL ? 'القيمة' : 'Value').'</th></tr>';
    foreach ($analysisResult['security'] as $k => $v) {
        $html .= '<tr><td>'.htmlspecialchars($k, ENT_QUOTES, 'UTF-8').'</td><td>';
        if (is_array($v)) {
            $html .= htmlspecialchars(json_encode($v, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT), ENT_QUOTES, 'UTF-8');
        } else {
            $html .= htmlspecialchars((string)$v, ENT_QUOTES, 'UTF-8');
        }
        $html .= '</td></tr>';
    }
    $html .= '</table></div>';

    // Footer
    $html .= $footer;

    $html .= '</body></html>';

    // Load HTML into Dompdf
    $dompdf->loadHtml($html, 'UTF-8');

    // Set size and orientation
    $dompdf->setPaper('A4', 'portrait');

    // Render PDF (will be stored in memory)
    $dompdf->render();

    // Return PDF content
    return $dompdf->output();
}

// Set the default character encoding for PHP
mb_internal_encoding('UTF-8');

// Set the default character encoding for multibyte PHP functions
mb_http_output('UTF-8');

// Set the default character encoding for HTTP headers
header('Content-Type: text/html; charset=UTF-8');
?>