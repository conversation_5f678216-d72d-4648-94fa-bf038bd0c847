# دليل التثبيت السريع - Lahza.io Payment Gateway

## 🚀 التثبيت السريع (5 دقائق)

### الخطوة 1: رفع الملفات
```bash
# رفع الملفات إلى مجلد WHMCS
/modules/gateways/lahza.php
/modules/gateways/lahza_config.php
/modules/gateways/callback/lahza.php
/modules/gateways/test_lahza.php
/modules/gateways/README_LAHZA.md
/modules/gateways/CHANGELOG_LAHZA.md
/modules/gateways/INSTALL_LAHZA.md
```

### الخطوة 2: تفعيل البوابة
1. اذهب إلى **Setup > Payments > Payment Gateways**
2. ابحث عن **Lahza.io Payment Gateway**
3. اضغط **Activate**

### الخطوة 3: التكوين الأساسي
```
Public Key: pk_test_your_public_key_here
Secret Key: sk_test_your_secret_key_here
Test Mode: ✅ Yes (للاختبار)
Payment Method: Popup (موصى به)
```

### الخطوة 4: اختبار البوابة
1. اذهب إلى: `your-domain.com/modules/gateways/test_lahza.php?test=lahza`
2. تأكد من نجاح جميع الاختبارات
3. اختبر معاملة صغيرة

### الخطوة 5: الانتقال للوضع المباشر
```
Public Key: pk_live_your_live_public_key
Secret Key: sk_live_your_live_secret_key
Test Mode: ❌ No
```

---

## 🔧 التكوين المتقدم

### إعدادات Webhook
1. في لوحة تحكم Lahza.io:
   ```
   Webhook URL: https://your-domain.com/modules/gateways/callback/lahza.php
   Events: charge.success, refund.processed
   ```

2. في إعدادات البوابة:
   ```
   Webhook URL: (تلقائي أو مخصص)
   IP Whitelist: *************,**************
   ```

### تخصيص التصميم
```css
/* إضافة في Custom CSS */
.lahza-payment-container {
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.payment-header {
    background: linear-gradient(135deg, #your-color-1, #your-color-2);
}
```

### إعدادات متقدمة
```
Payment Channels: card,bank,mobile_money
Enable Logging: ✅ Yes (للتطوير)
Show Card Type: ✅ Yes
```

---

## 🧪 الاختبار

### بيانات الاختبار
```
Test Card: ****************
CVV: 123
Expiry: 12/28
Name: Test User
```

### اختبار المعاملات
1. إنشاء فاتورة اختبار
2. اختيار Lahza.io كطريقة دفع
3. إكمال الدفع بالبيانات التجريبية
4. التحقق من تسجيل المعاملة

### اختبار Webhooks
1. تفعيل السجلات المفصلة
2. إجراء معاملة اختبار
3. مراجعة سجلات Gateway Log
4. التأكد من استلام Webhook

---

## 🔍 استكشاف الأخطاء

### مشاكل شائعة وحلولها

#### 1. "Module Not Activated"
```
الحل: تأكد من تفعيل البوابة في WHMCS Admin
المسار: Setup > Payments > Payment Gateways
```

#### 2. "Invalid Public Key format"
```
الحل: تأكد من صيغة المفتاح
الصحيح: pk_test_xxxxxxxxxx أو pk_live_xxxxxxxxxx
```

#### 3. "Currency not supported"
```
الحل: استخدم العملات المدعومة فقط
المدعوم: ILS, USD, JOD
```

#### 4. "Webhook signature invalid"
```
الحل: 
1. تحقق من Secret Key
2. تأكد من تكوين Webhook URL
3. راجع IP Whitelist
```

#### 5. "Payment verification failed"
```
الحل:
1. تحقق من اتصال الإنترنت
2. تأكد من صحة API Keys
3. راجع سجلات الأخطاء
```

### تفعيل السجلات المفصلة
```
1. Enable Logging: Yes
2. مراجعة: Utilities > Logs > Gateway Log
3. البحث عن: [Lahza.io]
```

---

## 📋 قائمة التحقق

### قبل الانتقال للوضع المباشر
- [ ] اختبار المعاملات الناجحة
- [ ] اختبار المعاملات الفاشلة
- [ ] اختبار Webhooks
- [ ] مراجعة السجلات
- [ ] اختبار العملات المختلفة
- [ ] اختبار الأجهزة المحمولة
- [ ] تأكيد إعدادات الأمان

### بعد الانتقال للوضع المباشر
- [ ] تغيير API Keys للوضع المباشر
- [ ] إيقاف Test Mode
- [ ] تحديث Webhook URL
- [ ] مراقبة المعاملات الأولى
- [ ] إعداد التنبيهات
- [ ] تدريب فريق الدعم

---

## 🆘 الحصول على المساعدة

### الموارد المتاحة
1. **التوثيق الكامل**: README_LAHZA.md
2. **سجل التغييرات**: CHANGELOG_LAHZA.md
3. **ملف الاختبار**: test_lahza.php?test=lahza
4. **التكوين المتقدم**: lahza_config.php

### طلب الدعم
```
البريد الإلكتروني: <EMAIL>
الموضوع: [Lahza Gateway] وصف المشكلة
المعلومات المطلوبة:
- إصدار WHMCS
- إصدار PHP
- رسالة الخطأ
- خطوات إعادة الإنتاج
```

### معلومات مفيدة للدعم
```bash
# معلومات النظام
PHP Version: <?php echo PHP_VERSION; ?>
WHMCS Version: [من لوحة التحكم]
Gateway Version: 3.0.0

# سجلات مفيدة
Gateway Log: Utilities > Logs > Gateway Log
Activity Log: Utilities > Logs > Activity Log
System Log: Utilities > System > PHP Error Log
```

---

## 🔄 التحديثات

### التحقق من التحديثات
1. مراجعة CHANGELOG_LAHZA.md
2. نسخ احتياطي من الملفات الحالية
3. تحديث الملفات الجديدة
4. اختبار الوظائف الأساسية

### الإشعارات
- تابع التحديثات عبر GitHub
- اشترك في النشرة الإخبارية
- راجع منتديات WHMCS

---

## ✅ تم التثبيت بنجاح!

إذا وصلت إلى هنا، فقد تم تثبيت بوابة Lahza.io بنجاح! 🎉

**الخطوات التالية:**
1. اختبر معاملة صغيرة
2. راجع السجلات
3. انتقل للوضع المباشر عند الاستعداد
4. استمتع بالمعاملات الآمنة والسريعة!

---

*© 2024 WIDDX. جميع الحقوق محفوظة.*
