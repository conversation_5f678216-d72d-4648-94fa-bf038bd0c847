/* ===== UNIFIED CSS FILE - ALL STYLES ===== */

/* ===== CSS RESET & BASE STYLES ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    font-size: 16px;
}

body {
    font-family: 'Inter', sans-serif;
    line-height: 1.6;
    color: var(--text-primary);
    background: transparent; /* Let the modern background show through */
    overflow-x: hidden;
    transition: color 0.3s ease;
    position: relative;
}

/* ===== CSS VARIABLES ===== */
:root {
    /* Dark Theme (Default) */
    --primary-color: #010815;
    --secondary-color: #6c5bb9;
    --accent-color: #c0a5d5;
    --text-primary: #ffffff;
    --text-secondary: rgba(255, 255, 255, 0.8);
    --text-muted: rgba(255, 255, 255, 0.6);
    --background-primary: #010815;
    --background-secondary: #0f1419;
    --background-tertiary: rgba(255, 255, 255, 0.05);
    --border-color: rgba(192, 165, 213, 0.1);
    --card-background: rgba(255, 255, 255, 0.05);
    --navbar-background: rgba(1, 8, 21, 0.95);
    --footer-background: #000510;

    /* Common Variables */
    --white: #ffffff;
    --gray-light: #f8f9fa;
    --gray-dark: #2c3e50;
    --transition: all 0.3s ease;
    --border-radius: 12px;
    --box-shadow: 0 10px 30px rgba(108, 91, 185, 0.1);
    --container-max-width: 1200px;
    --section-padding: 80px 0;
}

/* Dark theme is default, so no explicit block is needed */

/* Light Theme */
[data-theme="light"] {
    --primary-color: #ffffff;
    --secondary-color: #6c5bb9;
    --accent-color: #8b5bb9;
    --text-primary: #1a1a1a;
    --text-secondary: rgba(26, 26, 26, 0.8);
    --text-muted: rgba(26, 26, 26, 0.6);
    --background-primary: #ffffff;
    --background-secondary: #f8f9fa;
    --background-tertiary: rgba(108, 91, 185, 0.05);
    --border-color: rgba(108, 91, 185, 0.2);
    --card-background: rgba(108, 91, 185, 0.05);
    --navbar-background: rgba(255, 255, 255, 0.95);
    --footer-background: #f1f3f4;
    --box-shadow: 0 10px 30px rgba(108, 91, 185, 0.15);
}

/* Smooth theme transitions */
.theme-transitioning,
.theme-transitioning * {
    transition: background-color 0.3s ease,
                color 0.3s ease,
                border-color 0.3s ease,
                box-shadow 0.3s ease !important;
}

/* Language Toggle Styles */
.language-toggle {
    background: transparent;
    border: 2px solid var(--accent-color);
    color: var(--accent-color);
    padding: 8px 12px;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 0.9rem;
    margin-left: 1rem;
    margin-right: 1rem;
}

.language-toggle:hover {
    background: var(--accent-color);
    color: var(--primary-color);
    transform: translateY(-2px);
}

.language-toggle i {
    font-size: 1rem;
}

.language-toggle span {
    font-weight: 500;
    min-width: 40px;
    text-align: center;
}

/* ===== UTILITY CLASSES ===== */
.container {
    max-width: var(--container-max-width);
    margin: 0 auto;
    padding: 0 20px;
}

.btn {
    display: inline-block;
    padding: 14px 28px;
    border-radius: var(--border-radius);
    text-decoration: none;
    font-weight: 600;
    text-align: center;
    transition: var(--transition);
    cursor: pointer;
    border: 2px solid transparent;
    font-size: 16px;
    position: relative;
    overflow: hidden;
}

.btn-primary {
    background: linear-gradient(135deg, var(--secondary-color), var(--accent-color));
    color: var(--white);
    border-color: var(--secondary-color);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 15px 35px rgba(108, 91, 185, 0.3);
}

.btn-secondary {
    background: transparent;
    color: var(--accent-color);
    border-color: var(--accent-color);
}

.btn-secondary:hover {
    background: var(--accent-color);
    color: var(--primary-color);
    transform: translateY(-2px);
}

.btn-outline {
    background: transparent;
    color: var(--white);
    border-color: var(--white);
}

.btn-outline:hover {
    background: var(--white);
    color: var(--primary-color);
    transform: translateY(-2px);
}

/* ===== PRELOADER ===== */
#preloader {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--primary-color);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10000;
}

.preloader-content {
    text-align: center;
}

.logo-animation h1 {
    font-size: 3rem;
    font-weight: 700;
    color: var(--accent-color);
    margin-bottom: 20px;
    letter-spacing: 3px;
}

.loading-bar {
    width: 200px;
    height: 4px;
    background: rgba(192, 165, 213, 0.2);
    border-radius: 2px;
    overflow: hidden;
    position: relative;
}

.loading-bar::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, var(--secondary-color), var(--accent-color));
    animation: loading 2s ease-in-out infinite;
}

@keyframes loading {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* ===== NAVIGATION ===== */
.navbar {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    background: var(--navbar-background);
    backdrop-filter: blur(10px);
    z-index: 1000;
    transition: var(--transition);
    padding: 15px 0;
    border-bottom: 1px solid var(--border-color);
}

.navbar.scrolled {
    padding: 10px 0;
    background: var(--navbar-background);
    box-shadow: var(--box-shadow);
}

.nav-container {
    max-width: var(--container-max-width);
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-logo a {
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--accent-color);
    text-decoration: none;
    letter-spacing: 2px;
}

.nav-menu {
    display: flex;
    gap: 30px;
    align-items: center;
}

.nav-link {
    color: var(--text-primary);
    text-decoration: none;
    font-weight: 500;
    transition: var(--transition);
    position: relative;
    padding: 8px 0;
}

.nav-link:hover,
.nav-link.active {
    color: var(--accent-color);
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--accent-color);
    transition: width 0.3s ease;
}

.nav-link:hover::after,
.nav-link.active::after {
    width: 100%;
}

.client-area {
    background: linear-gradient(135deg, var(--secondary-color), var(--accent-color));
    padding: 8px 16px !important;
    border-radius: 20px;
    color: var(--white) !important;
}

.client-area::after {
    display: none;
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
    gap: 4px;
}

.hamburger span {
    width: 25px;
    height: 3px;
    background: var(--text-primary);
    transition: var(--transition);
    border-radius: 2px;
}

/* ===== THEME TOGGLE ===== */
.theme-toggle {
    background: transparent;
    border: 2px solid var(--accent-color);
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--accent-color);
    cursor: pointer;
    transition: var(--transition);
    margin-left: 15px;
}

.theme-toggle:hover {
    background: var(--accent-color);
    color: var(--white);
    transform: scale(1.1);
}

.theme-toggle i {
    font-size: 1rem;
    transition: var(--transition);
}

/* ===== MODERN GRADIENT BACKGROUND SYSTEM ===== */
.modern-gradient-background {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    overflow: hidden;
    background: var(--background-primary);
    transition: all 0.3s ease;
}

/* Base gradient layers */
.gradient-layer-1 {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at 20% 30%, rgba(15, 20, 25, 0.9) 0%, transparent 50%),
                radial-gradient(circle at 80% 70%, rgba(108, 91, 185, 0.15) 0%, transparent 60%),
                radial-gradient(circle at 40% 80%, rgba(192, 165, 213, 0.1) 0%, transparent 50%);
    animation: gradientShift1 20s ease-in-out infinite;
}

.gradient-layer-2 {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at 60% 20%, rgba(44, 62, 80, 0.3) 0%, transparent 40%),
                radial-gradient(circle at 10% 60%, rgba(108, 91, 185, 0.08) 0%, transparent 70%),
                radial-gradient(circle at 90% 40%, rgba(192, 165, 213, 0.12) 0%, transparent 55%);
    animation: gradientShift2 25s ease-in-out infinite reverse;
}

.gradient-layer-3 {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(1, 8, 21, 0.8) 0%, rgba(15, 20, 25, 0.6) 50%, rgba(44, 62, 80, 0.4) 100%),
                radial-gradient(ellipse at 70% 10%, rgba(108, 91, 185, 0.1) 0%, transparent 50%);
    animation: gradientShift3 30s ease-in-out infinite;
}

/* Animated gradient spheres */
.gradient-sphere {
    position: absolute;
    border-radius: 50%;
    filter: blur(40px);
    opacity: 0.6;
    animation: floatSphere 15s ease-in-out infinite;
}

.gradient-sphere-1 {
    width: 300px;
    height: 300px;
    background: radial-gradient(circle, rgba(108, 91, 185, 0.3) 0%, rgba(108, 91, 185, 0.1) 50%, transparent 100%);
    top: 10%;
    left: 15%;
    animation-delay: 0s;
    animation-duration: 20s;
}

.gradient-sphere-2 {
    width: 400px;
    height: 400px;
    background: radial-gradient(circle, rgba(192, 165, 213, 0.25) 0%, rgba(192, 165, 213, 0.08) 50%, transparent 100%);
    top: 60%;
    right: 10%;
    animation-delay: -5s;
    animation-duration: 25s;
}

.gradient-sphere-3 {
    width: 250px;
    height: 250px;
    background: radial-gradient(circle, rgba(44, 62, 80, 0.4) 0%, rgba(44, 62, 80, 0.15) 50%, transparent 100%);
    bottom: 20%;
    left: 60%;
    animation-delay: -10s;
    animation-duration: 18s;
}

.gradient-sphere-4 {
    width: 350px;
    height: 350px;
    background: radial-gradient(circle, rgba(108, 91, 185, 0.2) 0%, rgba(192, 165, 213, 0.1) 50%, transparent 100%);
    top: 30%;
    right: 40%;
    animation-delay: -15s;
    animation-duration: 22s;
}

/* Smooth wave elements */
.wave-element {
    position: absolute;
    width: 100%;
    height: 100%;
    opacity: 0.3;
}

.wave-element::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 200%;
    height: 200%;
    background: conic-gradient(from 0deg at 50% 50%,
                              rgba(108, 91, 185, 0.1) 0deg,
                              transparent 60deg,
                              rgba(192, 165, 213, 0.08) 120deg,
                              transparent 180deg,
                              rgba(108, 91, 185, 0.05) 240deg,
                              transparent 300deg,
                              rgba(192, 165, 213, 0.1) 360deg);
    animation: rotateWave 40s linear infinite;
    filter: blur(60px);
}

.wave-element::after {
    content: '';
    position: absolute;
    top: 20%;
    left: 20%;
    width: 160%;
    height: 160%;
    background: conic-gradient(from 180deg at 50% 50%,
                              rgba(192, 165, 213, 0.08) 0deg,
                              transparent 90deg,
                              rgba(108, 91, 185, 0.06) 180deg,
                              transparent 270deg,
                              rgba(192, 165, 213, 0.08) 360deg);
    animation: rotateWave 35s linear infinite reverse;
    filter: blur(80px);
}

/* Particle system */
.particle-system {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
}

.particle {
    position: absolute;
    width: 2px;
    height: 2px;
    background: rgba(192, 165, 213, 0.6);
    border-radius: 50%;
    animation: floatParticle linear infinite;
}

/* Noise texture overlay */
.noise-texture {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0.03;
    background-image:
        radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.1) 1px, transparent 1px),
        radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.08) 1px, transparent 1px),
        radial-gradient(circle at 50% 10%, rgba(255, 255, 255, 0.05) 1px, transparent 1px),
        radial-gradient(circle at 10% 90%, rgba(255, 255, 255, 0.06) 1px, transparent 1px);
    background-size: 50px 50px, 80px 80px, 120px 120px, 90px 90px;
    animation: noiseShift 8s ease-in-out infinite;
}

/* ===== HERO SECTION ===== */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
    background: transparent;
    transition: all 0.3s ease;
    padding: 120px 0 80px;
}

/* ===== DARK THEME BACKGROUND ADJUSTMENTS ===== */
[data-theme="dark"] .modern-gradient-background {
    background: var(--background-primary);
}

[data-theme="dark"] .gradient-layer-1 {
    background: radial-gradient(circle at 20% 30%, rgba(15, 20, 25, 0.9) 0%, transparent 50%),
                radial-gradient(circle at 80% 70%, rgba(108, 91, 185, 0.15) 0%, transparent 60%),
                radial-gradient(circle at 40% 80%, rgba(192, 165, 213, 0.1) 0%, transparent 50%);
}

[data-theme="dark"] .gradient-layer-2 {
    background: radial-gradient(circle at 60% 20%, rgba(44, 62, 80, 0.3) 0%, transparent 40%),
                radial-gradient(circle at 10% 60%, rgba(108, 91, 185, 0.08) 0%, transparent 70%),
                radial-gradient(circle at 90% 40%, rgba(192, 165, 213, 0.12) 0%, transparent 55%);
}

[data-theme="dark"] .gradient-layer-3 {
    background: linear-gradient(135deg, rgba(1, 8, 21, 0.8) 0%, rgba(15, 20, 25, 0.6) 50%, rgba(44, 62, 80, 0.4) 100%),
                radial-gradient(ellipse at 70% 10%, rgba(108, 91, 185, 0.1) 0%, transparent 50%);
}

[data-theme="dark"] .gradient-sphere-1 {
    background: radial-gradient(circle, rgba(108, 91, 185, 0.3) 0%, rgba(108, 91, 185, 0.1) 50%, transparent 100%);
}

[data-theme="dark"] .gradient-sphere-2 {
    background: radial-gradient(circle, rgba(192, 165, 213, 0.25) 0%, rgba(192, 165, 213, 0.08) 50%, transparent 100%);
}

[data-theme="dark"] .gradient-sphere-3 {
    background: radial-gradient(circle, rgba(44, 62, 80, 0.4) 0%, rgba(44, 62, 80, 0.15) 50%, transparent 100%);
}

[data-theme="dark"] .gradient-sphere-4 {
    background: radial-gradient(circle, rgba(108, 91, 185, 0.2) 0%, rgba(192, 165, 213, 0.1) 50%, transparent 100%);
}

[data-theme="dark"] .particle {
    background: rgba(192, 165, 213, 0.6);
}

/* ===== LIGHT THEME BACKGROUND ADJUSTMENTS ===== */
[data-theme="light"] .modern-gradient-background {
    background: var(--background-primary);
}

[data-theme="light"] .gradient-layer-1 {
    background: radial-gradient(circle at 20% 30%, rgba(248, 249, 250, 0.9) 0%, transparent 50%),
                radial-gradient(circle at 80% 70%, rgba(108, 91, 185, 0.08) 0%, transparent 60%),
                radial-gradient(circle at 40% 80%, rgba(192, 165, 213, 0.06) 0%, transparent 50%);
}

[data-theme="light"] .gradient-layer-2 {
    background: radial-gradient(circle at 60% 20%, rgba(241, 243, 244, 0.8) 0%, transparent 40%),
                radial-gradient(circle at 10% 60%, rgba(108, 91, 185, 0.05) 0%, transparent 70%),
                radial-gradient(circle at 90% 40%, rgba(192, 165, 213, 0.07) 0%, transparent 55%);
}

[data-theme="light"] .gradient-layer-3 {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 249, 250, 0.7) 50%, rgba(241, 243, 244, 0.5) 100%),
                radial-gradient(ellipse at 70% 10%, rgba(108, 91, 185, 0.06) 0%, transparent 50%);
}

[data-theme="light"] .gradient-sphere-1 {
    background: radial-gradient(circle, rgba(108, 91, 185, 0.15) 0%, rgba(108, 91, 185, 0.05) 50%, transparent 100%);
}

[data-theme="light"] .gradient-sphere-2 {
    background: radial-gradient(circle, rgba(192, 165, 213, 0.12) 0%, rgba(192, 165, 213, 0.04) 50%, transparent 100%);
}

[data-theme="light"] .gradient-sphere-3 {
    background: radial-gradient(circle, rgba(139, 91, 185, 0.2) 0%, rgba(139, 91, 185, 0.08) 50%, transparent 100%);
}

[data-theme="light"] .gradient-sphere-4 {
    background: radial-gradient(circle, rgba(108, 91, 185, 0.15) 0%, rgba(192, 165, 213, 0.06) 50%, transparent 100%);
}

[data-theme="light"] .particle {
    background: rgba(108, 91, 185, 0.4);
}

[data-theme="light"] .wave-element::before {
    background: conic-gradient(from 0deg at 50% 50%,
                              rgba(108, 91, 185, 0.06) 0deg,
                              transparent 60deg,
                              rgba(192, 165, 213, 0.04) 120deg,
                              transparent 180deg,
                              rgba(108, 91, 185, 0.03) 240deg,
                              transparent 300deg,
                              rgba(192, 165, 213, 0.06) 360deg);
}

[data-theme="light"] .wave-element::after {
    background: conic-gradient(from 180deg at 50% 50%,
                              rgba(192, 165, 213, 0.04) 0deg,
                              transparent 90deg,
                              rgba(108, 91, 185, 0.03) 180deg,
                              transparent 270deg,
                              rgba(192, 165, 213, 0.04) 360deg);
}

[data-theme="light"] .noise-texture {
    background-image:
        radial-gradient(circle at 25% 25%, rgba(108, 91, 185, 0.08) 1px, transparent 1px),
        radial-gradient(circle at 75% 75%, rgba(108, 91, 185, 0.06) 1px, transparent 1px),
        radial-gradient(circle at 50% 10%, rgba(108, 91, 185, 0.04) 1px, transparent 1px),
        radial-gradient(circle at 10% 90%, rgba(108, 91, 185, 0.05) 1px, transparent 1px);
}

[data-theme="light"] .wave-element::before {
    background: conic-gradient(from 0deg at 50% 50%,
                              rgba(108, 91, 185, 0.06) 0deg,
                              transparent 60deg,
                              rgba(192, 165, 213, 0.04) 120deg,
                              transparent 180deg,
                              rgba(108, 91, 185, 0.03) 240deg,
                              transparent 300deg,
                              rgba(192, 165, 213, 0.05) 360deg);
}

[data-theme="light"] .wave-element::after {
    background: conic-gradient(from 180deg at 50% 50%,
                              rgba(192, 165, 213, 0.04) 0deg,
                              transparent 90deg,
                              rgba(108, 91, 185, 0.03) 180deg,
                              transparent 270deg,
                              rgba(192, 165, 213, 0.04) 360deg);
}

[data-theme="light"] .particle {
    background: rgba(108, 91, 185, 0.4);
}

[data-theme="light"] .noise-texture {
    opacity: 0.02;
    background-image:
        radial-gradient(circle at 25% 25%, rgba(108, 91, 185, 0.08) 1px, transparent 1px),
        radial-gradient(circle at 75% 75%, rgba(108, 91, 185, 0.06) 1px, transparent 1px),
        radial-gradient(circle at 50% 10%, rgba(192, 165, 213, 0.05) 1px, transparent 1px),
        radial-gradient(circle at 10% 90%, rgba(192, 165, 213, 0.04) 1px, transparent 1px);
}

/* Light theme gradient animations */
[data-theme="light"] .gradient-layer-1 {
    animation: lightGradientShift1 20s ease-in-out infinite;
}

[data-theme="light"] .gradient-layer-2 {
    animation: lightGradientShift2 25s ease-in-out infinite reverse;
}

[data-theme="light"] .gradient-layer-3 {
    animation: lightGradientShift3 30s ease-in-out infinite;
}

/* Light theme specific animations */
@keyframes lightGradientShift1 {
    0%, 100% {
        background: radial-gradient(circle at 20% 30%, rgba(248, 249, 250, 0.9) 0%, transparent 50%),
                    radial-gradient(circle at 80% 70%, rgba(108, 91, 185, 0.08) 0%, transparent 60%),
                    radial-gradient(circle at 40% 80%, rgba(192, 165, 213, 0.06) 0%, transparent 50%);
    }
    50% {
        background: radial-gradient(circle at 40% 10%, rgba(248, 249, 250, 0.85) 0%, transparent 52%),
                    radial-gradient(circle at 60% 90%, rgba(108, 91, 185, 0.10) 0%, transparent 62%),
                    radial-gradient(circle at 30% 60%, rgba(192, 165, 213, 0.08) 0%, transparent 52%);
    }
}

@keyframes lightGradientShift2 {
    0%, 100% {
        background: radial-gradient(circle at 60% 20%, rgba(241, 243, 244, 0.8) 0%, transparent 40%),
                    radial-gradient(circle at 10% 60%, rgba(108, 91, 185, 0.05) 0%, transparent 70%),
                    radial-gradient(circle at 90% 40%, rgba(192, 165, 213, 0.07) 0%, transparent 55%);
    }
    50% {
        background: radial-gradient(circle at 50% 40%, rgba(241, 243, 244, 0.85) 0%, transparent 42%),
                    radial-gradient(circle at 30% 50%, rgba(108, 91, 185, 0.07) 0%, transparent 72%),
                    radial-gradient(circle at 70% 60%, rgba(192, 165, 213, 0.09) 0%, transparent 57%);
    }
}

@keyframes lightGradientShift3 {
    0%, 100% {
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 249, 250, 0.7) 50%, rgba(241, 243, 244, 0.5) 100%),
                    radial-gradient(ellipse at 70% 10%, rgba(108, 91, 185, 0.06) 0%, transparent 50%);
    }
    50% {
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 249, 250, 0.75) 50%, rgba(241, 243, 244, 0.55) 100%),
                    radial-gradient(ellipse at 30% 90%, rgba(108, 91, 185, 0.08) 0%, transparent 55%);
    }
}

/* Light theme specific adjustments */
[data-theme="light"] .floating-card {
    background: rgba(108, 91, 185, 0.1);
    border-color: rgba(108, 91, 185, 0.3);
    color: var(--text-primary);
}

[data-theme="light"] .floating-card:hover {
    background: rgba(108, 91, 185, 0.2);
    border-color: var(--accent-color);
}

/* Light theme section backgrounds - all transparent */
[data-theme="light"] .services-overview {
    background: transparent;
}

[data-theme="light"] .why-choose-us {
    background: transparent;
}

[data-theme="light"] .preloader-content h1 {
    color: var(--secondary-color);
}

[data-theme="light"] .scroll-arrow {
    border-color: var(--secondary-color);
}

[data-theme="light"] .hero-particles {
    background: transparent;
}

/* Light theme specific adjustments */
[data-theme="light"] .portfolio-overlay {
    background: rgba(255, 255, 255, 0.95);
}

[data-theme="light"] .overlay-content {
    color: var(--text-primary);
}

[data-theme="light"] .overlay-content p {
    color: var(--text-secondary);
}

[data-theme="light"] .btn-icon {
    background: rgba(108, 91, 185, 0.1);
    border-color: var(--secondary-color);
    color: var(--secondary-color);
}

[data-theme="light"] .btn-icon:hover {
    background: var(--secondary-color);
    color: var(--white);
}

[data-theme="light"] .page-header::before {
    background-image:
        radial-gradient(circle at 20% 80%, rgba(108, 91, 185, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(192, 165, 213, 0.1) 0%, transparent 50%);
}

[data-theme="light"] .timeline::before {
    background: linear-gradient(180deg, var(--secondary-color), var(--accent-color));
}

[data-theme="light"] .timeline-item::before {
    background: var(--accent-color);
    border-color: var(--background-primary);
}

[data-theme="light"] .tech-item:hover {
    background: rgba(108, 91, 185, 0.15);
}

[data-theme="light"] .faq-question:hover {
    background: rgba(108, 91, 185, 0.05);
}

[data-theme="light"] .social-link:hover {
    background: rgba(108, 91, 185, 0.1);
}

[data-theme="light"] .hamburger span {
    background: var(--text-primary);
}

/* Light theme header/navbar specific adjustments */
[data-theme="light"] .navbar {
    background: transparent;
    border-bottom: none;
    box-shadow: none;
}

[data-theme="light"] .navbar.scrolled {
    background: transparent;
    box-shadow: none;
}

[data-theme="light"] .nav-logo a {
    color: var(--secondary-color);
}

[data-theme="light"] .nav-link {
    color: var(--text-primary);
}

[data-theme="light"] .nav-link:hover,
[data-theme="light"] .nav-link.active {
    color: var(--secondary-color);
}

[data-theme="light"] .nav-link::after {
    background: var(--secondary-color);
}

[data-theme="light"] .client-area {
    background: linear-gradient(135deg, var(--secondary-color), var(--accent-color));
    color: var(--white) !important;
}

[data-theme="light"] .theme-toggle {
    border-color: var(--secondary-color);
    color: var(--secondary-color);
}

[data-theme="light"] .theme-toggle:hover {
    background: var(--secondary-color);
    color: var(--white);
}

/* Dark theme header/navbar specific adjustments */
:root .navbar,
[data-theme="dark"] .navbar {
    background: transparent;
    border-bottom: none;
}

:root .navbar.scrolled,
[data-theme="dark"] .navbar.scrolled {
    background: transparent;
    box-shadow: none;
}

:root .nav-logo a,
[data-theme="dark"] .nav-logo a {
    color: var(--accent-color);
}

:root .nav-link,
[data-theme="dark"] .nav-link {
    color: var(--text-primary);
}

:root .nav-link:hover,
:root .nav-link.active,
[data-theme="dark"] .nav-link:hover,
[data-theme="dark"] .nav-link.active {
    color: var(--accent-color);
}

:root .nav-link::after,
[data-theme="dark"] .nav-link::after {
    background: var(--accent-color);
}

:root .theme-toggle,
[data-theme="dark"] .theme-toggle {
    border-color: var(--accent-color);
    color: var(--accent-color);
}

:root .theme-toggle:hover,
[data-theme="dark"] .theme-toggle:hover {
    background: var(--accent-color);
    color: var(--white);
}

/* Ensure hero content is visible by default */
.hero-content {
    opacity: 1;
}

.hero-content > * {
    opacity: 1;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

.hero-particles {
    position: absolute;
    width: 100%;
    height: 100%;
    background: transparent;
}

.hero .container {
    position: relative;
    z-index: 2;
    display: grid;
    grid-template-columns: 1fr 1.2fr;
    gap: 40px;
    align-items: center;
    max-width: 1400px;
}

.hero-content {
    max-width: 600px;
    z-index: 3;
    position: relative;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    line-height: 1.1;
    margin-bottom: 24px;
    color: var(--text-primary);
}

.title-line {
    display: block;
}

.highlight {
    background: linear-gradient(135deg, var(--secondary-color), var(--accent-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    font-size: 1.2rem;
    color: var(--text-secondary);
    margin-bottom: 32px;
    line-height: 1.6;
}

.hero-buttons {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
}

.hero-visual {
    position: relative;
    height: 80vh;
    min-height: 600px;
    max-height: 800px;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: visible;
}

/* ===== 3D ROBOT CONTAINER ===== */
.robot-container {
    position: relative;
    width: 100%;
    height: 100%;
    min-width: 600px;
    min-height: 600px;
    max-width: 800px;
    max-height: 800px;
    z-index: 1;
    border-radius: var(--border-radius);
    overflow: visible;
    display: flex;
    align-items: center;
    justify-content: center;
}

.robot-container::before {
    content: '';
    position: absolute;
    top: -20px;
    left: -20px;
    right: -20px;
    bottom: -20px;
    background: radial-gradient(circle, rgba(108, 91, 185, 0.1) 0%, transparent 70%);
    border-radius: var(--border-radius);
    z-index: -1;
    animation: robotGlow 4s ease-in-out infinite alternate;
}

@keyframes robotGlow {
    0% {
        opacity: 0.3;
        transform: scale(0.95);
    }
    100% {
        opacity: 0.6;
        transform: scale(1.05);
    }
}

.robot-container spline-viewer {
    width: 100%;
    height: 100%;
    border-radius: var(--border-radius);
    object-fit: contain;
    object-position: center;
}

/* Hide Spline logo */
.robot-container spline-viewer #logo,
.robot-container spline-viewer a[href*="spline.design"] {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
}

/* Additional selectors to ensure logo is hidden */
spline-viewer #logo,
spline-viewer a[href*="spline.design"],
spline-viewer a[id="logo"],
spline-viewer [id="logo"],
spline-viewer [href*="spline.design"] {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
    pointer-events: none !important;
    position: absolute !important;
    left: -9999px !important;
    top: -9999px !important;
    width: 0 !important;
    height: 0 !important;
    overflow: hidden !important;
}

/* Hide any element containing "Built with Spline" text */
spline-viewer *[title*="Spline"],
spline-viewer *[alt*="Spline"],
spline-viewer *:contains("Built with Spline"),
spline-viewer *:contains("Spline") {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
}

/* Target the specific logo structure */
spline-viewer > div > a,
spline-viewer div a[href*="spline"],
spline-viewer div a[id="logo"] {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
}

.floating-elements {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 999;
    pointer-events: none;
    overflow: visible;
}

.floating-card {
    position: absolute;
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(192, 165, 213, 0.3);
    border-radius: var(--border-radius);
    padding: 15px 20px;
    text-align: center;
    color: var(--text-primary);
    transition: var(--transition);
    cursor: pointer;
    pointer-events: auto;
    min-width: 100px;
    opacity: 1;
    visibility: visible;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    box-shadow: 0 8px 25px rgba(108, 91, 185, 0.2);
}

.floating-card:hover {
    transform: translateY(-10px) scale(1.05);
    background: rgba(192, 165, 213, 0.25);
    border-color: var(--accent-color);
    box-shadow: 0 15px 35px rgba(108, 91, 185, 0.3);
}

.floating-card i {
    font-size: 2rem;
    color: var(--accent-color);
    margin-bottom: 8px;
    display: block;
    transition: var(--transition);
}

.floating-card:hover i {
    transform: scale(1.1);
    color: var(--white);
}

.floating-card span {
    font-weight: 600;
    font-size: 0.9rem;
    color: var(--text-primary);
    transition: var(--transition);
}

.floating-card:hover span {
    color: var(--white);
}

/* Light theme floating cards */
[data-theme="light"] .floating-card {
    background: rgba(255, 255, 255, 0.9);
    border-color: rgba(108, 91, 185, 0.3);
    box-shadow: 0 8px 25px rgba(108, 91, 185, 0.15);
}

[data-theme="light"] .floating-card:hover {
    background: linear-gradient(135deg, var(--secondary-color), var(--accent-color));
    border-color: var(--secondary-color);
    box-shadow: 0 15px 35px rgba(108, 91, 185, 0.25);
}

[data-theme="light"] .floating-card i {
    color: var(--secondary-color);
}

[data-theme="light"] .floating-card span {
    color: var(--text-primary);
}

/* Dark theme floating cards */
[data-theme="dark"] .floating-card {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(192, 165, 213, 0.3);
    box-shadow: 0 8px 25px rgba(192, 165, 213, 0.2);
}

[data-theme="dark"] .floating-card:hover {
    background: linear-gradient(135deg, var(--secondary-color), var(--accent-color));
    border-color: var(--accent-color);
    box-shadow: 0 15px 35px rgba(192, 165, 213, 0.3);
}

/* Positioning floating cards around the robot */
.floating-card:nth-child(1) { top: 10%; right: 15%; }
.floating-card:nth-child(2) { top: 30%; left: 10%; }
.floating-card:nth-child(3) { bottom: 30%; right: 10%; }
.floating-card:nth-child(4) { bottom: 10%; left: 15%; }

.scroll-indicator {
    position: absolute;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 2;
}

.scroll-arrow {
    width: 30px;
    height: 30px;
    border: 2px solid var(--accent-color);
    border-top: none;
    border-left: none;
    transform: rotate(45deg);
    animation: bounce 2s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0) rotate(45deg); }
    40% { transform: translateY(-10px) rotate(45deg); }
    60% { transform: translateY(-5px) rotate(45deg); }
}

/* ===== MODERN BACKGROUND ANIMATIONS ===== */
@keyframes gradientShift1 {
    0%, 100% {
        background: radial-gradient(circle at 20% 30%, rgba(15, 20, 25, 0.9) 0%, transparent 50%),
                    radial-gradient(circle at 80% 70%, rgba(108, 91, 185, 0.15) 0%, transparent 60%),
                    radial-gradient(circle at 40% 80%, rgba(192, 165, 213, 0.1) 0%, transparent 50%);
    }
    25% {
        background: radial-gradient(circle at 30% 20%, rgba(15, 20, 25, 0.8) 0%, transparent 55%),
                    radial-gradient(circle at 70% 80%, rgba(108, 91, 185, 0.18) 0%, transparent 65%),
                    radial-gradient(circle at 50% 70%, rgba(192, 165, 213, 0.12) 0%, transparent 55%);
    }
    50% {
        background: radial-gradient(circle at 40% 10%, rgba(15, 20, 25, 0.85) 0%, transparent 52%),
                    radial-gradient(circle at 60% 90%, rgba(108, 91, 185, 0.16) 0%, transparent 62%),
                    radial-gradient(circle at 30% 60%, rgba(192, 165, 213, 0.11) 0%, transparent 52%);
    }
    75% {
        background: radial-gradient(circle at 10% 40%, rgba(15, 20, 25, 0.9) 0%, transparent 48%),
                    radial-gradient(circle at 90% 60%, rgba(108, 91, 185, 0.17) 0%, transparent 58%),
                    radial-gradient(circle at 60% 50%, rgba(192, 165, 213, 0.13) 0%, transparent 48%);
    }
}

@keyframes gradientShift2 {
    0%, 100% {
        background: radial-gradient(circle at 60% 20%, rgba(44, 62, 80, 0.3) 0%, transparent 40%),
                    radial-gradient(circle at 10% 60%, rgba(108, 91, 185, 0.08) 0%, transparent 70%),
                    radial-gradient(circle at 90% 40%, rgba(192, 165, 213, 0.12) 0%, transparent 55%);
    }
    33% {
        background: radial-gradient(circle at 70% 30%, rgba(44, 62, 80, 0.35) 0%, transparent 45%),
                    radial-gradient(circle at 20% 70%, rgba(108, 91, 185, 0.10) 0%, transparent 75%),
                    radial-gradient(circle at 80% 50%, rgba(192, 165, 213, 0.14) 0%, transparent 60%);
    }
    66% {
        background: radial-gradient(circle at 50% 40%, rgba(44, 62, 80, 0.32) 0%, transparent 42%),
                    radial-gradient(circle at 30% 50%, rgba(108, 91, 185, 0.09) 0%, transparent 72%),
                    radial-gradient(circle at 70% 60%, rgba(192, 165, 213, 0.13) 0%, transparent 57%);
    }
}

@keyframes gradientShift3 {
    0%, 100% {
        background: linear-gradient(135deg, rgba(1, 8, 21, 0.8) 0%, rgba(15, 20, 25, 0.6) 50%, rgba(44, 62, 80, 0.4) 100%),
                    radial-gradient(ellipse at 70% 10%, rgba(108, 91, 185, 0.1) 0%, transparent 50%);
    }
    50% {
        background: linear-gradient(135deg, rgba(1, 8, 21, 0.85) 0%, rgba(15, 20, 25, 0.65) 50%, rgba(44, 62, 80, 0.45) 100%),
                    radial-gradient(ellipse at 30% 90%, rgba(108, 91, 185, 0.12) 0%, transparent 55%);
    }
}

@keyframes floatSphere {
    0%, 100% {
        transform: translate(0, 0) scale(1);
        opacity: 0.6;
    }
    25% {
        transform: translate(20px, -30px) scale(1.05);
        opacity: 0.7;
    }
    50% {
        transform: translate(-15px, -20px) scale(0.95);
        opacity: 0.5;
    }
    75% {
        transform: translate(10px, 25px) scale(1.02);
        opacity: 0.65;
    }
}

@keyframes rotateWave {
    0% {
        transform: rotate(0deg) scale(1);
    }
    50% {
        transform: rotate(180deg) scale(1.1);
    }
    100% {
        transform: rotate(360deg) scale(1);
    }
}

@keyframes floatParticle {
    0% {
        transform: translateY(100vh) translateX(0) scale(0);
        opacity: 0;
    }
    10% {
        opacity: 1;
        transform: translateY(90vh) translateX(10px) scale(1);
    }
    90% {
        opacity: 1;
        transform: translateY(10vh) translateX(-10px) scale(1);
    }
    100% {
        transform: translateY(-10vh) translateX(0) scale(0);
        opacity: 0;
    }
}

@keyframes noiseShift {
    0%, 100% {
        background-position: 0 0, 0 0, 0 0, 0 0;
        opacity: 0.03;
    }
    25% {
        background-position: 10px 5px, -5px 10px, 15px -8px, -10px 12px;
        opacity: 0.04;
    }
    50% {
        background-position: -8px 12px, 12px -6px, -5px 15px, 8px -10px;
        opacity: 0.025;
    }
    75% {
        background-position: 5px -10px, -12px 8px, 10px 6px, -15px -5px;
        opacity: 0.035;
    }
}

/* ===== SERVICES OVERVIEW ===== */
.services-overview {
    padding: var(--section-padding);
    background: transparent;
    position: relative;
}

.section-header {
    text-align: center;
    margin-bottom: 60px;
}

.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 16px;
}

.section-subtitle {
    font-size: 1.1rem;
    color: var(--text-secondary);
    max-width: 600px;
    margin: 0 auto;
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 30px;
}

.service-card {
    background: var(--card-background);
    backdrop-filter: blur(10px);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 40px 30px;
    text-align: center;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.service-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(192, 165, 213, 0.1), transparent);
    transition: left 0.5s ease;
}

.service-card:hover::before {
    left: 100%;
}

.service-card:hover {
    transform: translateY(-10px);
    border-color: var(--accent-color);
    box-shadow: 0 20px 40px rgba(108, 91, 185, 0.2);
}

.service-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--secondary-color), var(--accent-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 24px;
    transition: var(--transition);
}

.service-card:hover .service-icon {
    transform: scale(1.1) rotate(5deg);
}

.service-icon i {
    font-size: 2rem;
    color: var(--white);
}

.service-card h3 {
    font-size: 1.4rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 16px;
}

.service-card p {
    color: var(--text-secondary);
    margin-bottom: 24px;
    line-height: 1.6;
}

.service-link {
    color: var(--accent-color);
    text-decoration: none;
    font-weight: 600;
    transition: var(--transition);
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.service-link:hover {
    color: var(--text-primary);
    transform: translateX(5px);
}

.service-link i {
    transition: var(--transition);
}

.service-link:hover i {
    transform: translateX(5px);
}

/* ===== WHY CHOOSE US SECTION ===== */
.why-choose-us {
    padding: var(--section-padding);
    background: transparent;
    position: relative;
}

.content-split {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;
}

.content-left h2 {
    font-size: 2.2rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 40px;
}

.feature-list {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.feature-item {
    display: flex;
    align-items: flex-start;
    gap: 20px;
}

.feature-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--secondary-color), var(--accent-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.feature-icon i {
    font-size: 1.5rem;
    color: var(--white);
}

.feature-content h4 {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 8px;
}

.feature-content p {
    color: var(--text-secondary);
    line-height: 1.6;
}

.stats-container {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 30px;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
    margin-top: 60px;
}

.stat-item {
    text-align: center;
    padding: 40px 20px;
    background: var(--card-background);
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
    backdrop-filter: blur(10px);
    position: relative;
    overflow: hidden;
    cursor: pointer;
    /* Remove CSS transitions to let GSAP handle animations */
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.stat-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(135deg, var(--secondary-color), var(--accent-color));
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.stat-item:hover::before {
    transform: scaleX(1);
}

.stat-item:hover {
    border-color: var(--accent-color);
    box-shadow: 0 20px 40px rgba(108, 91, 185, 0.2);
}

.stat-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--secondary-color), var(--accent-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    transition: var(--transition);
}

.stat-item:hover .stat-icon {
    transform: scale(1.1);
    box-shadow: 0 10px 25px rgba(108, 91, 185, 0.3);
}

.stat-icon i {
    font-size: 2rem;
    color: var(--white);
}

.stat-value {
    display: flex;
    align-items: baseline;
    justify-content: center;
    gap: 5px;
    margin-bottom: 10px;
}

.stat-number {
    font-size: 3rem;
    font-weight: 700;
    color: var(--text-primary);
    line-height: 1;
}

.stat-suffix {
    font-size: 2rem;
    font-weight: 600;
    color: var(--accent-color);
}

.stat-label {
    color: var(--text-secondary);
    font-weight: 500;
    font-size: 1.1rem;
}

/* Light theme adjustments for statistics */
[data-theme="light"] .stat-item {
    background: rgba(255, 255, 255, 0.9);
    border-color: rgba(108, 91, 185, 0.2);
    box-shadow: 0 5px 15px rgba(108, 91, 185, 0.1);
}

[data-theme="light"] .stat-item:hover {
    box-shadow: 0 20px 40px rgba(108, 91, 185, 0.15);
}

/* ===== TECHNICAL FEATURES SECTION ===== */
.technical-features {
    padding: var(--section-padding);
    background: transparent;
    position: relative;
}

.features-content {
    display: grid;
    grid-template-columns: 1fr 1.2fr;
    gap: 60px;
    align-items: center;
    margin-top: 60px;
}

.features-visual {
    position: relative;
}

.tech-illustration {
    position: relative;
    padding: 40px;
}

.server-stack {
    display: flex;
    flex-direction: column;
    gap: 20px;
    max-width: 300px;
    margin: 0 auto;
}

.server-layer {
    background: var(--card-background);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 20px;
    display: flex;
    align-items: center;
    gap: 15px;
    transition: var(--transition);
    backdrop-filter: blur(10px);
    cursor: pointer;
}

.server-layer:hover {
    transform: translateX(10px);
    border-color: var(--accent-color);
    box-shadow: 0 10px 25px rgba(108, 91, 185, 0.2);
}

.server-layer i {
    font-size: 1.5rem;
    color: var(--accent-color);
    width: 30px;
    text-align: center;
}

.server-layer span {
    font-weight: 600;
    color: var(--text-primary);
}

.features-list {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.tech-feature {
    display: flex;
    gap: 20px;
    align-items: flex-start;
}

.tech-feature .feature-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--secondary-color), var(--accent-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    transition: var(--transition);
}

.tech-feature:hover .feature-icon {
    transform: scale(1.1);
    box-shadow: 0 10px 25px rgba(108, 91, 185, 0.3);
}

.tech-feature .feature-icon i {
    font-size: 1.5rem;
    color: var(--white);
}

.tech-feature .feature-content h3 {
    font-size: 1.3rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 10px;
}

.tech-feature .feature-content p {
    color: var(--text-secondary);
    line-height: 1.6;
}

/* Light theme adjustments for technical features */
[data-theme="light"] .server-layer {
    background: rgba(255, 255, 255, 0.9);
    border-color: rgba(108, 91, 185, 0.2);
}

[data-theme="light"] .server-layer:hover {
    box-shadow: 0 10px 25px rgba(108, 91, 185, 0.15);
}

/* ===== FAQ SECTION ===== */
.faq-section {
    padding: var(--section-padding);
    background: transparent;
    position: relative;
}

.faq-container {
    max-width: 800px;
    margin: 60px auto 0;
}

.faq-item {
    background: var(--card-background);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    margin-bottom: 20px;
    overflow: hidden;
    transition: var(--transition);
    backdrop-filter: blur(10px);
}

.faq-item:hover {
    border-color: var(--accent-color);
    box-shadow: 0 10px 25px rgba(108, 91, 185, 0.1);
}

.faq-question {
    padding: 25px 30px;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: var(--transition);
    user-select: none;
}

.faq-question:hover {
    background: rgba(192, 165, 213, 0.05);
}

.faq-question h3 {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
    flex: 1;
}

.faq-question i {
    color: var(--accent-color);
    font-size: 1rem;
    transition: transform 0.3s ease;
}

.faq-item.active .faq-question i {
    transform: rotate(180deg);
}

.faq-answer {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease, padding 0.3s ease;
    background: rgba(255, 255, 255, 0.02);
}

.faq-item.active .faq-answer {
    max-height: 200px;
    padding: 0 30px 25px;
}

.faq-answer p {
    color: var(--text-secondary);
    line-height: 1.6;
    margin: 0;
}

/* Light theme adjustments for FAQ */
[data-theme="light"] .faq-item {
    background: rgba(255, 255, 255, 0.9);
    border-color: rgba(108, 91, 185, 0.2);
}

[data-theme="light"] .faq-item:hover {
    box-shadow: 0 10px 25px rgba(108, 91, 185, 0.1);
}

[data-theme="light"] .faq-question:hover {
    background: rgba(108, 91, 185, 0.05);
}

[data-theme="light"] .faq-answer {
    background: rgba(108, 91, 185, 0.02);
}

/* ===== CTA SECTION ===== */
.cta-section {
    padding: var(--section-padding);
    background: transparent;
    text-align: center;
}

.cta-content h2 {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--white);
    margin-bottom: 20px;
}

.cta-content p {
    font-size: 1.1rem;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 32px;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.cta-buttons {
    display: flex;
    gap: 20px;
    justify-content: center;
    flex-wrap: wrap;
}

/* ===== FOOTER ===== */
.footer {
    background: transparent;
    padding: 60px 0 20px;
    border-top: 1px solid var(--border-color);
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 40px;
    margin-bottom: 40px;
}

.footer-section h3 {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--accent-color);
    margin-bottom: 20px;
}

.footer-section h4 {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 16px;
}

.footer-section p {
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: 20px;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 8px;
}

.footer-section ul li a {
    color: var(--text-secondary);
    text-decoration: none;
    transition: var(--transition);
}

.footer-section ul li a:hover {
    color: var(--accent-color);
    transform: translateX(5px);
}

.social-links {
    display: flex;
    gap: 15px;
}

.social-links a {
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    text-decoration: none;
    transition: var(--transition);
}

.social-links a:hover {
    background: var(--accent-color);
    transform: translateY(-3px);
}

.contact-info p {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 12px;
    color: var(--text-secondary);
}

.contact-info i {
    color: var(--accent-color);
    width: 16px;
}

.footer-bottom {
    text-align: center;
    padding-top: 20px;
    border-top: 1px solid var(--border-color);
    color: var(--text-muted);
}

/* ===== SCROLL TO TOP BUTTON ===== */
.scroll-to-top {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, var(--secondary-color), var(--accent-color));
    border: none;
    border-radius: 50%;
    color: var(--white);
    font-size: 1.2rem;
    cursor: pointer;
    transition: var(--transition);
    opacity: 0;
    visibility: hidden;
    z-index: 1000;
}

.scroll-to-top.visible {
    opacity: 1;
    visibility: visible;
}

.scroll-to-top:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(108, 91, 185, 0.3);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 1024px) {
    .hero .container {
        grid-template-columns: 1fr;
        gap: 40px;
        text-align: center;
    }

    .hero-visual {
        height: 60vh;
        min-height: 500px;
    }

    .robot-container {
        min-width: 500px;
        min-height: 500px;
        max-width: 600px;
        max-height: 600px;
    }

    .content-split {
        grid-template-columns: 1fr;
        gap: 40px;
    }

    .stats-container {
        grid-template-columns: repeat(3, 1fr);
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 30px;
    }

    .features-content {
        grid-template-columns: 1fr;
        gap: 40px;
    }

    .faq-question {
        padding: 20px 25px;
    }

    .faq-item.active .faq-answer {
        padding: 0 25px 20px;
    }

    /* Background optimizations for tablets */
    .gradient-sphere {
        filter: blur(30px);
    }

    .gradient-sphere-1,
    .gradient-sphere-2 {
        width: 250px;
        height: 250px;
    }

    .gradient-sphere-3,
    .gradient-sphere-4 {
        width: 200px;
        height: 200px;
    }

    .wave-element::before,
    .wave-element::after {
        filter: blur(50px);
    }
}

@media (max-width: 768px) {
    .hamburger {
        display: flex;
    }

    .nav-menu {
        position: fixed;
        top: 70px;
        left: -100%;
        width: 100%;
        height: calc(100vh - 70px);
        background: transparent;
        backdrop-filter: none;
        flex-direction: column;
        justify-content: flex-start;
        align-items: center;
        padding-top: 50px;
        transition: left 0.3s ease;
        border-top: none;
    }

    .theme-toggle {
        margin: 20px 0 0 0;
        order: 10;
    }

    .nav-menu.active {
        left: 0;
    }

    .nav-link {
        font-size: 1.2rem;
        margin: 10px 0;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .hero-visual {
        height: 50vh;
        min-height: 400px;
    }

    .robot-container {
        min-width: 400px;
        min-height: 400px;
        max-width: 500px;
        max-height: 500px;
    }

    .floating-card {
        padding: 10px 15px;
        min-width: 80px;
    }

    .floating-card i {
        font-size: 1.2rem;
    }

    .floating-card span {
        font-size: 0.8rem;
    }

    .section-title {
        font-size: 2rem;
    }

    .services-grid {
        grid-template-columns: 1fr;
    }

    .stats-container {
        grid-template-columns: 1fr 1fr;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 20px;
    }

    .tech-feature {
        flex-direction: column;
        text-align: center;
        gap: 15px;
    }

    .server-stack {
        max-width: 250px;
    }

    .server-layer {
        padding: 15px;
        gap: 10px;
    }

    .faq-question {
        padding: 20px;
    }

    .faq-question h3 {
        font-size: 1rem;
    }

    .faq-item.active .faq-answer {
        padding: 0 20px 20px;
    }

    .cta-buttons {
        flex-direction: column;
        align-items: center;
    }

    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
    }

    /* Light theme mobile menu */
    [data-theme="light"] .nav-menu {
        background: transparent;
        border-top: none;
        box-shadow: none;
    }

    /* Dark theme mobile menu */
    :root .nav-menu,
    [data-theme="dark"] .nav-menu {
        background: transparent;
        border-top: none;
    }

    /* Mobile background optimizations */
    .gradient-sphere {
        display: none; /* Hide spheres on mobile for performance */
    }

    .wave-element {
        opacity: 0.2; /* Reduce wave opacity */
    }

    .wave-element::before,
    .wave-element::after {
        filter: blur(40px);
        animation-duration: 60s; /* Slower animations */
    }

    .particle-system {
        display: none; /* Hide particles on mobile */
    }

    .noise-texture {
        opacity: 0.01; /* Reduce noise texture */
    }

    /* Simplify gradient layers for mobile */
    .gradient-layer-1,
    .gradient-layer-2,
    .gradient-layer-3 {
        animation-duration: 40s; /* Slower animations */
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 15px;
    }

    .hero-title {
        font-size: 2rem;
    }

    .hero-visual {
        height: 40vh;
        min-height: 350px;
    }

    .robot-container {
        min-width: 350px;
        min-height: 350px;
        max-width: 400px;
        max-height: 400px;
    }

    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }

    .btn {
        width: 100%;
        max-width: 250px;
    }

    .floating-card {
        padding: 8px 12px;
        min-width: 70px;
    }

    .floating-card i {
        font-size: 1rem;
        margin-bottom: 5px;
    }

    .floating-card span {
        font-size: 0.7rem;
    }

    .stats-container {
        grid-template-columns: 1fr;
    }

    .stats-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .stat-item {
        padding: 30px 15px;
    }

    .stat-number {
        font-size: 2.5rem;
    }

    .stat-suffix {
        font-size: 1.8rem;
    }

    .tech-feature .feature-icon {
        width: 50px;
        height: 50px;
    }

    .tech-feature .feature-icon i {
        font-size: 1.2rem;
    }

    .server-layer {
        padding: 12px;
        gap: 8px;
    }

    .server-layer i {
        font-size: 1.2rem;
    }

    .faq-question {
        padding: 15px;
    }

    .faq-question h3 {
        font-size: 0.95rem;
    }

    .faq-item.active .faq-answer {
        padding: 0 15px 15px;
    }
}

/* ===== PAGE HEADER ===== */
.page-header {
    padding: 120px 0 80px;
    background: transparent;
    text-align: center;
    position: relative;
    overflow: hidden;
}

/* Dark theme page header
:root .page-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, #0a1428 50%, #1a1f3a 100%);
}*/

/* Light theme page header */
[data-theme="light"] .page-header {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 50%, #e9ecef 100%);
}

.page-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(circle at 20% 80%, rgba(192, 165, 213, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(108, 91, 185, 0.1) 0%, transparent 50%);
}

.page-header-content {
    position: relative;
    z-index: 2;
}

.page-header h1 {
    font-size: 3rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 16px;
}

.page-header p {
    font-size: 1.2rem;
    color: var(--text-secondary);
    margin-bottom: 24px;
}

.breadcrumb {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
    font-size: 0.9rem;
}

.breadcrumb a {
    color: var(--accent-color);
    text-decoration: none;
    transition: var(--transition);
}

.breadcrumb a:hover {
    color: var(--text-primary);
}

.breadcrumb span {
    color: var(--text-muted);
}

/* ===== MISSION & VISION ===== */
.mission-vision {
    padding: var(--section-padding);
    background: transparent;
}

.content-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 40px;
}

.content-card {
    background: var(--card-background);
    backdrop-filter: blur(10px);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 40px 30px;
    text-align: center;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.content-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(192, 165, 213, 0.1), transparent);
    transition: left 0.5s ease;
}

.content-card:hover::before {
    left: 100%;
}

.content-card:hover {
    transform: translateY(-10px);
    border-color: var(--accent-color);
    box-shadow: 0 20px 40px rgba(108, 91, 185, 0.2);
}

.card-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--secondary-color), var(--accent-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 24px;
    transition: var(--transition);
}

.content-card:hover .card-icon {
    transform: scale(1.1) rotate(10deg);
}

.card-icon i {
    font-size: 2rem;
    color: var(--white);
}

.content-card h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 16px;
}

.content-card p {
    color: var(--text-secondary);
    line-height: 1.6;
}

/* Light theme specific adjustments for about page content cards */
[data-theme="light"] .content-card {
    background: rgba(255, 255, 255, 0.8);
    border-color: rgba(108, 91, 185, 0.15);
    box-shadow: 0 5px 20px rgba(108, 91, 185, 0.1);
}

[data-theme="light"] .content-card:hover {
    background: rgba(255, 255, 255, 0.95);
    box-shadow: 0 20px 40px rgba(108, 91, 185, 0.2);
    border-color: var(--secondary-color);
}

[data-theme="light"] .content-card::before {
    background: linear-gradient(90deg, transparent, rgba(108, 91, 185, 0.15), transparent);
}

/* Dark theme specific adjustments for about page content cards */
[data-theme="dark"] .content-card {
    background: rgba(255, 255, 255, 0.05);
    border-color: rgba(192, 165, 213, 0.1);
}

[data-theme="dark"] .content-card:hover {
    background: rgba(255, 255, 255, 0.08);
    border-color: var(--accent-color);
}

[data-theme="dark"] .content-card::before {
    background: linear-gradient(90deg, transparent, rgba(192, 165, 213, 0.15), transparent);
}

/* ===== COMPANY STORY ===== */
.company-story {
    padding: var(--section-padding);
    background: transparent;
}

.story-content p {
    color: var(--text-secondary);
    line-height: 1.7;
    margin-bottom: 20px;
    font-size: 1.1rem;
}

.story-stats {
    display: flex;
    gap: 40px;
    margin-top: 40px;
}

.story-stat {
    text-align: center;
}

.story-stat .stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--accent-color);
    margin-bottom: 8px;
}

.story-stat .stat-label {
    color: var(--text-secondary);
    font-weight: 500;
    font-size: 0.9rem;
}

.story-visual {
    position: relative;
}

.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: linear-gradient(180deg, var(--secondary-color), var(--accent-color));
}

.timeline-item {
    position: relative;
    margin-bottom: 40px;
    padding-left: 40px;
}

.timeline-item::before {
    content: '';
    position: absolute;
    left: -8px;
    top: 8px;
    width: 16px;
    height: 16px;
    background: var(--accent-color);
    border-radius: 50%;
    border: 3px solid var(--primary-color);
}

.timeline-year {
    font-size: 1.2rem;
    font-weight: 700;
    color: var(--accent-color);
    margin-bottom: 8px;
}

.timeline-content h4 {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 4px;
}

.timeline-content p {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

/* ===== TEAM SECTION ===== */
.team-section {
    padding: var(--section-padding);
    background: transparent;
}

.team-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 40px;
}

.team-member {
    background: var(--card-background);
    border-radius: var(--border-radius);
    padding: 30px 20px;
    text-align: center;
    transition: var(--transition);
    border: 1px solid var(--border-color);
}

.team-member:hover {
    transform: translateY(-10px);
    border-color: var(--accent-color);
    box-shadow: 0 20px 40px rgba(108, 91, 185, 0.2);
}

.member-image {
    position: relative;
    width: 120px;
    height: 120px;
    margin: 0 auto 20px;
    border-radius: 50%;
    overflow: hidden;
}

.image-placeholder {
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, var(--secondary-color), var(--accent-color));
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
}

.image-placeholder i {
    font-size: 3rem;
    color: var(--white);
}

.member-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(108, 91, 185, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: var(--transition);
    border-radius: 50%;
}

.member-image:hover .member-overlay {
    opacity: 1;
}

.member-social {
    display: flex;
    gap: 10px;
}

.member-social a {
    width: 35px;
    height: 35px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    text-decoration: none;
    transition: var(--transition);
}

.member-social a:hover {
    background: var(--white);
    color: var(--secondary-color);
    transform: scale(1.1);
}

.member-info h4 {
    font-size: 1.3rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 8px;
}

.member-info p {
    color: var(--accent-color);
    font-weight: 500;
    margin-bottom: 8px;
}

.member-info span {
    color: var(--text-secondary);
    font-size: 0.9rem;
    line-height: 1.4;
}

/* Light theme specific adjustments for team section */
[data-theme="light"] .team-member {
    background: rgba(255, 255, 255, 0.8);
    border-color: rgba(108, 91, 185, 0.15);
    box-shadow: 0 5px 20px rgba(108, 91, 185, 0.1);
}

[data-theme="light"] .team-member:hover {
    background: rgba(255, 255, 255, 0.95);
    box-shadow: 0 20px 40px rgba(108, 91, 185, 0.2);
    border-color: var(--secondary-color);
}

[data-theme="light"] .member-overlay {
    background: rgba(108, 91, 185, 0.9);
}

/* Dark theme specific adjustments for team section */
[data-theme="dark"] .team-member {
    background: rgba(255, 255, 255, 0.05);
    border-color: rgba(192, 165, 213, 0.1);
}

[data-theme="dark"] .team-member:hover {
    background: rgba(255, 255, 255, 0.08);
    border-color: var(--accent-color);
}

[data-theme="dark"] .member-overlay {
    background: rgba(192, 165, 213, 0.9);
}

/* ===== ADVANTAGES GRID ===== */
.advantages-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
}

.advantage-item {
    background: var(--card-background);
    border-radius: var(--border-radius);
    padding: 30px 25px;
    text-align: center;
    transition: var(--transition);
    border: 1px solid var(--border-color);
}

.advantage-item:hover {
    transform: translateY(-5px);
    border-color: var(--accent-color);
    box-shadow: 0 15px 30px rgba(108, 91, 185, 0.2);
}

.advantage-icon {
    width: 70px;
    height: 70px;
    background: linear-gradient(135deg, var(--secondary-color), var(--accent-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    transition: var(--transition);
}

.advantage-item:hover .advantage-icon {
    transform: scale(1.1) rotate(10deg);
}

.advantage-icon i {
    font-size: 1.8rem;
    color: var(--white);
}

.advantage-item h4 {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 12px;
}

.advantage-item p {
    color: var(--text-secondary);
    line-height: 1.6;
}

/* Light theme specific adjustments for advantages section */
[data-theme="light"] .advantage-item {
    background: rgba(255, 255, 255, 0.8);
    border-color: rgba(108, 91, 185, 0.15);
    box-shadow: 0 5px 20px rgba(108, 91, 185, 0.1);
}

[data-theme="light"] .advantage-item:hover {
    background: rgba(255, 255, 255, 0.95);
    box-shadow: 0 15px 30px rgba(108, 91, 185, 0.2);
    border-color: var(--secondary-color);
}

/* Dark theme specific adjustments for advantages section */
[data-theme="dark"] .advantage-item {
    background: rgba(255, 255, 255, 0.05);
    border-color: rgba(192, 165, 213, 0.1);
}

[data-theme="dark"] .advantage-item:hover {
    background: rgba(255, 255, 255, 0.08);
    border-color: var(--accent-color);
}

/* Light theme timeline adjustments */
[data-theme="light"] .timeline-item::before {
    background: var(--secondary-color);
    border-color: var(--background-primary);
}

[data-theme="light"] .timeline::before {
    background: linear-gradient(180deg, var(--secondary-color), var(--accent-color));
}

/* Dark theme timeline adjustments */
[data-theme="dark"] .timeline-item::before {
    background: var(--accent-color);
    border-color: var(--background-primary);
}

[data-theme="dark"] .timeline::before {
    background: linear-gradient(180deg, var(--secondary-color), var(--accent-color));
}

/* ===== DETAILED SERVICE CARDS ===== */
.service-card.detailed {
    padding: 50px 40px;
    max-width: 400px;
    margin: 0 auto;
}

.service-features {
    margin: 30px 0;
}

.service-features ul {
    list-style: none;
    padding: 0;
}

.service-features li {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 12px;
    color: var(--text-secondary);
    font-size: 0.95rem;
}

.service-features li i {
    color: var(--accent-color);
    font-size: 0.8rem;
    width: 16px;
}

.service-pricing {
    margin: 30px 0;
    text-align: center;
}

.price {
    font-size: 1.3rem;
    font-weight: 700;
    color: var(--accent-color);
    background: rgba(192, 165, 213, 0.1);
    padding: 12px 20px;
    border-radius: 25px;
    display: inline-block;
}

/* ===== SERVICE PROCESS ===== */
.service-process {
    padding: var(--section-padding);
    background: transparent;
}

.process-steps {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 40px;
}

.process-step {
    text-align: center;
    position: relative;
}

.step-number {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--secondary-color), var(--accent-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--white);
    margin: 0 auto 24px;
    position: relative;
    z-index: 2;
}

.step-content h4 {
    font-size: 1.3rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 16px;
}

.step-content p {
    color: var(--text-secondary);
    line-height: 1.6;
}

/* ===== TECHNOLOGIES SECTION ===== */
.technologies {
    padding: var(--section-padding);
    background: transparent;
}

.tech-categories {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 40px;
}

.tech-category {
    background: var(--card-background);
    border-radius: var(--border-radius);
    padding: 30px 25px;
    border: 1px solid var(--border-color);
    transition: var(--transition);
}

.tech-category:hover {
    border-color: var(--accent-color);
    transform: translateY(-5px);
}

.tech-category h4 {
    font-size: 1.3rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 24px;
    text-align: center;
}

.tech-icons {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
    gap: 20px;
}

.tech-item {
    text-align: center;
    padding: 20px 10px;
    background: var(--background-tertiary);
    border-radius: 8px;
    transition: var(--transition);
    cursor: pointer;
}

.tech-item:hover {
    background: rgba(192, 165, 213, 0.1);
    transform: translateY(-3px);
}

.tech-item i {
    font-size: 2rem;
    color: var(--accent-color);
    margin-bottom: 8px;
    display: block;
}

.tech-item span {
    font-size: 0.85rem;
    color: var(--text-secondary);
    font-weight: 500;
}

/* ===== FAQ SECTION ===== */
.faq-section {
    padding: var(--section-padding);
    background: transparent;
}

.faq-grid {
    display: grid;
    gap: 20px;
    max-width: 800px;
    margin: 0 auto;
}

.faq-item {
    background: var(--card-background);
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
    overflow: hidden;
    transition: var(--transition);
}

.faq-item:hover {
    border-color: var(--accent-color);
}

.faq-question {
    padding: 25px 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;
    transition: var(--transition);
}

.faq-question:hover {
    background: rgba(192, 165, 213, 0.05);
}

.faq-question h4 {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.faq-question i {
    color: var(--accent-color);
    font-size: 1.2rem;
    transition: transform 0.3s ease;
}

.faq-item.active .faq-question i {
    transform: rotate(45deg);
}

.faq-answer {
    padding: 0 30px;
    max-height: 0;
    overflow: hidden;
    transition: all 0.3s ease;
}

.faq-item.active .faq-answer {
    padding: 0 30px 25px;
    max-height: 200px;
}

.faq-answer p {
    color: var(--text-secondary);
    line-height: 1.6;
    margin: 0;
}

/* ===== RESPONSIVE UPDATES ===== */
@media (max-width: 768px) {
    .page-header h1 {
        font-size: 2.5rem;
    }

    .process-steps {
        grid-template-columns: 1fr;
    }

    .tech-categories {
        grid-template-columns: 1fr;
    }

    .tech-icons {
        grid-template-columns: repeat(3, 1fr);
    }

    .story-stats {
        flex-direction: column;
        gap: 20px;
        text-align: center;
    }

    .timeline {
        padding-left: 20px;
    }

    .timeline-item {
        padding-left: 30px;
    }
}

@media (max-width: 480px) {
    .service-card.detailed {
        padding: 30px 20px;
    }

    .tech-icons {
        grid-template-columns: repeat(2, 1fr);
    }

    .faq-question {
        padding: 20px;
    }

    .faq-item.active .faq-answer {
        padding: 0 20px 20px;
    }
}

/* ===== PORTFOLIO FILTER ===== */
.portfolio-filter {
    padding: 40px 0;
    background: transparent;
    border-bottom: 1px solid var(--border-color);
}

.filter-buttons {
    display: flex;
    justify-content: center;
    gap: 20px;
    flex-wrap: wrap;
}

.filter-btn {
    padding: 12px 24px;
    background: transparent;
    border: 2px solid var(--border-color);
    border-radius: 25px;
    color: var(--text-secondary);
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
    font-size: 0.95rem;
}

.filter-btn:hover,
.filter-btn.active {
    background: linear-gradient(135deg, var(--secondary-color), var(--accent-color));
    border-color: var(--accent-color);
    color: var(--white);
    transform: translateY(-2px);
}

/* ===== PORTFOLIO GRID ===== */
.portfolio-grid {
    padding: var(--section-padding);
    background: transparent;
}

.portfolio-items {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 40px;
}

.portfolio-item {
    background: var(--card-background);
    border-radius: var(--border-radius);
    overflow: hidden;
    transition: var(--transition);
    border: 1px solid var(--border-color);
}

.portfolio-item:hover {
    transform: translateY(-10px);
    border-color: var(--accent-color);
    box-shadow: 0 20px 40px rgba(108, 91, 185, 0.2);
}

.portfolio-image {
    position: relative;
    height: 250px;
    overflow: hidden;
}

.image-placeholder {
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, var(--secondary-color), var(--accent-color));
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

.image-placeholder i {
    font-size: 4rem;
    color: var(--white);
    opacity: 0.8;
}

.image-placeholder.large i {
    font-size: 6rem;
}

.portfolio-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(1, 8, 21, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: var(--transition);
}

.portfolio-item:hover .portfolio-overlay {
    opacity: 1;
}

.overlay-content {
    text-align: center;
    color: var(--text-primary);
    padding: 20px;
}

.overlay-content h4 {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 8px;
}

.overlay-content p {
    color: var(--text-secondary);
    margin-bottom: 20px;
}

.overlay-buttons {
    display: flex;
    gap: 15px;
    justify-content: center;
}

.btn-icon {
    width: 45px;
    height: 45px;
    background: rgba(192, 165, 213, 0.2);
    border: 2px solid var(--accent-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    text-decoration: none;
    transition: var(--transition);
    cursor: pointer;
}

.btn-icon:hover {
    background: var(--accent-color);
    transform: scale(1.1);
}

.portfolio-info {
    padding: 25px;
}

.portfolio-category {
    display: inline-block;
    background: rgba(192, 165, 213, 0.1);
    color: var(--accent-color);
    padding: 4px 12px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 500;
    margin-bottom: 12px;
}

.portfolio-info h3 {
    font-size: 1.3rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 8px;
}

.portfolio-info p {
    color: var(--text-secondary);
    line-height: 1.5;
}

/* ===== PORTFOLIO MODAL ===== */
.portfolio-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition);
}

.portfolio-modal.active {
    opacity: 1;
    visibility: visible;
}

.modal-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    cursor: pointer;
}

.modal-content {
    background: var(--background-primary);
    border-radius: var(--border-radius);
    max-width: 800px;
    max-height: 90vh;
    width: 90%;
    overflow-y: auto;
    position: relative;
    border: 1px solid var(--border-color);
}

.modal-close {
    position: absolute;
    top: 20px;
    right: 20px;
    width: 40px;
    height: 40px;
    background: var(--background-tertiary);
    border: none;
    border-radius: 50%;
    color: var(--text-primary);
    font-size: 1.2rem;
    cursor: pointer;
    transition: var(--transition);
    z-index: 10;
}

.modal-close:hover {
    background: var(--accent-color);
    transform: scale(1.1);
}

.modal-header {
    display: grid;
    grid-template-columns: 200px 1fr;
    gap: 30px;
    padding: 40px;
    border-bottom: 1px solid var(--border-color);
}

.modal-image {
    height: 150px;
}

.modal-info h2 {
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 16px;
}

.modal-category {
    display: inline-block;
    background: rgba(192, 165, 213, 0.1);
    color: var(--accent-color);
    padding: 6px 16px;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 500;
    margin-bottom: 16px;
}

.modal-meta {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.meta-item {
    color: var(--text-secondary);
    font-size: 0.95rem;
}

.meta-item strong {
    color: var(--text-primary);
}

.modal-description,
.modal-features,
.modal-technologies,
.modal-results {
    padding: 30px 40px;
    border-bottom: 1px solid var(--border-color);
}

.modal-description h3,
.modal-features h3,
.modal-technologies h3,
.modal-results h3 {
    font-size: 1.3rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 16px;
}

.modal-description p {
    color: var(--text-secondary);
    line-height: 1.7;
    font-size: 1.05rem;
}

.modal-features ul {
    list-style: none;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 12px;
}

.modal-features li {
    display: flex;
    align-items: center;
    gap: 12px;
    color: var(--text-secondary);
}

.modal-features li i {
    color: var(--accent-color);
    font-size: 0.9rem;
}

.tech-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
}

.tech-tag {
    background: rgba(192, 165, 213, 0.1);
    color: var(--accent-color);
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 500;
    border: 1px solid rgba(192, 165, 213, 0.2);
}

.results-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.result-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 16px;
    background: var(--card-background);
    border-radius: 8px;
    border: 1px solid var(--border-color);
}

.result-item i {
    color: var(--accent-color);
    font-size: 1.2rem;
}

.result-item span {
    color: var(--text-primary);
    font-weight: 500;
}

/* ===== TESTIMONIALS ===== */
.testimonials {
    padding: var(--section-padding);
    background: transparent;
}

.testimonials-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 40px;
}

.testimonial-item {
    background: var(--card-background);
    border-radius: var(--border-radius);
    padding: 30px;
    border: 1px solid var(--border-color);
    transition: var(--transition);
}

.testimonial-item:hover {
    transform: translateY(-5px);
    border-color: var(--accent-color);
    box-shadow: 0 15px 30px rgba(108, 91, 185, 0.2);
}

.testimonial-content {
    margin-bottom: 25px;
}

.quote-icon {
    margin-bottom: 20px;
}

.quote-icon i {
    font-size: 2rem;
    color: var(--accent-color);
    opacity: 0.7;
}

.testimonial-content p {
    color: var(--text-secondary);
    line-height: 1.7;
    font-size: 1.05rem;
    font-style: italic;
}

.testimonial-author {
    display: flex;
    align-items: center;
    gap: 15px;
}

.author-avatar {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, var(--secondary-color), var(--accent-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.author-avatar i {
    font-size: 1.5rem;
    color: var(--white);
}

.author-info h4 {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 4px;
}

.author-info span {
    color: var(--text-muted);
    font-size: 0.9rem;
}

/* ===== CONTACT SECTION ===== */
.contact-section {
    padding: var(--section-padding);
    background: transparent;
}

.contact-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: start;
}

.contact-info-section {
    padding-right: 20px;
}

.contact-methods {
    margin: 40px 0;
}

.contact-method {
    display: flex;
    align-items: flex-start;
    gap: 20px;
    margin-bottom: 30px;
    padding: 20px;
    background: var(--card-background);
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
    transition: var(--transition);
}

.contact-method:hover {
    border-color: var(--accent-color);
    transform: translateY(-3px);
}

.method-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, var(--secondary-color), var(--accent-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.method-icon i {
    font-size: 1.3rem;
    color: var(--white);
}

.method-content h4 {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 8px;
}

.method-content p {
    color: var(--text-secondary);
    line-height: 1.5;
}

.social-section {
    margin-top: 40px;
}

.social-section h4 {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 20px;
}

.social-link {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 16px;
    background: var(--card-background);
    border-radius: 8px;
    color: var(--text-secondary);
    text-decoration: none;
    transition: var(--transition);
    margin-bottom: 10px;
    border: 1px solid var(--border-color);
}

.social-link:hover {
    background: rgba(192, 165, 213, 0.1);
    color: var(--text-primary);
    border-color: var(--accent-color);
    transform: translateX(5px);
}

.social-link i {
    font-size: 1.2rem;
    color: var(--accent-color);
}

/* ===== CONTACT FORM ===== */
.contact-form-section {
    background: var(--card-background);
    border-radius: var(--border-radius);
    padding: 40px;
    border: 1px solid var(--border-color);
}

.contact-form {
    display: flex;
    flex-direction: column;
    gap: 25px;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group label {
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: 8px;
    font-size: 0.95rem;
}

.form-group input,
.form-group select,
.form-group textarea {
    padding: 14px 16px;
    background: var(--background-tertiary);
    border: 2px solid var(--border-color);
    border-radius: 8px;
    color: var(--text-primary);
    font-size: 1rem;
    transition: var(--transition);
    font-family: inherit;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--accent-color);
    background: var(--card-background);
    box-shadow: 0 0 0 3px rgba(192, 165, 213, 0.1);
}

.form-group input::placeholder,
.form-group textarea::placeholder {
    color: var(--text-muted);
}

.form-group input.error,
.form-group select.error,
.form-group textarea.error {
    border-color: #ff6b6b;
    background: rgba(255, 107, 107, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 120px;
}

.checkbox-group {
    flex-direction: row;
    align-items: flex-start;
    gap: 12px;
}

.checkbox-label {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    cursor: pointer;
    color: var(--text-secondary);
    line-height: 1.5;
    font-size: 0.9rem;
}

.checkbox-label input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 20px;
    height: 20px;
    background: var(--background-tertiary);
    border: 2px solid var(--border-color);
    border-radius: 4px;
    position: relative;
    transition: var(--transition);
    flex-shrink: 0;
    margin-top: 2px;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark {
    background: linear-gradient(135deg, var(--secondary-color), var(--accent-color));
    border-color: var(--accent-color);
}

.checkbox-label input[type="checkbox"]:checked + .checkmark::after {
    content: '\f00c';
    font-family: 'Font Awesome 6 Free';
    font-weight: 900;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: var(--white);
    font-size: 0.7rem;
}

.privacy-link {
    color: var(--accent-color);
    text-decoration: none;
    transition: var(--transition);
}

.privacy-link:hover {
    color: var(--white);
    text-decoration: underline;
}

.submit-btn {
    margin-top: 10px;
    position: relative;
    overflow: hidden;
}

.submit-btn:disabled {
    opacity: 0.7;
    cursor: not-allowed;
}

/* ===== MAP SECTION ===== */
.map-section {
    padding: var(--section-padding);
    background: var(--background-secondary);
}

.map-container {
    position: relative;
    border-radius: var(--border-radius);
    overflow: hidden;
    border: 1px solid var(--border-color);
}

.map-container iframe {
    width: 100%;
    height: 400px;
    filter: grayscale(100%) contrast(1.2);
    transition: var(--transition);
}

.map-container:hover iframe {
    filter: grayscale(0%) contrast(1);
}

.map-overlay {
    position: absolute;
    top: 20px;
    left: 20px;
    background: var(--card-background);
    backdrop-filter: blur(10px);
    padding: 25px;
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
    max-width: 300px;
}

.map-info h4 {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 8px;
}

.map-info p {
    color: var(--text-secondary);
    margin-bottom: 16px;
    line-height: 1.5;
}

/* ===== CONTACT FAQ ===== */
.contact-faq {
    padding: var(--section-padding);
    background: var(--background-primary);
}

/* ===== RESPONSIVE CONTACT STYLES ===== */
@media (max-width: 1024px) {
    .contact-grid {
        grid-template-columns: 1fr;
        gap: 40px;
    }

    .contact-info-section {
        padding-right: 0;
    }

    .modal-header {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .modal-image {
        height: 120px;
        margin: 0 auto;
        width: 120px;
    }
}

@media (max-width: 768px) {
    .contact-form-section {
        padding: 25px;
    }

    .contact-method {
        flex-direction: column;
        text-align: center;
        gap: 15px;
    }

    .method-icon {
        margin: 0 auto;
    }

    .portfolio-items {
        grid-template-columns: 1fr;
    }

    .filter-buttons {
        gap: 10px;
    }

    .filter-btn {
        padding: 10px 16px;
        font-size: 0.9rem;
    }

    .map-overlay {
        position: relative;
        top: 0;
        left: 0;
        margin: 20px;
        max-width: none;
    }
}

@media (max-width: 480px) {
    .contact-form-section {
        padding: 20px;
    }

    .form-group input,
    .form-group select,
    .form-group textarea {
        padding: 12px;
    }

    .checkbox-label {
        font-size: 0.85rem;
    }

    .modal-content {
        width: 95%;
        max-height: 95vh;
    }

    .modal-header,
    .modal-description,
    .modal-features,
    .modal-technologies,
    .modal-results {
        padding: 20px;
    }

    .results-grid {
        grid-template-columns: 1fr;
    }
}

/* ===== FEATURES GRID (Updated Why Choose Us) ===== */
.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 40px;
    margin-top: 60px;
}

.feature-block {
    text-align: center;
    padding: 40px 30px;
    background: var(--card-background);
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
    transition: var(--transition);
}

.feature-block:hover {
    transform: translateY(-10px);
    box-shadow: var(--box-shadow);
    border-color: var(--accent-color);
}

.feature-block .feature-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto 20px;
    background: linear-gradient(135deg, var(--secondary-color), var(--accent-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: var(--white);
}

.feature-block h3 {
    font-size: 1.5rem;
    margin-bottom: 15px;
    color: var(--text-primary);
}

.feature-block p {
    color: var(--text-secondary);
    line-height: 1.6;
}

/* ===== PORTFOLIO PREVIEW ===== */
.portfolio-preview {
    padding: var(--section-padding);
    background: transparent;
}

.portfolio-gallery {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
    margin-top: 60px;
}

.portfolio-item-preview {
    position: relative;
    border-radius: var(--border-radius);
    overflow: hidden;
    background: var(--card-background);
    border: 1px solid var(--border-color);
    transition: var(--transition);
}

.portfolio-item-preview:hover {
    transform: translateY(-10px);
    box-shadow: var(--box-shadow);
}

.portfolio-item-preview .portfolio-image {
    position: relative;
    height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, var(--secondary-color), var(--accent-color));
}

.portfolio-item-preview .image-placeholder {
    font-size: 3rem;
    color: var(--white);
    opacity: 0.8;
}

.portfolio-item-preview .portfolio-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
    color: var(--white);
    padding: 20px;
    transform: translateY(100%);
    transition: var(--transition);
}

.portfolio-item-preview:hover .portfolio-overlay {
    transform: translateY(0);
}

.portfolio-overlay h4 {
    font-size: 1.2rem;
    margin-bottom: 5px;
}

.portfolio-overlay p {
    font-size: 0.9rem;
    opacity: 0.9;
}

.portfolio-cta {
    text-align: center;
    margin-top: 50px;
}

/* ===== CLIENT TESTIMONIALS ===== */
.client-testimonials {
    padding: var(--section-padding);
    background: transparent;
}

.testimonials-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 40px;
    margin-top: 60px;
}

.testimonial-card {
    background: var(--card-background);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 30px;
    transition: var(--transition);
}

.testimonial-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--box-shadow);
    border-color: var(--accent-color);
}

.testimonial-content {
    margin-bottom: 25px;
}

.quote-icon {
    font-size: 2rem;
    color: var(--accent-color);
    margin-bottom: 15px;
}

.testimonial-content p {
    font-style: italic;
    line-height: 1.6;
    color: var(--text-secondary);
    font-size: 1.1rem;
}

.testimonial-author {
    display: flex;
    align-items: center;
    gap: 15px;
}

.author-avatar {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, var(--secondary-color), var(--accent-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-size: 1.2rem;
}

.author-info h4 {
    color: var(--text-primary);
    margin-bottom: 5px;
    font-size: 1.1rem;
}

.author-info span {
    color: var(--text-muted);
    font-size: 0.9rem;
}

/* ===== RESPONSIVE DESIGN FOR NEW SECTIONS ===== */
@media (max-width: 768px) {
    .features-grid {
        grid-template-columns: 1fr;
        gap: 30px;
    }

    .feature-block {
        padding: 30px 20px;
    }

    .portfolio-gallery {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
    }

    .testimonials-grid {
        grid-template-columns: 1fr;
        gap: 30px;
    }

    .testimonial-card {
        padding: 25px;
    }
}

@media (max-width: 480px) {
    .feature-block .feature-icon {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }

    .feature-block h3 {
        font-size: 1.3rem;
    }

    .portfolio-gallery {
        grid-template-columns: 1fr;
    }

    .testimonial-content p {
        font-size: 1rem;
    }
}

/* ===== KNOWLEDGEBASE STYLES ===== */

/* KB Search Section */
.kb-search {
    padding: 60px 0;
    background: transparent;
}

.search-container {
    max-width: 800px;
    margin: 0 auto;
    position: relative;
}

.search-box {
    position: relative;
    display: flex;
    align-items: center;
    background: var(--card-background);
    border: 2px solid var(--border-color);
    border-radius: 50px;
    padding: 8px;
    transition: var(--transition);
    backdrop-filter: blur(10px);
}

.search-box:focus-within {
    border-color: var(--accent-color);
    box-shadow: 0 0 0 4px rgba(192, 165, 213, 0.1);
}

.search-box i {
    color: var(--text-muted);
    font-size: 1.2rem;
    margin: 0 20px;
}

.search-box input {
    flex: 1;
    background: transparent;
    border: none;
    outline: none;
    color: var(--text-primary);
    font-size: 1.1rem;
    padding: 16px 0;
}

.search-box input::placeholder {
    color: var(--text-muted);
}

.search-btn {
    background: linear-gradient(135deg, var(--secondary-color), var(--accent-color));
    color: var(--white);
    border: none;
    border-radius: 40px;
    padding: 16px 32px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
}

.search-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(108, 91, 185, 0.3);
}

.search-suggestions {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: var(--card-background);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    margin-top: 10px;
    max-height: 300px;
    overflow-y: auto;
    z-index: 100;
    display: none;
    backdrop-filter: blur(10px);
}

.search-suggestions.active {
    display: block;
}

.suggestion-item {
    padding: 15px 20px;
    border-bottom: 1px solid var(--border-color);
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 12px;
}

.suggestion-item:hover {
    background: var(--background-tertiary);
}

.suggestion-item:last-child {
    border-bottom: none;
}

.suggestion-item i {
    color: var(--accent-color);
    font-size: 0.9rem;
}

/* KB Categories */
.kb-categories {
    padding: var(--section-padding);
    background: transparent;
}

.categories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-top: 60px;
}

.category-card {
    background: var(--card-background);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 30px;
    text-align: center;
    transition: var(--transition);
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.category-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(192, 165, 213, 0.1), transparent);
    transition: left 0.5s ease;
}

.category-card:hover::before {
    left: 100%;
}

.category-card:hover {
    transform: translateY(-10px);
    border-color: var(--accent-color);
    box-shadow: 0 20px 40px rgba(108, 91, 185, 0.2);
}

.category-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--secondary-color), var(--accent-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    transition: var(--transition);
}

.category-card:hover .category-icon {
    transform: scale(1.1) rotate(5deg);
}

.category-icon i {
    font-size: 2rem;
    color: var(--white);
}

.category-card h3 {
    font-size: 1.4rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 12px;
}

.category-card p {
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: 20px;
}

.category-stats {
    display: flex;
    justify-content: center;
    gap: 20px;
}

.article-count {
    background: rgba(192, 165, 213, 0.1);
    color: var(--accent-color);
    padding: 6px 16px;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 500;
}

/* KB Articles */
.kb-articles {
    padding: var(--section-padding);
    background: transparent;
}

.articles-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 30px;
    margin-top: 60px;
}

.article-card {
    background: var(--card-background);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 25px;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.article-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(135deg, var(--secondary-color), var(--accent-color));
    transform: scaleY(0);
    transition: transform 0.3s ease;
}

.article-card:hover::before {
    transform: scaleY(1);
}

.article-card:hover {
    transform: translateY(-5px);
    border-color: var(--accent-color);
    box-shadow: 0 15px 30px rgba(108, 91, 185, 0.15);
}

.article-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.article-category {
    background: rgba(192, 165, 213, 0.1);
    color: var(--accent-color);
    padding: 4px 12px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 500;
}

.article-date {
    color: var(--text-muted);
    font-size: 0.9rem;
}

.article-title {
    font-size: 1.3rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 12px;
    line-height: 1.4;
}

.article-excerpt {
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: 20px;
}

.article-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.read-time {
    display: flex;
    align-items: center;
    gap: 6px;
    color: var(--text-muted);
    font-size: 0.9rem;
}

.read-time i {
    font-size: 0.8rem;
}

.read-more {
    color: var(--accent-color);
    text-decoration: none;
    font-weight: 500;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 6px;
}

.read-more:hover {
    color: var(--white);
    transform: translateX(3px);
}

.read-more::after {
    content: '\f061';
    font-family: 'Font Awesome 6 Free';
    font-weight: 900;
    font-size: 0.8rem;
    transition: var(--transition);
}

/* KB FAQ */
.kb-faq {
    padding: var(--section-padding);
    background: transparent;
}

.faq-container {
    max-width: 800px;
    margin: 60px auto 0;
}

.faq-item {
    background: var(--card-background);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    margin-bottom: 20px;
    overflow: hidden;
    transition: var(--transition);
}

.faq-item:hover {
    border-color: var(--accent-color);
}

.faq-question {
    padding: 25px;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: var(--transition);
}

.faq-question:hover {
    background: var(--background-tertiary);
}

.faq-question h3 {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.faq-question i {
    color: var(--accent-color);
    transition: transform 0.3s ease;
}

.faq-item.active .faq-question i {
    transform: rotate(180deg);
}

.faq-answer {
    padding: 0 25px;
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease, padding 0.3s ease;
}

.faq-item.active .faq-answer {
    padding: 0 25px 25px;
    max-height: 200px;
}

.faq-answer p {
    color: var(--text-secondary);
    line-height: 1.6;
    margin: 0;
}

/* KB Support */
.kb-support {
    padding: var(--section-padding);
    background: transparent;
}

.support-content {
    display: grid;
    grid-template-columns: 1fr auto;
    gap: 60px;
    align-items: center;
    background: var(--card-background);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 60px;
    position: relative;
    overflow: hidden;
}

.support-content::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(108, 91, 185, 0.05), rgba(192, 165, 213, 0.05));
    pointer-events: none;
}

.support-text h2 {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 16px;
}

.support-text p {
    color: var(--text-secondary);
    font-size: 1.1rem;
    line-height: 1.6;
    margin-bottom: 30px;
}

.support-actions {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
}

.support-visual {
    position: relative;
}

.support-icon {
    width: 120px;
    height: 120px;
    background: linear-gradient(135deg, var(--secondary-color), var(--accent-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

.support-icon::before {
    content: '';
    position: absolute;
    top: -10px;
    left: -10px;
    right: -10px;
    bottom: -10px;
    background: linear-gradient(135deg, var(--secondary-color), var(--accent-color));
    border-radius: 50%;
    opacity: 0.2;
    animation: pulse 2s infinite;
}

.support-icon i {
    font-size: 3rem;
    color: var(--white);
    z-index: 1;
    position: relative;
}

@keyframes pulse {
    0% {
        transform: scale(1);
        opacity: 0.2;
    }
    50% {
        transform: scale(1.1);
        opacity: 0.1;
    }
    100% {
        transform: scale(1);
        opacity: 0.2;
    }
}

/* ===== KNOWLEDGEBASE RESPONSIVE STYLES ===== */
@media (max-width: 1024px) {
    .categories-grid {
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 25px;
    }

    .articles-grid {
        grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
        gap: 25px;
    }

    .support-content {
        grid-template-columns: 1fr;
        gap: 40px;
        text-align: center;
        padding: 40px;
    }

    .support-visual {
        order: -1;
    }
}

@media (max-width: 768px) {
    .search-box {
        flex-direction: column;
        gap: 15px;
        padding: 20px;
        border-radius: 20px;
    }

    .search-box input {
        padding: 16px 20px;
        border: 1px solid var(--border-color);
        border-radius: 12px;
        background: var(--background-tertiary);
    }

    .search-btn {
        width: 100%;
        border-radius: 12px;
    }

    .categories-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .category-card {
        padding: 25px;
    }

    .articles-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .article-card {
        padding: 20px;
    }

    .article-meta {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .article-footer {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .faq-question {
        padding: 20px;
    }

    .faq-item.active .faq-answer {
        padding: 0 20px 20px;
    }

    .support-content {
        padding: 30px;
    }

    .support-text h2 {
        font-size: 1.6rem;
    }

    .support-actions {
        justify-content: center;
    }

    .support-icon {
        width: 100px;
        height: 100px;
    }

    .support-icon i {
        font-size: 2.5rem;
    }
}

@media (max-width: 480px) {
    .kb-search {
        padding: 40px 0;
    }

    .search-container {
        padding: 0 10px;
    }

    .search-box {
        padding: 15px;
    }

    .search-box input {
        font-size: 1rem;
        padding: 14px 16px;
    }

    .search-btn {
        padding: 14px 24px;
        font-size: 0.9rem;
    }

    .category-card {
        padding: 20px;
    }

    .category-icon {
        width: 60px;
        height: 60px;
    }

    .category-icon i {
        font-size: 1.5rem;
    }

    .category-card h3 {
        font-size: 1.2rem;
    }

    .article-card {
        padding: 16px;
    }

    .article-title {
        font-size: 1.1rem;
    }

    .article-excerpt {
        font-size: 0.9rem;
    }

    .faq-question {
        padding: 16px;
    }

    .faq-question h3 {
        font-size: 1rem;
    }

    .faq-item.active .faq-answer {
        padding: 0 16px 16px;
    }

    .support-content {
        padding: 20px;
    }

    .support-text h2 {
        font-size: 1.4rem;
    }

    .support-text p {
        font-size: 1rem;
    }

    .support-actions {
        flex-direction: column;
        width: 100%;
    }

    .support-actions .btn {
        width: 100%;
        text-align: center;
    }

    .support-icon {
        width: 80px;
        height: 80px;
    }

    .support-icon i {
        font-size: 2rem;
    }
}

/* ===== CLIENT AREA SPECIFIC STYLES ===== */

/* Authentication Container Enhancements */
.auth-container {
    position: relative;
    overflow: hidden;
}

.auth-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg,
        rgba(108, 91, 185, 0.1) 0%,
        rgba(192, 165, 213, 0.05) 100%);
    border-radius: var(--border-radius);
    z-index: -1;
}

/* Form Styling Enhancements */
.form-input::placeholder {
    color: var(--text-muted);
}

.form-input:invalid {
    border-color: var(--text-muted);
}

.form-input:valid {
    border-color: var(--accent-color);
}

/* Checkbox Styling */
.checkbox-container {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-size: 14px;
    color: var(--text-secondary);
    gap: 10px;
}

.checkbox-container input[type="checkbox"] {
    appearance: none;
    width: 18px;
    height: 18px;
    border: 2px solid var(--border-color);
    border-radius: 4px;
    background: var(--background-tertiary);
    cursor: pointer;
    position: relative;
    transition: var(--transition);
}

.checkbox-container input[type="checkbox"]:checked {
    background: var(--secondary-color);
    border-color: var(--secondary-color);
}

.checkbox-container input[type="checkbox"]:checked::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: var(--white);
    font-size: 12px;
    font-weight: bold;
}

.terms-link {
    color: var(--accent-color);
    text-decoration: none;
}

.terms-link:hover {
    text-decoration: underline;
}

/* Loading States */
.btn.loading {
    position: relative;
    color: transparent;
}

.btn.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 20px;
    height: 20px;
    border: 2px solid transparent;
    border-top: 2px solid var(--white);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: translate(-50%, -50%) rotate(0deg); }
    100% { transform: translate(-50%, -50%) rotate(360deg); }
}

/* Error Messages */
.error-message {
    background: rgba(108, 91, 185, 0.1);
    border: 1px solid rgba(108, 91, 185, 0.3);
    color: var(--text-secondary);
    padding: 12px 16px;
    border-radius: var(--border-radius);
    margin-bottom: 20px;
    font-size: 14px;
    display: none;
    border-left: 4px solid var(--secondary-color);
}

.error-message.show {
    display: block;
    animation: slideDown 0.3s ease;
}

.success-message {
    background: rgba(192, 165, 213, 0.1);
    border: 1px solid rgba(192, 165, 213, 0.3);
    color: var(--accent-color);
    padding: 12px 16px;
    border-radius: var(--border-radius);
    margin-bottom: 20px;
    font-size: 14px;
    display: none;
    border-left: 4px solid var(--accent-color);
}

.success-message.show {
    display: block;
    animation: slideDown 0.3s ease;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Password Strength Indicator */
.password-strength {
    margin-top: 8px;
    height: 4px;
    background: var(--background-tertiary);
    border-radius: 2px;
    overflow: hidden;
    display: none;
}

.password-strength.show {
    display: block;
}

.password-strength-bar {
    height: 100%;
    width: 0%;
    transition: all 0.3s ease;
    border-radius: 2px;
}

.password-strength-bar.weak {
    width: 33%;
    background: var(--text-muted);
}

.password-strength-bar.medium {
    width: 66%;
    background: var(--secondary-color);
}

.password-strength-bar.strong {
    width: 100%;
    background: var(--accent-color);
}

.password-strength-text {
    font-size: 12px;
    margin-top: 4px;
    color: var(--text-muted);
}

/* Social Login Buttons */
.social-login {
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid var(--border-color);
}

.social-login-title {
    text-align: center;
    color: var(--text-muted);
    font-size: 14px;
    margin-bottom: 15px;
}

.social-buttons {
    display: flex;
    gap: 10px;
}

.social-btn {
    flex: 1;
    padding: 12px;
    border: 1px solid var(--border-color);
    background: var(--background-tertiary);
    color: var(--text-primary);
    border-radius: 8px;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    font-size: 14px;
    text-decoration: none;
}

.social-btn:hover {
    background: var(--background-secondary);
    border-color: var(--accent-color);
}

.social-btn.google {
    color: var(--accent-color);
}

.social-btn.microsoft {
    color: var(--secondary-color);
}

/* User Menu */
.user-menu {
    position: relative;
    margin-left: 20px;
}

.user-menu-toggle {
    display: flex;
    align-items: center;
    gap: 8px;
    background: none;
    border: 1px solid var(--border-color);
    color: var(--text-primary);
    padding: 8px 12px;
    border-radius: 20px;
    cursor: pointer;
    transition: var(--transition);
    font-size: 14px;
}

.user-menu-toggle:hover {
    background: var(--background-tertiary);
    border-color: var(--accent-color);
}

.user-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    background: var(--card-background);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 8px 0;
    min-width: 200px;
    box-shadow: var(--box-shadow);
    backdrop-filter: blur(10px);
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    z-index: 1000;
}

.user-dropdown.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 10px 16px;
    color: var(--text-primary);
    text-decoration: none;
    transition: var(--transition);
    font-size: 14px;
}

.dropdown-item:hover {
    background: var(--background-tertiary);
    color: var(--accent-color);
}

.dropdown-divider {
    height: 1px;
    background: var(--border-color);
    margin: 8px 0;
}

/* Stats Grid */
.stats-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.stat-item {
    text-align: center;
    padding: 15px;
    background: var(--background-tertiary);
    border-radius: 8px;
    border: 1px solid var(--border-color);
}

.stat-number {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--accent-color);
    margin-bottom: 5px;
}

.stat-label {
    font-size: 0.85rem;
    color: var(--text-muted);
}

/* Quick Actions */
.quick-actions {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.action-btn {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 12px 16px;
    background: var(--background-tertiary);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    color: var(--text-primary);
    cursor: pointer;
    transition: var(--transition);
    font-size: 14px;
    text-align: left;
}

.action-btn:hover {
    background: var(--background-secondary);
    border-color: var(--accent-color);
    color: var(--accent-color);
}

/* ===== WHMCS DASHBOARD LAYOUT ===== */

.whmcs-dashboard {
    padding: 100px 0 40px;
    min-height: 100vh;
}

.whmcs-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.whmcs-layout {
    display: grid;
    grid-template-columns: 280px 1fr;
    gap: 30px;
    align-items: start;
}

/* WHMCS Sidebar */
.whmcs-sidebar {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.sidebar-section {
    background: var(--card-background);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    overflow: hidden;
    backdrop-filter: blur(10px);
}

.sidebar-header {
    background: var(--background-secondary);
    padding: 12px 16px;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
    color: var(--text-primary);
    font-size: 14px;
}

.sidebar-header i {
    color: var(--accent-color);
    font-size: 16px;
}

/* User Info Section */
.user-info {
    padding: 16px;
}

.user-name {
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 8px;
    font-size: 16px;
}

.user-details {
    margin-bottom: 16px;
}

.user-details div {
    color: var(--text-secondary);
    font-size: 13px;
    margin-bottom: 2px;
}

.update-btn {
    background: linear-gradient(135deg, var(--secondary-color), var(--accent-color));
    color: var(--white);
    border: none;
    padding: 8px 16px;
    border-radius: var(--border-radius);
    cursor: pointer;
    font-size: 12px;
    display: flex;
    align-items: center;
    gap: 6px;
    transition: var(--transition);
    width: 100%;
    justify-content: center;
    font-weight: 600;
}

.update-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(108, 91, 185, 0.3);
}

/* Contacts Section */
.contacts-content {
    padding: 16px;
    text-align: center;
}

.no-contacts {
    color: var(--text-muted);
    font-size: 13px;
    margin-bottom: 12px;
}

.new-contact-btn {
    background: transparent;
    border: 2px solid var(--accent-color);
    color: var(--accent-color);
    padding: 8px 12px;
    border-radius: var(--border-radius);
    cursor: pointer;
    font-size: 12px;
    display: flex;
    align-items: center;
    gap: 6px;
    transition: var(--transition);
    width: 100%;
    justify-content: center;
    font-weight: 500;
}

.new-contact-btn:hover {
    background: var(--accent-color);
    color: var(--primary-color);
    transform: translateY(-2px);
}

/* Shortcuts Section */
.shortcuts-content {
    padding: 8px 0;
}

.shortcut-item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 10px 16px;
    color: var(--text-secondary);
    text-decoration: none;
    font-size: 13px;
    transition: var(--transition);
    border-bottom: 1px solid var(--border-color);
}

.shortcut-item:last-child {
    border-bottom: none;
}

.shortcut-item:hover {
    background: var(--background-tertiary);
    color: var(--accent-color);
}

.shortcut-item i {
    color: var(--accent-color);
    width: 16px;
    text-align: center;
}

/* Main Content Area */
.whmcs-main-content {
    display: flex;
    flex-direction: column;
    gap: 25px;
}

/* Welcome Header */
.welcome-header h1 {
    font-size: 2rem;
    color: var(--text-primary);
    margin-bottom: 8px;
    font-weight: 400;
}

.breadcrumb {
    font-size: 13px;
    color: var(--text-muted);
}

.breadcrumb a {
    color: var(--accent-color);
    text-decoration: none;
}

.breadcrumb a:hover {
    text-decoration: underline;
}

/* Stats Cards */
.stats-cards {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
}

.stat-card {
    background: var(--card-background);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 20px;
    display: flex;
    align-items: center;
    gap: 15px;
    backdrop-filter: blur(10px);
    transition: var(--transition);
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.stat-card:hover {
    border-color: var(--accent-color);
    background: var(--background-secondary);
    transform: translateY(-2px);
    box-shadow: 0 15px 35px rgba(108, 91, 185, 0.2);
}

.stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
}

.stat-card:nth-child(1) .stat-icon {
    background: rgba(108, 91, 185, 0.1);
    color: var(--secondary-color);
}

.stat-card:nth-child(2) .stat-icon {
    background: rgba(52, 152, 219, 0.1);
    color: #3498db;
}

.stat-card:nth-child(3) .stat-icon {
    background: rgba(231, 76, 60, 0.1);
    color: #e74c3c;
}

.stat-card:nth-child(4) .stat-icon {
    background: rgba(243, 156, 18, 0.1);
    color: #f39c12;
}

.stat-content {
    flex: 1;
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 4px;
}

.stat-label {
    font-size: 12px;
    color: var(--text-muted);
    font-weight: 600;
    letter-spacing: 0.5px;
}

/* Search Section */
.search-section {
    margin: 10px 0;
}

.search-bar {
    position: relative;
    max-width: 600px;
}

.search-bar i {
    position: absolute;
    left: 16px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-muted);
    font-size: 16px;
}

.search-bar input {
    width: 100%;
    padding: 12px 16px 12px 45px;
    background: var(--card-background);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    color: var(--text-primary);
    font-size: 14px;
    transition: var(--transition);
}

.search-bar input:focus {
    outline: none;
    border-color: var(--accent-color);
    box-shadow: 0 0 0 3px rgba(192, 165, 213, 0.1);
    transform: translateY(-1px);
}

.search-bar input::placeholder {
    color: var(--text-muted);
}

/* Content Sections */
.content-sections {
    display: flex;
    flex-direction: column;
    gap: 25px;
}

.content-section {
    background: var(--card-background);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    overflow: hidden;
    backdrop-filter: blur(10px);
    position: relative;
}

.section-header {
    background: var(--background-secondary);
    padding: 12px 16px;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.section-title {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
    color: var(--text-primary);
    font-size: 14px;
}

.section-title i {
    color: var(--accent-color);
    font-size: 16px;
}

.my-services-btn,
.open-ticket-btn,
.view-all-btn {
    background: linear-gradient(135deg, var(--secondary-color), var(--accent-color));
    color: var(--white);
    border: none;
    padding: 6px 12px;
    border-radius: var(--border-radius);
    cursor: pointer;
    font-size: 12px;
    transition: var(--transition);
    font-weight: 600;
    position: relative;
    overflow: hidden;
}

.my-services-btn:hover,
.open-ticket-btn:hover,
.view-all-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(108, 91, 185, 0.3);
}

.section-content {
    padding: 16px;
}

/* No Products/Tickets Messages */
.no-products-message,
.no-tickets-message {
    color: var(--text-secondary);
    font-size: 14px;
    text-align: center;
    padding: 20px;
}

.order-link,
.ticket-link {
    color: var(--accent-color);
    text-decoration: none;
}

.order-link:hover,
.ticket-link:hover {
    text-decoration: underline;
}

/* Two Column Layout */
.two-column-layout {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 25px;
}

.half-width {
    margin: 0;
}

/* Domain Actions */
.domain-actions {
    display: flex;
    gap: 10px;
    justify-content: center;
    padding: 10px 0;
}

.register-btn,
.transfer-btn {
    background: linear-gradient(135deg, var(--secondary-color), var(--accent-color));
    color: var(--white);
    border: none;
    padding: 10px 20px;
    border-radius: var(--border-radius);
    cursor: pointer;
    font-size: 14px;
    transition: var(--transition);
    font-weight: 600;
    position: relative;
    overflow: hidden;
}

.register-btn:hover,
.transfer-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(108, 91, 185, 0.3);
}

/* Mobile Responsiveness for WHMCS Layout */
@media (max-width: 1024px) {
    .whmcs-layout {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .stats-cards {
        grid-template-columns: repeat(2, 1fr);
    }

    .two-column-layout {
        grid-template-columns: 1fr;
        gap: 20px;
    }
}

@media (max-width: 768px) {
    .whmcs-dashboard {
        padding: 80px 0 40px;
    }

    .whmcs-container {
        padding: 0 15px;
    }

    .stats-cards {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .welcome-header h1 {
        font-size: 1.5rem;
    }

    .search-bar {
        max-width: 100%;
    }

    .social-buttons {
        flex-direction: column;
    }

    .client-area-hero {
        padding: 100px 0 60px;
    }

    .auth-container {
        margin: 0 10px;
        padding: 30px 20px;
    }

    .client-area-title {
        font-size: 2.5rem;
    }

    .client-area-subtitle {
        font-size: 1rem;
    }
}

@media (max-width: 480px) {
    .whmcs-container {
        padding: 0 10px;
    }

    .welcome-header h1 {
        font-size: 1.5rem;
    }

    .stat-card {
        padding: 15px;
    }

    .stat-icon {
        width: 40px;
        height: 40px;
        font-size: 20px;
    }

    .stat-number {
        font-size: 1.5rem;
    }

    .auth-tabs {
        flex-direction: column;
    }

    .auth-tab {
        padding: 15px;
    }

    .client-area-title {
        font-size: 2rem;
    }
}

/* Focus States for Accessibility */
.auth-tab:focus,
.form-input:focus,
.btn:focus,
.checkbox-container input:focus,
.stat-card:focus,
.shortcut-item:focus,
.user-menu-toggle:focus {
    outline: 2px solid var(--accent-color);
    outline-offset: 2px;
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
    .form-input {
        border-width: 2px;
    }

    .btn {
        border: 2px solid var(--accent-color);
    }

    .auth-tab.active {
        border: 2px solid var(--white);
    }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
    .stat-card:hover,
    .content-section:hover {
        transform: none;
    }

    .loading-spinner {
        animation: none;
    }
}

/* Print Styles */
@media print {
    .navbar,
    .modern-gradient-background,
    .loading-overlay {
        display: none;
    }

    .whmcs-dashboard {
        padding: 0;
    }

    .whmcs-layout {
        display: block;
    }

    .sidebar-section,
    .content-section {
        break-inside: avoid;
        margin-bottom: 20px;
    }
}
