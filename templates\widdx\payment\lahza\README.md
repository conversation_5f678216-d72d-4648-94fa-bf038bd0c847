# Lahza.io Payment Gateway for WHMCS WIDDX Template

## نظرة عامة | Overview

بوابة دفع Lahza.io المحسنة لقالب WHMCS WIDDX مع دعم شامل للعملات المحلية والميزات الحديثة.

Enhanced Lahza.io payment gateway for WHMCS WIDDX template with comprehensive support for local currencies and modern features.

## الميزات الرئيسية | Key Features

### 🚀 **الميزات التقنية | Technical Features**
- ✅ دعم العملات: ILS, USD, JOD | Currency Support: ILS, USD, JOD
- ✅ طريقتان للدفع: Popup و Redirect | Two Payment Methods: Popup & Redirect
- ✅ دعم Webhooks للتحديثات الفورية | Webhook Support for Real-time Updates
- ✅ تشفير SSL 256-bit للأمان | 256-bit SSL Encryption for Security
- ✅ واجهة مستخدم حديثة ومتجاوبة | Modern Responsive UI/UX
- ✅ دعم الوضع المظلم والفاتح | Dark/Light Mode Support

### 🌍 **الدعم متعدد اللغات | Multi-language Support**
- ✅ دعم كامل للغة العربية (RTL) | Full Arabic Language Support (RTL)
- ✅ دعم العبرية والفارسية | Hebrew and Persian Support
- ✅ واجهة ثنائية اللغة | Bilingual Interface

### 🎨 **التصميم والتخصيص | Design & Customization**
- ✅ متوافق مع قالب WIDDX | WIDDX Template Compatible
- ✅ CSS قابل للتخصيص | Customizable CSS
- ✅ رسوم متحركة حديثة | Modern Animations
- ✅ تصميم متجاوب بالكامل | Fully Responsive Design

## التثبيت والإعداد | Installation & Setup

### 1. **رفع الملفات | Upload Files**

```bash
# رفع ملفات بوابة الدفع
modules/gateways/lahza.php
modules/gateways/callback/lahza.php

# رفع ملفات القالب المخصص
templates/widdx/payment/lahza/payment-form.tpl
templates/widdx/payment/lahza/lahza-payment.css
templates/widdx/payment/lahza/lahza-payment.js
```

### 2. **تفعيل البوابة | Activate Gateway**

1. اذهب إلى لوحة تحكم WHMCS | Go to WHMCS Admin Panel
2. **Setup → Payments → Payment Gateways**
3. ابحث عن "Lahza.io Payment Gateway" | Find "Lahza.io Payment Gateway"
4. اضغط **Activate** | Click **Activate**

### 3. **الإعدادات المطلوبة | Required Configuration**

| الإعداد | Setting | الوصف | Description |
|---------|---------|--------|-------------|
| **Public Key** | مفتاح عام | `pk_test_...` أو `pk_live_...` | Test or Live public key |
| **Secret Key** | مفتاح سري | `sk_test_...` أو `sk_live_...` | Test or Live secret key |
| **Test Mode** | وضع التجربة | تفعيل للاختبار | Enable for testing |
| **Payment Method** | طريقة الدفع | Popup (مُوصى) أو Redirect | Popup (Recommended) or Redirect |

### 4. **الإعدادات الاختيارية | Optional Configuration**

| الإعداد | Setting | القيمة الافتراضية | Default Value |
|---------|---------|------------------|---------------|
| **Allowed Channels** | القنوات المسموحة | `card,bank,mobile_money` | Payment channels |
| **Webhook URL** | رابط Webhook | تلقائي | Auto-generated |
| **Custom CSS** | CSS مخصص | فارغ | Custom styling |
| **Enable Logging** | تفعيل السجلات | لا | Debug logging |

## استخدام البوابة | Using the Gateway

### **للعملاء | For Customers**

1. **اختيار المنتج/الخدمة** | Choose Product/Service
2. **إنشاء الفاتورة** | Generate Invoice
3. **اختيار Lahza.io كطريقة دفع** | Select Lahza.io as Payment Method
4. **إكمال الدفع بأمان** | Complete Secure Payment

### **طرق الدفع المدعومة | Supported Payment Methods**

- 💳 **البطاقات الائتمانية** | Credit/Debit Cards
- 🏦 **التحويل البنكي** | Bank Transfer
- 📱 **المحافظ الرقمية** | Mobile Money
- 📲 **رمز QR** | QR Code Payments

## الأمان والحماية | Security & Protection

### 🔒 **ميزات الأمان | Security Features**

- **تشفير SSL 256-bit** | 256-bit SSL Encryption
- **التحقق من التوقيع** | Signature Verification
- **حماية CSRF** | CSRF Protection
- **التحقق من IP** | IP Whitelisting
- **سجلات مفصلة** | Detailed Logging

### 🛡️ **حماية البيانات | Data Protection**

- عدم تخزين بيانات البطاقات | No Card Data Storage
- تشفير البيانات الحساسة | Sensitive Data Encryption
- امتثال لمعايير PCI DSS | PCI DSS Compliance

## استكشاف الأخطاء | Troubleshooting

### ❌ **المشاكل الشائعة | Common Issues**

#### 1. **خطأ "Module Not Activated"**
```php
// الحل: تأكد من تفعيل البوابة في WHMCS
// Solution: Ensure gateway is activated in WHMCS
Setup → Payments → Payment Gateways → Lahza.io → Activate
```

#### 2. **خطأ "Invalid Public/Secret Key"**
```php
// الحل: تحقق من المفاتيح في لوحة تحكم Lahza.io
// Solution: Verify keys in Lahza.io dashboard
- Test Mode: pk_test_... / sk_test_...
- Live Mode: pk_live_... / sk_live_...
```

#### 3. **فشل التحقق من الدفع**
```php
// الحل: تحقق من إعدادات Webhook
// Solution: Check webhook configuration
Webhook URL: https://yourdomain.com/modules/gateways/callback/lahza.php
```

### 🔧 **تفعيل السجلات | Enable Logging**

```php
// في إعدادات البوابة
// In gateway settings
Enable Logging: Yes

// عرض السجلات
// View logs
Utilities → Logs → Gateway Log
```

## التخصيص المتقدم | Advanced Customization

### 🎨 **تخصيص CSS**

```css
/* إضافة في Custom CSS */
/* Add to Custom CSS */
.lahza-payment-container {
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.lahza-pay-btn {
    background: linear-gradient(45deg, #your-color1, #your-color2);
}
```

### 🔧 **تخصيص JavaScript**

```javascript
// تخصيص سلوك الدفع
// Customize payment behavior
window.lahzaPaymentData = {
    // إعدادات مخصصة
    // Custom settings
};
```

## الدعم والمساعدة | Support & Help

### 📞 **قنوات الدعم | Support Channels**

- 🌐 **الموقع الرسمي** | Official Website: [lahza.io](https://lahza.io)
- 📚 **التوثيق** | Documentation: [docs.lahza.io](https://docs.lahza.io)
- 💬 **الدعم الفني** | Technical Support: [<EMAIL>](mailto:<EMAIL>)

### 🔗 **روابط مفيدة | Useful Links**

- [Lahza.io Dashboard](https://dashboard.lahza.io)
- [API Documentation](https://api-docs.lahza.io)
- [Status Page](https://statuspage.freshping.io/61673-Lahza)

## الترخيص | License

هذا المشروع مرخص تحت رخصة MIT - انظر ملف [LICENSE](LICENSE) للتفاصيل.

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## المساهمة | Contributing

نرحب بالمساهمات! يرجى قراءة [CONTRIBUTING.md](CONTRIBUTING.md) للتفاصيل.

We welcome contributions! Please read [CONTRIBUTING.md](CONTRIBUTING.md) for details.

---

**تم التطوير بواسطة فريق WIDDX** | **Developed by WIDDX Team**

© 2024 WIDDX. جميع الحقوق محفوظة | All rights reserved.
