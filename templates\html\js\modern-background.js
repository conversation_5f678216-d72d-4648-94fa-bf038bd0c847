// ===== MODERN GRADIENT BACKGROUND SYSTEM =====

class ModernBackground {
    constructor() {
        this.particles = [];
        this.particleCount = 50;
        this.animationId = null;
        this.isInitialized = false;
        this.performanceMode = this.detectPerformanceMode();

        this.init();
    }

    detectPerformanceMode() {
        // Detect device performance capabilities
        const canvas = document.createElement('canvas');
        const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');

        if (!gl) return 'low';

        const debugInfo = gl.getExtension('WEBGL_debug_renderer_info');
        if (debugInfo) {
            const renderer = gl.getParameter(debugInfo.UNMASKED_RENDERER_WEBGL);
            if (renderer.includes('Intel') || renderer.includes('Mobile')) {
                return 'medium';
            }
        }

        // Check for reduced motion preference
        if (window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
            return 'low';
        }

        return 'high';
    }

    init() {
        if (this.isInitialized) return;

        this.createBackgroundStructure();
        this.initializeParticles();
        this.startAnimation();
        this.setupEventListeners();

        this.isInitialized = true;
        console.log('🎨 Modern background system initialized');
    }

    createBackgroundStructure() {
        // Check if background already exists
        if (document.querySelector('.modern-gradient-background')) return;

        const backgroundContainer = document.createElement('div');
        backgroundContainer.className = 'modern-gradient-background';

        // Create gradient layers
        for (let i = 1; i <= 3; i++) {
            const layer = document.createElement('div');
            layer.className = `gradient-layer-${i}`;
            backgroundContainer.appendChild(layer);
        }

        // Create gradient spheres (only in high performance mode)
        if (this.performanceMode === 'high') {
            for (let i = 1; i <= 4; i++) {
                const sphere = document.createElement('div');
                sphere.className = `gradient-sphere gradient-sphere-${i}`;
                backgroundContainer.appendChild(sphere);
            }
        }

        // Create wave element (medium and high performance)
        if (this.performanceMode !== 'low') {
            const waveElement = document.createElement('div');
            waveElement.className = 'wave-element';
            backgroundContainer.appendChild(waveElement);
        }

        // Create particle system container
        const particleSystem = document.createElement('div');
        particleSystem.className = 'particle-system';
        backgroundContainer.appendChild(particleSystem);

        // Create noise texture overlay
        const noiseTexture = document.createElement('div');
        noiseTexture.className = 'noise-texture';
        backgroundContainer.appendChild(noiseTexture);

        // Insert at the beginning of body
        document.body.insertBefore(backgroundContainer, document.body.firstChild);
    }

    initializeParticles() {
        const particleSystem = document.querySelector('.particle-system');
        if (!particleSystem) return;

        // Adjust particle count based on performance
        const counts = {
            low: 15,
            medium: 30,
            high: 50
        };

        this.particleCount = counts[this.performanceMode];

        // Clear existing particles
        particleSystem.innerHTML = '';
        this.particles = [];

        for (let i = 0; i < this.particleCount; i++) {
            this.createParticle(particleSystem, i);
        }
    }

    createParticle(container, index) {
        const particle = document.createElement('div');
        particle.className = 'particle';

        // Random starting position
        const startX = Math.random() * window.innerWidth;
        const animationDuration = 15 + Math.random() * 20; // 15-35 seconds
        const animationDelay = Math.random() * 10; // 0-10 seconds delay
        const horizontalDrift = (Math.random() - 0.5) * 100; // -50px to 50px drift

        particle.style.left = startX + 'px';
        particle.style.animationDuration = animationDuration + 's';
        particle.style.animationDelay = animationDelay + 's';
        particle.style.setProperty('--horizontal-drift', horizontalDrift + 'px');

        // Add some variety to particle appearance
        const opacity = 0.3 + Math.random() * 0.4; // 0.3 to 0.7
        const size = 1 + Math.random() * 2; // 1px to 3px

        particle.style.opacity = opacity;
        particle.style.width = size + 'px';
        particle.style.height = size + 'px';

        container.appendChild(particle);

        this.particles.push({
            element: particle,
            x: startX,
            duration: animationDuration,
            delay: animationDelay
        });
    }

    startAnimation() {
        if (this.performanceMode === 'low') return; // Skip complex animations for low performance

        this.animateParticles();
    }

    animateParticles() {
        // This function can be used for additional JavaScript-based animations
        // Currently, we're using CSS animations for better performance

        // Optional: Add mouse interaction effects
        this.setupMouseInteraction();
    }

    setupMouseInteraction() {
        if (this.performanceMode === 'low') return;

        this.mouseX = 0;
        this.mouseY = 0;
        this.isMouseInteractionActive = false;

        this.mouseMoveHandler = (e) => {
            this.mouseX = e.clientX;
            this.mouseY = e.clientY;
            if (!this.isMouseInteractionActive) {
                this.isMouseInteractionActive = true;
                requestAnimationFrame(() => this.updateParticlesOnMouseMove());
            }
        };

        document.addEventListener('mousemove', this.mouseMoveHandler);
    }

    updateParticlesOnMouseMove() {
        if (!this.isMouseInteractionActive) return;

        const maxDistance = 150;

        this.particles.forEach((p, index) => {
            if (index % 3 !== 0) return;

            const rect = p.element.getBoundingClientRect();
            const particleX = rect.left + rect.width / 2;
            const particleY = rect.top + rect.height / 2;

            const distance = Math.sqrt(
                Math.pow(this.mouseX - particleX, 2) + Math.pow(this.mouseY - particleY, 2)
            );

            if (distance < maxDistance) {
                const force = (maxDistance - distance) / maxDistance;
                const angle = Math.atan2(this.mouseY - particleY, this.mouseX - particleX);
                const offsetX = Math.cos(angle) * force * -30; // Move away from cursor
                const offsetY = Math.sin(angle) * force * -30;

                p.element.style.transform = `translate(${offsetX}px, ${offsetY}px)`;
                p.element.style.transition = 'transform 0.1s ease-out';
            } else {
                p.element.style.transform = 'translate(0, 0)';
                p.element.style.transition = 'transform 0.5s ease-in-out';
            }
        });

        this.isMouseInteractionActive = false;
    }

    setupEventListeners() {
        // Handle theme changes
        window.addEventListener('themeChanged', (e) => {
            this.handleThemeChange(e.detail.theme);
        });

        // Handle window resize
        let resizeTimeout;
        window.addEventListener('resize', () => {
            clearTimeout(resizeTimeout);
            resizeTimeout = setTimeout(() => {
                this.handleResize();
            }, 250);
        });

        // Handle visibility change for performance
        document.addEventListener('visibilitychange', () => {
            this.handleVisibilityChange();
        });
    }

    handleThemeChange(theme) {
        const background = document.querySelector('.modern-gradient-background');
        if (!background) return;

        console.log('🎨 Background system handling theme change to:', theme);

        // Add a smooth transition class
        background.style.transition = 'all 0.5s ease';

        // Update particles for theme
        this.updateParticlesForTheme(theme);

        // Remove transition after animation
        setTimeout(() => {
            background.style.transition = '';
        }, 500);
    }

    updateParticlesForTheme(theme) {
        const particles = document.querySelectorAll('.particle');
        particles.forEach(particle => {
            if (theme === 'light') {
                particle.style.background = 'rgba(108, 91, 185, 0.4)';
            } else {
                particle.style.background = 'rgba(192, 165, 213, 0.6)';
            }
        });
    }

    handleResize() {
        // Re-detect performance and rebuild everything for robustness
        this.performanceMode = this.detectPerformanceMode();
        
        const background = document.querySelector('.modern-gradient-background');
        if (background) {
            background.remove();
        }
        
        this.createBackgroundStructure();
        this.initializeParticles();
    }

    handleVisibilityChange() {
        const background = document.querySelector('.modern-gradient-background');
        if (!background) return;

        if (document.hidden) {
            // Pause animations when tab is not visible
            background.style.animationPlayState = 'paused';
        } else {
            // Resume animations when tab becomes visible
            background.style.animationPlayState = 'running';
        }
    }

    destroy() {
        if (this.animationId) {
            cancelAnimationFrame(this.animationId);
        }

        const background = document.querySelector('.modern-gradient-background');
        if (background) {
            background.remove();
        }

        this.isInitialized = false;
    }
}

// Initialize the modern background system
let modernBackground;

function initModernBackground() {
    if (!modernBackground) {
        modernBackground = new ModernBackground();
    }
}

// Auto-initialize when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initModernBackground);
} else {
    initModernBackground();
}

// Export for manual initialization if needed
window.ModernBackground = ModernBackground;
window.initModernBackground = initModernBackground;
