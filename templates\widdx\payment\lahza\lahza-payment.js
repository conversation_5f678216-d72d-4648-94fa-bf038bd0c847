/**
 * Lahza.io Payment Gateway JavaScript for WHMCS WIDDX Template
 * 
 * Enhanced JavaScript for better user experience and error handling
 * Supports both popup and redirect payment methods
 * 
 * <AUTHOR> Development Team
 * @version 2.0.0
 */

class LahzaPaymentHandler {
    constructor(config) {
        this.config = {
            enableLogging: false,
            retryAttempts: 3,
            retryDelay: 1000,
            ...config
        };
        
        this.payButton = null;
        this.errorElement = null;
        this.loadingElement = null;
        this.errorMessageSpan = null;
        this.originalButtonText = '';
        this.isProcessing = false;
        
        this.init();
    }
    
    init() {
        // Wait for DOM to be ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.setupElements());
        } else {
            this.setupElements();
        }
    }
    
    setupElements() {
        this.payButton = document.getElementById('lahza-pay-btn');
        this.errorElement = document.getElementById('lahza-payment-errors');
        this.loadingElement = document.getElementById('lahza-payment-loading');
        this.errorMessageSpan = this.errorElement?.querySelector('.error-message');
        
        if (this.payButton) {
            this.originalButtonText = this.payButton.innerHTML;
            this.payButton.addEventListener('click', (e) => this.handlePayment(e));
        }
        
        this.log('Payment handler initialized');
    }
    
    log(message, data = null) {
        if (this.config.enableLogging) {
            console.log('[Lahza Payment]', message, data || '');
        }
    }
    
    showError(message) {
        if (this.errorMessageSpan && this.errorElement) {
            this.errorMessageSpan.textContent = message;
            this.errorElement.style.display = 'block';
            this.errorElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
        this.hideLoading();
        this.resetButton();
        this.log('Error shown:', message);
    }
    
    hideError() {
        if (this.errorElement) {
            this.errorElement.style.display = 'none';
        }
    }
    
    showLoading() {
        this.hideError();
        if (this.loadingElement) {
            this.loadingElement.style.display = 'block';
        }
        if (this.payButton) {
            this.payButton.disabled = true;
            this.payButton.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Processing...';
        }
        this.isProcessing = true;
        this.log('Loading state shown');
    }
    
    hideLoading() {
        if (this.loadingElement) {
            this.loadingElement.style.display = 'none';
        }
        this.isProcessing = false;
    }
    
    resetButton() {
        if (this.payButton) {
            this.payButton.disabled = false;
            this.payButton.innerHTML = this.originalButtonText;
        }
    }
    
    validatePaymentData(paymentData) {
        const required = ['key', 'email', 'amount', 'currency'];
        const missing = required.filter(field => !paymentData[field]);
        
        if (missing.length > 0) {
            throw new Error(`Missing required fields: ${missing.join(', ')}`);
        }
        
        if (paymentData.amount <= 0) {
            throw new Error('Invalid payment amount');
        }
        
        if (!['ILS', 'USD', 'JOD'].includes(paymentData.currency)) {
            throw new Error('Unsupported currency');
        }
        
        this.log('Payment data validated', paymentData);
    }
    
    async handlePayment(event) {
        event.preventDefault();
        
        if (this.isProcessing) {
            this.log('Payment already in progress, ignoring click');
            return;
        }
        
        this.showLoading();
        
        try {
            // Check if Lahza library is loaded
            if (typeof LahzaPopup === 'undefined') {
                throw new Error('Lahza payment library not loaded. Please refresh the page and try again.');
            }
            
            // Get payment data from button attributes or global config
            const paymentData = this.getPaymentData();
            this.validatePaymentData(paymentData);
            
            this.log('Initiating payment', paymentData);
            
            // Initialize Lahza popup
            const lahza = new LahzaPopup();
            
            await lahza.newTransaction({
                ...paymentData,
                onSuccess: (transaction) => this.handleSuccess(transaction),
                onCancel: () => this.handleCancel(),
                onError: (error) => this.handleError(error)
            });
            
        } catch (error) {
            this.log('Payment initialization error:', error);
            this.showError(error.message || 'Error initializing payment. Please try again.');
        }
    }
    
    getPaymentData() {
        // Try to get data from global window object first
        if (window.lahzaPaymentData) {
            return window.lahzaPaymentData;
        }
        
        // Fallback to data attributes
        const button = this.payButton;
        if (!button) {
            throw new Error('Payment button not found');
        }
        
        return {
            key: button.dataset.publicKey,
            email: button.dataset.email,
            mobile: button.dataset.mobile,
            firstName: button.dataset.firstName,
            lastName: button.dataset.lastName,
            amount: parseInt(button.dataset.amount),
            currency: button.dataset.currency,
            ref: button.dataset.reference,
            channels: button.dataset.channels ? JSON.parse(button.dataset.channels) : undefined,
            metadata: button.dataset.metadata ? JSON.parse(button.dataset.metadata) : {}
        };
    }
    
    handleSuccess(transaction) {
        this.log('Payment successful:', transaction);
        
        // Add success animation
        if (this.payButton) {
            this.payButton.classList.add('payment-success');
            this.payButton.innerHTML = '<i class="fas fa-check me-2"></i>Payment Successful!';
        }
        
        // Show success message briefly before redirect
        this.hideError();
        this.hideLoading();
        
        // Redirect after a short delay
        setTimeout(() => {
            const returnUrl = this.config.returnUrl || window.lahzaReturnUrl;
            if (returnUrl) {
                const separator = returnUrl.includes('?') ? '&' : '?';
                window.location.href = `${returnUrl}${separator}reference=${transaction.reference}&status=success`;
            } else {
                // Fallback redirect
                window.location.reload();
            }
        }, 1500);
    }
    
    handleCancel() {
        this.log('Payment cancelled by user');
        this.showError('Payment was cancelled. You can try again when ready.');
    }
    
    handleError(error) {
        this.log('Payment error:', error);
        this.showError(error.message || 'An error occurred during payment. Please try again.');
    }
    
    // Retry mechanism for failed payments
    async retryPayment(paymentData, attempt = 1) {
        if (attempt > this.config.retryAttempts) {
            throw new Error('Maximum retry attempts reached. Please try again later.');
        }
        
        this.log(`Payment attempt ${attempt}/${this.config.retryAttempts}`);
        
        try {
            const lahza = new LahzaPopup();
            return await lahza.newTransaction(paymentData);
        } catch (error) {
            if (attempt < this.config.retryAttempts) {
                await this.delay(this.config.retryDelay * attempt);
                return this.retryPayment(paymentData, attempt + 1);
            }
            throw error;
        }
    }
    
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    
    // Public method to update payment data
    updatePaymentData(newData) {
        window.lahzaPaymentData = { ...window.lahzaPaymentData, ...newData };
        this.log('Payment data updated', window.lahzaPaymentData);
    }
    
    // Public method to trigger payment programmatically
    triggerPayment() {
        if (this.payButton) {
            this.payButton.click();
        }
    }
}

// Auto-initialize when script loads
document.addEventListener('DOMContentLoaded', function() {
    // Check if we're on a page with Lahza payment form
    if (document.getElementById('lahza-pay-btn')) {
        window.lahzaPaymentHandler = new LahzaPaymentHandler({
            enableLogging: window.lahzaEnableLogging || false,
            returnUrl: window.lahzaReturnUrl
        });
    }
});

// Utility functions for backward compatibility
window.LahzaUtils = {
    showError: function(message) {
        if (window.lahzaPaymentHandler) {
            window.lahzaPaymentHandler.showError(message);
        }
    },
    
    hideError: function() {
        if (window.lahzaPaymentHandler) {
            window.lahzaPaymentHandler.hideError();
        }
    },
    
    updatePaymentData: function(data) {
        if (window.lahzaPaymentHandler) {
            window.lahzaPaymentHandler.updatePaymentData(data);
        }
    }
};

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = LahzaPaymentHandler;
}
