/*
 * WHMCS WIDDX Theme System
 * Consolidated Theme Variables and Dark/Light Mode Support
 */

/* Import company brand colors and variables */
@import url('variables.css');
@import url("https://fonts.googleapis.com/css2?family=Cairo:wght@200..1000&display=swap");

/* Light Theme Variables (Default) */
:root {
  /* Primary Colors - Using company brand colors */
  --primary: var(--primary-color) !important;
  --primary-dark: var(--primary-dark) !important;
  --primary-darker: var(--primary-dark) !important;
  --primary-light: var(--primary-light) !important;
  --primary-darker-rgb: 58, 40, 112; /* RGB values for primary-dark */

  /* Background Colors */
  --body-bg: var(--white) !important;
  --header-bg: var(--white) !important;
  --sidebar-bg: var(--white) !important;
  --card-bg: var(--white) !important;
  --modal-bg: var(--white) !important;
  --dropdown-bg: var(--white) !important;
  --header-light: var(--white) !important;

  /* Text Colors */
  --text-color: var(--gray-700) !important;
  --text-muted: var(--gray-500) !important;
  --heading-color: var(--gray-900) !important;

  /* UI Element Colors */
  --btn-bg: var(--white) !important;
  --btn-color: var(--gray-900) !important;
  --btn-color-hover: var(--white) !important;
  --btn-color-custom: var(--gray-900) !important;
  --box-hover: var(--white) !important;

  /* Status Colors - Using company brand colors */
  --success: var(--success-color) !important;
  --info: var(--info-color) !important;
  --warning: var(--warning-color) !important;
  --danger: var(--error-color) !important;
  --success-rgb: 16, 185, 129; /* RGB for success-color */
  --info-rgb: 59, 130, 246; /* RGB for info-color */
  --warning-rgb: 245, 158, 11; /* RGB for warning-color */
  --danger-rgb: 239, 68, 68; /* RGB for error-color */
  --worning: var(--warning-color) !important; /* Fix for typo in original variables */

  /* Status Light Colors */
  --success-light: var(--success-light) !important;
  --danger-light: var(--error-light) !important;
  --warning-light: var(--warning-light) !important;

  /* Neutral Colors */
  --gray: var(--gray-500) !important;
  --gray-light: var(--gray-100) !important;
  --gray-dark: var(--gray-800) !important;
  --dark: var(--gray-900) !important;

  /* Border Colors */
  --border-color: var(--gray-200) !important;
  --border-dark-color: var(--primary-dark) !important;
  --border-primary-light: var(--primary-light) !important;

  /* UI Properties */
  --custom-radius: var(--radius-md) !important;
  --box-shadow: var(--shadow-sm) !important;
  --box-shadow-lg: var(--shadow-lg) !important;

  /* Typography */
  --font-family-sans-serif: "Cairo", sans-serif !important;
  --font-family-monospace: "Cairo", monospace !important;
}

/* Dark Theme Variables */
:root[data-bs-theme="dark"] {
  /* Primary Colors - Keep consistent with light theme using company brand colors */
  --primary: var(--primary-color) !important;
  --primary-dark: var(--primary-dark) !important;
  --primary-darker: var(--primary-dark) !important;
  --primary-light: var(--primary-light) !important;
  --primary-darker-rgb: 58, 40, 112; /* RGB values for primary-dark */

  /* Background Colors */
  --body-bg: var(--gray-900) !important;
  --header-bg: var(--gray-800) !important;
  --sidebar-bg: var(--gray-800) !important;
  --card-bg: var(--gray-800) !important;
  --modal-bg: var(--gray-800) !important;
  --dropdown-bg: var(--gray-800) !important;
  --header-light: var(--gray-800) !important;

  /* Text Colors */
  --text-color: var(--gray-50) !important;
  --text-muted: var(--gray-400) !important;
  --heading-color: var(--gray-50) !important;

  /* UI Element Colors */
  --btn-bg: var(--gray-900) !important;
  --btn-color: var(--white) !important;
  --btn-color-hover: var(--white) !important;
  --btn-color-custom: var(--black) !important;
  --box-hover: var(--primary-light) !important;

  /* Status Colors - Keep consistent with light theme using company brand colors */
  --success: var(--success-color) !important;
  --info: var(--info-color) !important;
  --warning: var(--warning-color) !important;
  --danger: var(--error-color) !important;
  --success-rgb: 16, 185, 129; /* RGB for success-color */
  --info-rgb: 59, 130, 246; /* RGB for info-color */
  --warning-rgb: 245, 158, 11; /* RGB for warning-color */
  --danger-rgb: 239, 68, 68; /* RGB for error-color */
  --worning: var(--warning-color) !important; /* Fix for typo in original variables */

  /* Status Light Colors - Slightly darker for dark theme */
  --success-light: var(--success-light) !important;
  --danger-light: var(--error-light) !important;
  --warning-light: var(--warning-light) !important;

  /* Neutral Colors */
  --gray: var(--gray-600) !important;
  --gray-light: var(--gray-800) !important;
  --gray-dark: var(--gray-900) !important;
  --dark: var(--gray-900) !important;

  /* Border Colors */
  --border-color: var(--gray-600) !important;
  --border-dark-color: var(--primary-dark) !important;
  --border-primary-light: var(--primary-light) !important;

  /* UI Properties */
  --custom-radius: var(--radius-md) !important;
  --box-shadow: var(--shadow-md) !important;
  --box-shadow-lg: var(--shadow-xl) !important;
}

/* Theme Toggle Button Styles */
.ww-color-switch {
  position: fixed;
  right: 20px;
  bottom: 20px;
  z-index: 9999;
  /* Ensure the button is always visible */
  filter: drop-shadow(0 0 5px rgba(0, 0, 0, 0.3));
}

/* Header Theme Toggle Button */
.ww-color-switch-header {
  display: flex;
  align-items: center;
  margin-left: 10px;
}

.ww-theme-toggle {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: var(--primary);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
  cursor: pointer;
  border: none;
  outline: none;
  padding: 0;
}

/* Ensure better contrast in dark mode */
:root[data-bs-theme="dark"] .ww-theme-toggle {
  background-color: var(--primary-dark);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.4);
}

.ww-theme-toggle:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

:root[data-bs-theme="dark"] .ww-theme-toggle:hover {
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.5);
  background-color: var(--primary);
}

.ww-theme-light, .ww-theme-dark {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: opacity 0.3s ease, transform 0.3s ease;
}

/* Light theme icon (moon) - shown when in light mode */
.ww-theme-light {
  opacity: 1;
  transform: translateY(0);
}

/* Dark theme icon (sun) - shown when in dark mode */
.ww-theme-dark {
  opacity: 0;
  transform: translateY(50px);
}

/* When in dark mode, switch the visibility of icons */
:root[data-bs-theme="dark"] .ww-theme-light {
  opacity: 0;
  transform: translateY(-50px);
}

:root[data-bs-theme="dark"] .ww-theme-dark {
  opacity: 1;
  transform: translateY(0);
}

.ww-theme-toggle i {
  font-size: 24px;
  color: var(--white);
}

/* Header Theme Toggle Button Styles */
.ww-theme-toggle-header {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: var(--primary);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
  cursor: pointer;
  border: none;
  outline: none;
  padding: 0;
}

:root[data-bs-theme="dark"] .ww-theme-toggle-header {
  background-color: var(--primary-dark);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.ww-theme-toggle-header:hover {
  transform: scale(1.05);
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.2);
}

:root[data-bs-theme="dark"] .ww-theme-toggle-header:hover {
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.3);
  background-color: var(--primary);
}

.ww-theme-toggle-header i {
  font-size: 18px;
  color: var(--white);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .ww-color-switch {
    right: 15px;
    bottom: 15px;
  }

  .ww-theme-toggle {
    width: 45px;
    height: 45px;
  }

  .ww-theme-toggle i {
    font-size: 20px;
  }

  .ww-color-switch-header {
    margin-left: 5px;
  }

  .ww-theme-toggle-header {
    width: 35px;
    height: 35px;
  }

  .ww-theme-toggle-header i {
    font-size: 16px;
  }
}

/* Ensure visibility on small screens */
@media (max-width: 576px) {
  .ww-color-switch {
    right: 10px;
    bottom: 10px;
  }

  .ww-theme-toggle {
    width: 40px;
    height: 40px;
  }

  .ww-theme-toggle i {
    font-size: 18px;
  }

  .ww-theme-toggle-header {
    width: 32px;
    height: 32px;
  }

  .ww-theme-toggle-header i {
    font-size: 14px;
  }
}

/* =========================================
   COMPREHENSIVE DARK MODE STYLES
   ========================================= */

/* Base Elements */
[data-bs-theme="dark"] {
  color-scheme: dark;
}

[data-bs-theme="dark"] body {
  background-color: var(--body-bg);
  color: var(--text-color);
}

/* Typography */
[data-bs-theme="dark"] h1,
[data-bs-theme="dark"] h2,
[data-bs-theme="dark"] h3,
[data-bs-theme="dark"] h4,
[data-bs-theme="dark"] h5,
[data-bs-theme="dark"] h6,
[data-bs-theme="dark"] .h1,
[data-bs-theme="dark"] .h2,
[data-bs-theme="dark"] .h3,
[data-bs-theme="dark"] .h4,
[data-bs-theme="dark"] .h5,
[data-bs-theme="dark"] .h6 {
  color: var(--heading-color) !important;
}

[data-bs-theme="dark"] a:not(.btn):not(.nav-link) {
  color: var(--primary);
}

[data-bs-theme="dark"] a:not(.btn):not(.nav-link):hover {
  color: var(--primary-dark);
}

[data-bs-theme="dark"] .text-muted {
  color: var(--text-muted) !important;
}

[data-bs-theme="dark"] small,
[data-bs-theme="dark"] .small {
  color: var(--text-muted);
}

/* Layout Components */
[data-bs-theme="dark"] .header,
[data-bs-theme="dark"] .navbar,
[data-bs-theme="dark"] .layout-navbar {
  background-color: var(--header-bg) !important;
  color: var(--text-color) !important;
  border-color: var(--border-color) !important;
}

[data-bs-theme="dark"] .sidebar,
[data-bs-theme="dark"] .widdxsidebar,
[data-bs-theme="dark"] .bg-menu-theme {
  background-color: var(--sidebar-bg) !important;
  border-color: var(--border-color) !important;
}

[data-bs-theme="dark"] .footer {
  background-color: var(--header-bg) !important;
  color: var(--text-color) !important;
  border-color: var(--border-color) !important;
}

/* Cards & Panels */
[data-bs-theme="dark"] .card,
[data-bs-theme="dark"] .panel,
[data-bs-theme="dark"] .container-fluid,
[data-bs-theme="dark"] .mc-promo-manage,
[data-bs-theme="dark"] .mc-promo-login {
  background-color: var(--card-bg) !important;
  color: var(--text-color) !important;
  border-color: var(--border-color) !important;
}

[data-bs-theme="dark"] .card-header,
[data-bs-theme="dark"] .panel-heading {
  background-color: var(--gray-light) !important;
  border-color: var(--border-color) !important;
  color: var(--heading-color) !important;
}

[data-bs-theme="dark"] .card-footer,
[data-bs-theme="dark"] .panel-footer {
  background-color: var(--gray-light) !important;
  border-color: var(--border-color) !important;
}

/* Navigation */
[data-bs-theme="dark"] .nav-link,
[data-bs-theme="dark"] .dropdown-item {
  color: var(--text-color) !important;
}

[data-bs-theme="dark"] .nav-link:hover,
[data-bs-theme="dark"] .dropdown-item:hover {
  background-color: var(--gray-light) !important;
}

[data-bs-theme="dark"] .nav-tabs {
  border-color: var(--border-color) !important;
}

[data-bs-theme="dark"] .nav-tabs .nav-link.active,
[data-bs-theme="dark"] .nav-tabs .nav-item.show .nav-link {
  background-color: var(--card-bg) !important;
  color: var(--primary) !important;
  border-color: var(--border-color) var(--border-color) var(--card-bg) !important;
}

[data-bs-theme="dark"] .dropdown-menu {
  background-color: var(--dropdown-bg) !important;
  border-color: var(--border-color) !important;
}

[data-bs-theme="dark"] .dropdown-divider {
  border-color: var(--border-color) !important;
}

/* Forms */
[data-bs-theme="dark"] .form-control,
[data-bs-theme="dark"] .input-group-text,
[data-bs-theme="dark"] .custom-select,
[data-bs-theme="dark"] .custom-file-label,
[data-bs-theme="dark"] input[type="text"],
[data-bs-theme="dark"] input[type="email"],
[data-bs-theme="dark"] input[type="password"],
[data-bs-theme="dark"] input[type="number"],
[data-bs-theme="dark"] input[type="search"],
[data-bs-theme="dark"] input[type="tel"],
[data-bs-theme="dark"] input[type="url"],
[data-bs-theme="dark"] textarea,
[data-bs-theme="dark"] select {
  background-color: var(--card-bg) !important;
  border-color: var(--border-color) !important;
  color: var(--text-color) !important;
}

[data-bs-theme="dark"] .form-control:focus,
[data-bs-theme="dark"] input[type="text"]:focus,
[data-bs-theme="dark"] input[type="email"]:focus,
[data-bs-theme="dark"] input[type="password"]:focus,
[data-bs-theme="dark"] input[type="number"]:focus,
[data-bs-theme="dark"] input[type="search"]:focus,
[data-bs-theme="dark"] input[type="tel"]:focus,
[data-bs-theme="dark"] input[type="url"]:focus,
[data-bs-theme="dark"] textarea:focus,
[data-bs-theme="dark"] select:focus {
  background-color: var(--card-bg) !important;
  border-color: var(--primary) !important;
  box-shadow: 0 0 0 0.2rem rgba(var(--primary-darker-rgb), 0.25) !important;
}

[data-bs-theme="dark"] .form-control::placeholder,
[data-bs-theme="dark"] input::placeholder,
[data-bs-theme="dark"] textarea::placeholder {
  color: var(--text-muted) !important;
  opacity: 0.7;
}

[data-bs-theme="dark"] .form-control:disabled,
[data-bs-theme="dark"] .form-control[readonly],
[data-bs-theme="dark"] input:disabled,
[data-bs-theme="dark"] input[readonly],
[data-bs-theme="dark"] textarea:disabled,
[data-bs-theme="dark"] textarea[readonly],
[data-bs-theme="dark"] select:disabled,
[data-bs-theme="dark"] select[readonly] {
  background-color: var(--gray-light) !important;
  opacity: 0.7;
}

[data-bs-theme="dark"] .form-check-input {
  background-color: var(--card-bg) !important;
  border-color: var(--border-color) !important;
}

[data-bs-theme="dark"] .form-check-input:checked {
  background-color: var(--primary) !important;
  border-color: var(--primary) !important;
}

/* Tables */
[data-bs-theme="dark"] .table {
  color: var(--text-color) !important;
  border-color: var(--border-color) !important;
}

[data-bs-theme="dark"] .table thead th {
  background-color: var(--gray-light) !important;
  color: var(--heading-color) !important;
  border-color: var(--border-color) !important;
}

[data-bs-theme="dark"] .table td,
[data-bs-theme="dark"] .table th {
  border-color: var(--border-color) !important;
}

[data-bs-theme="dark"] .table-striped tbody tr:nth-of-type(odd) {
  background-color: rgba(255, 255, 255, 0.05) !important;
}

[data-bs-theme="dark"] .table-hover tbody tr:hover {
  background-color: rgba(255, 255, 255, 0.075) !important;
  color: var(--text-color) !important;
}

/* DataTables Specific */
[data-bs-theme="dark"] .dataTables_wrapper .dataTables_length,
[data-bs-theme="dark"] .dataTables_wrapper .dataTables_filter,
[data-bs-theme="dark"] .dataTables_wrapper .dataTables_info,
[data-bs-theme="dark"] .dataTables_wrapper .dataTables_processing,
[data-bs-theme="dark"] .dataTables_wrapper .dataTables_paginate {
  color: var(--text-color) !important;
}

[data-bs-theme="dark"] .dataTables_wrapper .dataTables_filter input {
  background-color: var(--card-bg) !important;
  border-color: var(--border-color) !important;
  color: var(--text-color) !important;
}

[data-bs-theme="dark"] table.dataTable thead th.sorting:before,
[data-bs-theme="dark"] table.dataTable thead th.sorting_asc:before,
[data-bs-theme="dark"] table.dataTable thead th.sorting_desc:before {
  color: var(--primary) !important;
}

[data-bs-theme="dark"] .dataTables_wrapper .dataTables_paginate .paginate_button {
  color: var(--text-color) !important;
}

[data-bs-theme="dark"] .dataTables_wrapper .dataTables_paginate .paginate_button.current,
[data-bs-theme="dark"] .dataTables_wrapper .dataTables_paginate .paginate_button.current:hover {
  background: var(--primary) !important;
  color: white !important;
  border-color: var(--primary-dark) !important;
}

[data-bs-theme="dark"] .dataTables_wrapper .dataTables_paginate .paginate_button:hover {
  background: var(--gray-light) !important;
  color: var(--text-color) !important;
  border-color: var(--border-color) !important;
}

/* Buttons */
[data-bs-theme="dark"] .btn-default,
[data-bs-theme="dark"] .btn-secondary {
  background-color: var(--gray) !important;
  border-color: var(--gray-dark) !important;
  color: var(--text-color) !important;
}

[data-bs-theme="dark"] .btn-default:hover,
[data-bs-theme="dark"] .btn-secondary:hover {
  background-color: var(--gray-dark) !important;
  border-color: var(--dark) !important;
}

[data-bs-theme="dark"] .btn-primary {
  background-color: var(--primary) !important;
  border-color: var(--primary-dark) !important;
}

[data-bs-theme="dark"] .btn-primary:hover {
  background-color: var(--primary-dark) !important;
  border-color: var(--primary-darker) !important;
}

[data-bs-theme="dark"] .btn-outline-primary {
  color: var(--primary) !important;
  border-color: var(--primary) !important;
}

[data-bs-theme="dark"] .btn-outline-primary:hover {
  background-color: var(--primary) !important;
  color: white !important;
}

[data-bs-theme="dark"] .btn-link {
  color: var(--primary) !important;
}

[data-bs-theme="dark"] .btn-link:hover {
  color: var(--primary-dark) !important;
}

/* Alerts & Notifications */
[data-bs-theme="dark"] .alert {
  border-color: var(--border-color) !important;
}

[data-bs-theme="dark"] .alert-info {
  background-color: rgba(var(--info), 0.15) !important;
  color: var(--text-color) !important;
}

[data-bs-theme="dark"] .alert-success {
  background-color: rgba(var(--success), 0.15) !important;
  color: var(--text-color) !important;
}

[data-bs-theme="dark"] .alert-warning {
  background-color: rgba(var(--warning), 0.15) !important;
  color: var(--text-color) !important;
}

[data-bs-theme="dark"] .alert-danger {
  background-color: rgba(var(--danger), 0.15) !important;
  color: var(--text-color) !important;
}

/* Modals */
[data-bs-theme="dark"] .modal-content {
  background-color: var(--modal-bg) !important;
  border-color: var(--border-color) !important;
}

[data-bs-theme="dark"] .modal-header,
[data-bs-theme="dark"] .modal-footer {
  border-color: var(--border-color) !important;
}

[data-bs-theme="dark"] .modal-backdrop.show {
  opacity: 0.7 !important;
}

/* Pagination */
[data-bs-theme="dark"] .pagination .page-link {
  background-color: var(--card-bg) !important;
  border-color: var(--border-color) !important;
  color: var(--text-color) !important;
}

[data-bs-theme="dark"] .pagination .page-item.active .page-link {
  background-color: var(--primary) !important;
  border-color: var(--primary) !important;
  color: white !important;
}

[data-bs-theme="dark"] .pagination .page-item.disabled .page-link {
  background-color: var(--gray-light) !important;
  color: var(--text-muted) !important;
}

/* Borders */
[data-bs-theme="dark"] .border,
[data-bs-theme="dark"] .border-top,
[data-bs-theme="dark"] .border-right,
[data-bs-theme="dark"] .border-bottom,
[data-bs-theme="dark"] .border-left {
  border-color: var(--border-color) !important;
}

/* List Groups */
[data-bs-theme="dark"] .list-group-item {
  background-color: var(--card-bg) !important;
  border-color: var(--border-color) !important;
  color: var(--text-color) !important;
}

[data-bs-theme="dark"] .list-group-item.active {
  background-color: var(--primary) !important;
  border-color: var(--primary) !important;
  color: white !important;
}

[data-bs-theme="dark"] .list-group-item-action:hover {
  background-color: var(--gray-light) !important;
}

/* Badges */
[data-bs-theme="dark"] .badge-secondary {
  background-color: var(--gray) !important;
}

[data-bs-theme="dark"] .badge-primary {
  background-color: var(--primary) !important;
}

[data-bs-theme="dark"] .badge-success {
  background-color: var(--success) !important;
}

[data-bs-theme="dark"] .badge-info {
  background-color: var(--info) !important;
}

[data-bs-theme="dark"] .badge-warning {
  background-color: var(--warning) !important;
}

[data-bs-theme="dark"] .badge-danger {
  background-color: var(--danger) !important;
}

/* Progress Bars */
[data-bs-theme="dark"] .progress {
  background-color: var(--gray-light) !important;
}

/* Tooltips & Popovers */
[data-bs-theme="dark"] .tooltip-inner {
  background-color: var(--dark) !important;
  color: var(--text-color) !important;
}

[data-bs-theme="dark"] .popover {
  background-color: var(--card-bg) !important;
  border-color: var(--border-color) !important;
}

[data-bs-theme="dark"] .popover-header {
  background-color: var(--gray-light) !important;
  border-color: var(--border-color) !important;
}

[data-bs-theme="dark"] .popover-body {
  color: var(--text-color) !important;
}

/* Custom Components */
/* Search Modal */
[data-bs-theme="dark"] #search-modal {
  background-color: rgba(15, 23, 42, 0.95) !important;
}

[data-bs-theme="dark"] .search-input {
  background-color: var(--card-bg) !important;
  color: var(--text-color) !important;
  border-color: var(--border-color) !important;
}

[data-bs-theme="dark"] .search-close,
[data-bs-theme="dark"] .search-submit {
  color: var(--text-color) !important;
}

/* Additional Dark Mode Fixes */
[data-bs-theme="dark"] .list-group-item {
  background-color: var(--card-bg) !important;
  color: var(--text-color) !important;
  border-color: var(--border-color) !important;
}

[data-bs-theme="dark"] .modal-content {
  background-color: var(--card-bg) !important;
  color: var(--text-color) !important;
  border-color: var(--border-color) !important;
}

[data-bs-theme="dark"] .modal-header,
[data-bs-theme="dark"] .modal-footer {
  border-color: var(--border-color) !important;
}

[data-bs-theme="dark"] .close {
  color: var(--text-color) !important;
  text-shadow: none !important;
}

[data-bs-theme="dark"] .pagination .page-link {
  background-color: var(--card-bg) !important;
  color: var(--text-color) !important;
  border-color: var(--border-color) !important;
}

[data-bs-theme="dark"] .pagination .page-item.active .page-link {
  background-color: var(--primary) !important;
  color: #ffffff !important;
  border-color: var(--primary) !important;
}

/* Mobile Menu */
[data-bs-theme="dark"] .mobile-bottom-nav {
  background-color: var(--header-bg) !important;
  border-top-color: var(--border-color) !important;
}

[data-bs-theme="dark"] .mobile-nav-item i,
[data-bs-theme="dark"] .mobile-nav-text {
  color: var(--text-color) !important;
}

/* Credit Cards */
[data-bs-theme="dark"] div.credit-card {
  background-color: var(--card-bg) !important;
  border-color: var(--border-color) !important;
}

[data-bs-theme="dark"] div.credit-card div.card-number {
  background-color: var(--gray-light) !important;
}

/* Invoice */
[data-bs-theme="dark"] .invoice-container {
  background-color: var(--card-bg) !important;
  border-color: var(--border-color) !important;
}

/* Owl Carousel */
[data-bs-theme="dark"] .owl-theme .owl-dots .owl-dot span {
  background-color: var(--gray) !important;
}

[data-bs-theme="dark"] .owl-theme .owl-dots .owl-dot.active span,
[data-bs-theme="dark"] .owl-theme .owl-dots .owl-dot:hover span {
  background-color: var(--primary) !important;
}

[data-bs-theme="dark"] .owl-theme .owl-nav [class*=owl-] {
  background-color: var(--gray) !important;
  color: var(--text-color) !important;
}

[data-bs-theme="dark"] .owl-theme .owl-nav [class*=owl-]:hover {
  background-color: var(--primary) !important;
  color: white !important;
}

/* Domain Search */
[data-bs-theme="dark"] .domain-checker-container {
  background-color: var(--card-bg) !important;
}

[data-bs-theme="dark"] .domain-checker-bg {
  background-color: var(--gray-light) !important;
}

/* Feature Boxes - Basic styling, detailed styling in hosting-features.css */
[data-bs-theme="dark"] .feature-box {
  background-color: var(--card-bg) !important;
  border-color: var(--border-color) !important;
}

[data-bs-theme="dark"] .feature-box:hover {
  border-color: var(--primary) !important;
}

/* Pricing Tables */
[data-bs-theme="dark"] .pricing-box {
  background-color: var(--card-bg) !important;
  border-color: var(--border-color) !important;
}

[data-bs-theme="dark"] .pricing-box .pricing-header {
  background-color: var(--gray-light) !important;
}

[data-bs-theme="dark"] .pricing-box.best-value {
  border-color: var(--primary) !important;
}

/* Code Blocks */
[data-bs-theme="dark"] pre,
[data-bs-theme="dark"] code {
  background-color: var(--gray-dark) !important;
  color: #e6e6e6 !important;
  border-color: var(--border-color) !important;
}

/* Miscellaneous */
[data-bs-theme="dark"] hr {
  border-color: var(--border-color) !important;
}

[data-bs-theme="dark"] .breadcrumb {
  background-color: var(--gray-light) !important;
}

[data-bs-theme="dark"] .breadcrumb-item.active {
  color: var(--text-muted) !important;
}

[data-bs-theme="dark"] .jumbotron {
  background-color: var(--gray-light) !important;
}
