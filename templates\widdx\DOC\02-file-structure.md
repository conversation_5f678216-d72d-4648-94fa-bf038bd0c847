# WIDDX Template File Structure
# هيكل ملفات قالب WIDDX

## Root Directory Files | ملفات المجلد الرئيسي

### Core Template Files | ملفات القالب الأساسية
- `header.tpl` - Main header template with navigation
- `homepage.tpl` - Homepage layout and content
- `login.tpl` - Login page template
- `clientregister.tpl` - Client registration form
- `error-page.tpl` - Error page template

### Client Area Files | ملفات منطقة العميل
- `clientareahome.tpl` - Client dashboard
- `clientareadetails.tpl` - Client profile management
- `clientareaproducts.tpl` - Products/services listing
- `clientareadomains.tpl` - Domain management
- `clientareainvoices.tpl` - Invoice management

### Domain Management | إدارة النطاقات
- `domainchecker.tpl` - Domain availability checker
- `domain-pricing.tpl` - Domain pricing display
- `clientareadomaindetails.tpl` - Domain details
- `clientareadomaindns.tpl` - DNS management
- `clientareadomainemailforwarding.tpl` - Email forwarding

### Support System | نظام الدعم
- `supportticketslist.tpl` - Support tickets
- `supportticketsubmit-stepone.tpl` - Ticket submission
- `knowledgebase.tpl` - Knowledge base
- `knowledgebasearticle.tpl` - Article display

## Directory Structure | هيكل المجلدات

### /css/
- `theme.css` - Main stylesheet
- `variables.css` - Color variables and theme settings
- `all.css` - Common styles
- `store.css` - Store-specific styles
- `invoice.css` - Invoice styling

### /js/
- Custom JavaScript files
- jQuery plugins
- AJAX functionality
- Form validation
- Dynamic content loading

### /img/
- Template images
- Icons
- Backgrounds
- Logo files

### /lang/
- Language files
- Translations
- Custom language strings

### /includes/
- PHP helper functions
- Custom modules
- Integration code
- Utility functions

### /oauth/
- OAuth integration files
- Authentication handlers
- Token management

### /payment/
- Payment gateway templates
- Payment processing
- Transaction handling

### /store/
- Store templates
- Product displays
- Shopping cart

## File Integration | تكامل الملفات

### Template Dependencies | تبعيات القالب
1. **Header Dependencies**
   - CSS files
   - JavaScript libraries
   - Navigation structure

2. **Page Dependencies**
   - Common includes
   - Required scripts
   - Style dependencies

3. **Asset Dependencies**
   - Image requirements
   - Font dependencies
   - External resources

### File Relationships | علاقات الملفات
1. **Template Inheritance**
   - Base template structure
   - Common elements
   - Shared components

2. **Asset Loading**
   - CSS loading order
   - JavaScript dependencies
   - Image optimization

3. **Language Integration**
   - Translation files
   - String management
   - Multi-language support

## Customization Points | نقاط التخصيص

### Template Customization | تخصيص القالب
1. **Layout Modifications**
   - Template structure
   - Component placement
   - Responsive design

2. **Style Customization**
   - Color scheme
   - Typography
   - Layout adjustments

3. **Functionality Extensions**
   - Custom modules
   - Additional features
   - Integration points

## File Naming Conventions | اصطلاحات تسمية الملفات

### Template Files | ملفات القالب
- Use descriptive names
- Follow WHMCS conventions
- Maintain consistency

### Asset Files | ملفات الأصول
- Organized by type
- Version control
- Clear naming structure

## Version Control | التحكم في الإصدارات

### File Management | إدارة الملفات
1. **Backup Strategy**
   - Regular backups
   - Version tracking
   - Change documentation

2. **Update Process**
   - Version compatibility
   - Update testing
   - Rollback procedures

---
*This documentation is provided in both English and Arabic for better accessibility.*
*تم تقديم هذا التوثيق باللغتين الإنجليزية والعربية لسهولة الوصول.* 