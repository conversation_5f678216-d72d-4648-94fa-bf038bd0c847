<?php
/* Smarty version 3.1.48, created on 2025-06-14 02:44:20
  from 'C:\xampp\htdocs\Whmcs\templates\widdx\backend\widget\mobile\widdx-search-icon.tpl' */

/* @var Smarty_Internal_Template $_smarty_tpl */
if ($_smarty_tpl->_decodeProperties($_smarty_tpl, array (
  'version' => '3.1.48',
  'unifunc' => 'content_684cc5e4e2e2c6_45408924',
  'has_nocache_code' => false,
  'file_dependency' => 
  array (
    '2e900e1ea4a6764b830f1235a6c776fb698abe94' => 
    array (
      0 => 'C:\\xampp\\htdocs\\Whmcs\\templates\\widdx\\backend\\widget\\mobile\\widdx-search-icon.tpl',
      1 => 1747864158,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
  ),
),false)) {
function content_684cc5e4e2e2c6_45408924 (Smarty_Internal_Template $_smarty_tpl) {
?><div id="search-modal">
    <div class="search-content-area">
        <div class="search-close">
            <i class="fa fa-times" id="search-close-btn"></i>
        </div>
        <form method="post" action="<?php echo routePath('knowledgebase-search');?>
" class="search-form">
            <input type="text" name="search" class="search-input" placeholder="<?php echo call_user_func_array( $_smarty_tpl->smarty->registered_plugins[Smarty::PLUGIN_FUNCTION]['lang'][0], array( array('key'=>"searchOurKnowledgebase"),$_smarty_tpl ) );?>
..." aria-label="Search">
            <button type="submit" class="search-submit">
                <i class="fas fa-search"></i>
            </button>
        </form>
        <div class="search-hint">Press Enter to search or Esc to close</div>
    </div>
</div>
<?php }
}
