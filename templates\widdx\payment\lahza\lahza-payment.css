/**
 * Lahza.io Payment Gateway Styles for WHMCS WIDDX Template
 * 
 * Custom CSS for enhanced payment form styling
 * Optimized for modern UI/UX and responsive design
 * 
 * <AUTHOR> Development Team
 * @version 2.0.0
 */

/* Payment Container Styles */
.lahza-payment-container {
    max-width: 500px;
    margin: 2rem auto;
    background: var(--bg-card, #ffffff);
    border-radius: var(--radius-lg, 0.5rem);
    box-shadow: var(--shadow-lg, 0 10px 15px -3px rgba(0, 0, 0, 0.1));
    overflow: hidden;
    transition: all var(--transition-normal, 250ms ease-in-out);
}

.lahza-payment-container:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-xl, 0 20px 25px -5px rgba(0, 0, 0, 0.1));
}

/* Payment Header */
.payment-header {
    text-align: center;
    padding: 2rem 2rem 1rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    position: relative;
}

.payment-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/><circle cx="10" cy="50" r="0.5" fill="white" opacity="0.1"/><circle cx="90" cy="30" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    pointer-events: none;
}

.payment-logo {
    position: relative;
    z-index: 1;
}

.payment-logo i {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.9;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

.payment-header h4 {
    margin: 0;
    font-weight: 600;
    font-size: 1.5rem;
    position: relative;
    z-index: 1;
}

.payment-header p {
    margin: 0.5rem 0 0;
    opacity: 0.9;
    font-size: 0.9rem;
    position: relative;
    z-index: 1;
}

/* Payment Details */
.payment-details {
    background: var(--gray-50, #f9fafb);
    border-radius: var(--radius-md, 0.375rem);
    padding: 1.5rem;
    margin: 1.5rem;
    border: 1px solid var(--border-light, #e5e7eb);
}

.payment-details .row {
    margin-bottom: 0.75rem;
}

.payment-details .row:last-child {
    margin-bottom: 0;
}

.payment-details small {
    font-size: 0.75rem;
    color: var(--text-secondary, #6b7280);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    font-weight: 500;
}

.payment-details .fw-bold {
    font-weight: 600;
    color: var(--text-primary, #111827);
    font-size: 1rem;
    margin-top: 0.25rem;
}

.payment-details .text-primary {
    color: var(--primary-color, #4a338d) !important;
    font-size: 1.25rem;
}

/* Payment Button */
.lahza-pay-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    border-radius: var(--radius-lg, 0.5rem);
    padding: 1rem 2rem;
    font-size: 1.1rem;
    font-weight: 600;
    color: white;
    width: 100%;
    margin: 1.5rem;
    margin-top: 0;
    transition: all var(--transition-normal, 250ms ease-in-out);
    position: relative;
    overflow: hidden;
}

.lahza-pay-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.lahza-pay-btn:hover::before {
    left: 100%;
}

.lahza-pay-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.lahza-pay-btn:active {
    transform: translateY(0);
}

.lahza-pay-btn:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
}

.lahza-pay-btn i {
    margin-right: 0.5rem;
}

/* Payment Security */
.payment-security {
    text-align: center;
    padding: 0 1.5rem 1.5rem;
}

.payment-security small {
    color: var(--text-secondary, #6b7280);
    font-size: 0.8rem;
}

.payment-security i {
    color: var(--success-color, #10b981);
    margin-right: 0.25rem;
}

/* Error Messages */
.lahza-payment-errors {
    margin: 1.5rem;
    margin-bottom: 0;
    border-radius: var(--radius-md, 0.375rem);
    border: 1px solid var(--error-color, #ef4444);
    background: rgba(239, 68, 68, 0.1);
    color: var(--error-color, #ef4444);
    padding: 1rem;
    font-size: 0.9rem;
}

.lahza-payment-errors i {
    margin-right: 0.5rem;
}

/* Loading State */
.lahza-payment-loading {
    text-align: center;
    padding: 1.5rem;
}

.lahza-payment-loading .spinner-border {
    width: 2rem;
    height: 2rem;
    border-width: 0.2em;
}

.lahza-payment-loading small {
    color: var(--text-secondary, #6b7280);
    font-size: 0.9rem;
}

/* Responsive Design */
@media (max-width: 576px) {
    .lahza-payment-container {
        margin: 1rem;
        border-radius: var(--radius-md, 0.375rem);
    }
    
    .payment-header {
        padding: 1.5rem 1rem 1rem;
    }
    
    .payment-header h4 {
        font-size: 1.25rem;
    }
    
    .payment-details {
        margin: 1rem;
        padding: 1rem;
    }
    
    .lahza-pay-btn {
        margin: 1rem;
        margin-top: 0;
        padding: 0.875rem 1.5rem;
        font-size: 1rem;
    }
    
    .payment-security {
        padding: 0 1rem 1rem;
    }
}

/* Dark Mode Support */
[data-bs-theme="dark"] .lahza-payment-container {
    background: var(--gray-800, #1f2937);
    border: 1px solid var(--gray-700, #374151);
}

[data-bs-theme="dark"] .payment-details {
    background: var(--gray-700, #374151);
    border-color: var(--gray-600, #4b5563);
}

[data-bs-theme="dark"] .payment-details .fw-bold {
    color: var(--white, #ffffff);
}

[data-bs-theme="dark"] .payment-details small {
    color: var(--gray-400, #9ca3af);
}

[data-bs-theme="dark"] .payment-security small {
    color: var(--gray-400, #9ca3af);
}

/* RTL Support */
[dir="rtl"] .lahza-pay-btn i {
    margin-right: 0;
    margin-left: 0.5rem;
}

[dir="rtl"] .payment-security i {
    margin-right: 0;
    margin-left: 0.25rem;
}

[dir="rtl"] .lahza-payment-errors i {
    margin-right: 0;
    margin-left: 0.5rem;
}

/* Animation for form appearance */
.lahza-payment-container {
    animation: slideInUp 0.5s ease-out;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Success state animation */
.payment-success {
    animation: successPulse 0.6s ease-out;
}

@keyframes successPulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}
