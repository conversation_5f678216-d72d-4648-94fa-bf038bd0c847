{if $twitterusername}

    <h2>{$LANG.twitterlatesttweets}</h2>

    <div id="twitterFeedOutput">
        <p class="text-center"><img src="{$BASE_PATH_IMG}/loading.gif" /></p>
    </div>

    <script type="text/javascript" src="{assetPath file='twitter.js'}"></script>

{elseif $announcements}
<section class="announcements-section transparent-section">
    <div class="container">
        <div class="section-heading mb-4">
            <h2>{$LANG.ourlatestnews}</h2>
            <p>{$LANG.announcementsdescription}</p>
        </div>

    <div class="owl-carousel owl-theme announcements-carousel">
        {foreach $announcements as $announcement}
            {if $announcement@index < 3}
                {assign var="imageName" value=$announcement.title|replace:' ':'_'|replace:' ':'_'|lower}
                <div class="item">
                    <div class="card h-100">
                        <div class="card-img-wrapper">
                            <img src="{$WEB_ROOT}/templates/{$template}/frontend/assets/img/announcements/{$imageName}.png"
                                onerror="this.onerror=null;this.src='{$WEB_ROOT}/templates/{$template}/frontend/assets/img/announcements/{$imageName}.jpg'"
                                onerror="this.onerror=null;this.src='{$WEB_ROOT}/templates/{$template}/frontend/assets/img/announcements/{$imageName}.svg'"
                                onerror="this.onerror=null;this.src='{$WEB_ROOT}/templates/{$template}/frontend/assets/img/announcements/default.png'"
                                alt="{$announcement.title|default:'Image not available'}" 
                                class="card-img-top">
                        </div>
                        <div class="card-body d-flex flex-column">
                            <h5 class="card-title">
                                <a href="{routePath('announcement-view', $announcement.id, $announcement.urlfriendlytitle)}">{$announcement.title}</a>
                            </h5>
                            <h6 class="card-subtitle mb-2 text-muted">
                                <i class="fas fa-calendar-alt"></i>
                                {$carbon->translatePassedToFormat($announcement.rawDate, 'jS M Y')}
                            </h6>
                            <p class="card-text flex-grow-1">
                                {if $announcement.text|strip_tags|strlen < 250}
                                    {$announcement.text}
                                {else}
                                    {$announcement.summary}</br>
                                {/if}
                            </p>
                            <div class="mt-auto">
                                <a href="{routePath('announcement-view', $announcement.id, $announcement.urlfriendlytitle)}" 
                                   class="btn btn-primary w-100">{$LANG.readmore} <i class="fas fa-arrow-right"></i></a>
                            </div>
                        </div>
                    </div>
                </div>
            {/if}
        {/foreach}
    </div>
    </div>
</section>

    <script type="text/javascript">
        $(document).ready(function(){
            $(".owl-carousel").owlCarousel({
                loop: true,
                margin: 10,
                nav: true,
                dots: true,
                autoplay: true,
                autoplayTimeout: 5000,
                autoplayHoverPause: true,
                responsive: {
                    0: {
                        items: 1,
                        margin: 10
                    },
                    576: {
                        items: 2,
                        margin: 15
                    },
                    992: {
                        items: 3,
                        margin: 15
                    },
                    1200: {
                        items: 4,
                        margin: 15
                    }
                },
                navText: [
                    "<i class='fas fa-chevron-left'></i>",
                    "<i class='fas fa-chevron-right'></i>"
                ]
            });
        });
    </script>

{/if}
