<?php
/* Smarty version 3.1.48, created on 2025-06-14 02:44:20
  from 'C:\xampp\htdocs\Whmcs\templates\widdx\backend\widget\widdx-search.tpl' */

/* @var Smarty_Internal_Template $_smarty_tpl */
if ($_smarty_tpl->_decodeProperties($_smarty_tpl, array (
  'version' => '3.1.48',
  'unifunc' => 'content_684cc5e4646071_58168807',
  'has_nocache_code' => false,
  'file_dependency' => 
  array (
    'cd70d19edc8369c17931170b4e6fb2b6a5e67969' => 
    array (
      0 => 'C:\\xampp\\htdocs\\Whmcs\\templates\\widdx\\backend\\widget\\widdx-search.tpl',
      1 => 1747864158,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
  ),
),false)) {
function content_684cc5e4646071_58168807 (Smarty_Internal_Template $_smarty_tpl) {
?><div class="navbar-nav align-items-center">
  <div class="nav-item d-flex align-items-center">
    <form method="post" action="<?php echo routePath('knowledgebase-search');?>
" class="d-flex">
      <button type="submit" class="btn btn-search">
        <i class="bx bx-search fs-4 lh-0"></i>
      </button>
      <input type="text" name="search" class="form-control border-0 shadow-none"
        placeholder="<?php echo call_user_func_array( $_smarty_tpl->smarty->registered_plugins[Smarty::PLUGIN_FUNCTION]['lang'][0], array( array('key'=>'searchOurKnowledgebase'),$_smarty_tpl ) );?>
..." aria-label="Search..." />
    </form>
  </div>
</div><?php }
}
