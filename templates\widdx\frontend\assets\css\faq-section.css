/*
 * FAQ Section CSS
 * Styles for FAQ accordion with light/dark mode support
 */

/* Light mode variables (default) */
:root {
    --faq-bg: #f8f9fa;
    --faq-section-title-color: #333;
    --faq-section-desc-color: #666;
    --accordion-item-bg: #fff;
    --accordion-item-border: #e9ecef;
    --accordion-button-bg: #fff;
    --accordion-button-color: #333;
    --accordion-button-active-bg: #f8f9fa;
    --accordion-button-active-color: #333;
    --accordion-button-focus-border: rgba(0, 123, 255, 0.25);
    --accordion-button-icon-color: #333;
    --accordion-body-bg: #fff;
    --accordion-body-color: #666;
    --accordion-shadow: 0 2px 8px rgba(0,0,0,0.06);
    --accordion-hover-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

/* Dark mode variables */
[data-bs-theme="dark"] {
    --faq-bg: #0f172a;
    --faq-section-title-color: #f8fafc;
    --faq-section-desc-color: #94a3b8;
    --accordion-item-bg: #1e293b;
    --accordion-item-border: #334155;
    --accordion-button-bg: #1e293b;
    --accordion-button-color: #f8fafc;
    --accordion-button-active-bg: #334155;
    --accordion-button-active-color: #f8fafc;
    --accordion-button-focus-border: rgba(162, 122, 192, 0.5);
    --accordion-button-icon-color: #94a3b8;
    --accordion-body-bg: #1e293b;
    --accordion-body-color: #94a3b8;
    --accordion-shadow: 0 2px 8px rgba(0,0,0,0.2);
    --accordion-hover-shadow: 0 4px 12px rgba(0,0,0,0.3);
}

/* FAQ Section Styles */
#faq {
    background: transparent !important;
    padding: 100px 0;
}

#faq .section-heading h2 {
    color: var(--faq-section-title-color);
    font-size: 2.5rem;
    margin-bottom: 1rem;
    font-weight: 600;
}

#faq .section-heading p {
    color: var(--faq-section-desc-color);
    font-size: 1.1rem;
    line-height: 1.6;
}

/* Accordion Styles */
.accordion.faq-wrap {
    border-radius: 10px;
    overflow: hidden;
    box-shadow: var(--accordion-shadow);
}

.accordion-item {
    background-color: var(--accordion-item-bg);
    border: 1px solid var(--accordion-item-border);
    margin-bottom: 10px;
    border-radius: 8px !important;
    overflow: hidden;
    transition: all 0.3s ease;
}

/* Add smooth transition for accordion collapse */
.accordion-collapse {
    transition: all 0.3s ease-in-out;
}

.accordion-item:hover {
    box-shadow: var(--accordion-hover-shadow);
}

.accordion-header {
    margin: 0;
}

.accordion-button {
    padding: 20px 25px;
    font-size: 1.1rem;
    font-weight: 500;
    color: var(--accordion-button-color);
    background-color: var(--accordion-button-bg);
    border: none;
    border-radius: 0 !important;
    position: relative;
    transition: all 0.3s ease;
}

.accordion-button:hover {
    background-color: rgba(var(--primary-darker-rgb), 0.05);
}

[data-bs-theme="dark"] .accordion-button:hover {
    background-color: rgba(var(--primary-darker-rgb), 0.15);
}

.accordion-button:not(.collapsed) {
    color: var(--accordion-button-active-color);
    background-color: var(--accordion-button-active-bg);
    box-shadow: none;
}

.accordion-button:focus {
    box-shadow: 0 0 0 0.25rem var(--accordion-button-focus-border);
    border-color: transparent;
    z-index: 3;
}

/* Override Bootstrap's default accordion button styles */
.accordion-button::after {
    flex-shrink: 0;
    width: 1.25rem;
    height: 1.25rem;
    margin-left: auto;
    content: "";
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23333'%3e%3cpath fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-size: 1.25rem;
    transition: transform .2s ease-in-out;
}

[data-bs-theme="dark"] .accordion-button::after {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%2394a3b8'%3e%3cpath fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e");
}

/* Fix for RTL support */
html[dir="rtl"] .accordion-button::after {
    margin-left: 0;
    margin-right: auto;
}

.accordion-button:not(.collapsed)::after {
    transform: rotate(-180deg);
}

.accordion-body {
    padding: 20px 25px;
    color: var(--accordion-body-color);
    background-color: var(--accordion-body-bg);
    font-size: 1rem;
    line-height: 1.6;
}

/* Additional dark mode styles for accordion */
[data-bs-theme="dark"] .accordion-item {
    border-color: var(--accordion-item-border);
}

[data-bs-theme="dark"] .accordion-button {
    color: var(--accordion-button-color);
    background-color: var(--accordion-button-bg);
}

[data-bs-theme="dark"] .accordion-button:not(.collapsed) {
    color: var(--accordion-button-active-color);
    background-color: var(--accordion-button-active-bg);
}

[data-bs-theme="dark"] .accordion-body {
    color: var(--accordion-body-color);
    background-color: var(--accordion-body-bg);
}

/* Responsive Styles */
@media (max-width: 991px) {
    #faq {
        padding: 80px 0;
    }

    #faq .section-heading h2 {
        font-size: 2.2rem;
    }
}

@media (max-width: 767px) {
    #faq {
        padding: 60px 0;
    }

    #faq .section-heading h2 {
        font-size: 1.8rem;
    }

    #faq .section-heading p {
        font-size: 1rem;
    }

    .accordion-button {
        padding: 15px 20px;
        font-size: 1rem;
    }

    .accordion-body {
        padding: 15px 20px;
    }
}

@media (max-width: 575px) {
    #faq {
        padding: 40px 0;
    }

    #faq .section-heading h2 {
        font-size: 1.6rem;
    }

    .accordion-item {
        margin-bottom: 8px;
    }

    .accordion-button {
        padding: 12px 15px;
    }

    .accordion-body {
        padding: 12px 15px;
    }
}
