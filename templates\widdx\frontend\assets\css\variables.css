/**
 * CSS Variables for WHMCS WIDDX Template
 * This file contains all the CSS custom properties used throughout the template
 */

:root {
  /* Primary Colors */
  --primary-color: #4a338d;
  --primary-light: #6b4fb8;
  --primary-dark: #3a2870;
  --secondary-color: #cc00bb;
  --secondary-light: #e033d4;
  --secondary-dark: #a3009a;

  /* Neutral Colors */
  --white: #ffffff;
  --black: #000000;
  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-800: #1f2937;
  --gray-900: #111827;

  /* Status Colors */
  --success-color: #10b981;
  --success-light: #34d399;
  --success-dark: #059669;
  --warning-color: #f59e0b;
  --warning-light: #fbbf24;
  --warning-dark: #d97706;
  --error-color: #ef4444;
  --error-light: #f87171;
  --error-dark: #dc2626;
  --info-color: #3b82f6;
  --info-light: #60a5fa;
  --info-dark: #2563eb;

  /* Background Colors */
  --bg-primary: var(--white);
  --bg-secondary: var(--gray-50);
  --bg-tertiary: var(--gray-100);
  --bg-dark: var(--gray-900);
  --bg-card: var(--white);
  --bg-overlay: rgba(0, 0, 0, 0.5);

  /* Text Colors */
  --text-primary: var(--gray-900);
  --text-secondary: var(--gray-600);
  --text-tertiary: var(--gray-400);
  --text-inverse: var(--white);
  --text-muted: var(--gray-500);

  /* Border Colors */
  --border-color: var(--gray-200);
  --border-light: var(--gray-100);
  --border-dark: var(--gray-300);
  --border-focus: var(--primary-color);

  /* Shadow Variables */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

  /* Typography */
  --font-family-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-family-secondary: 'Poppins', sans-serif;
  --font-family-mono: 'Fira Code', 'Monaco', 'Consolas', monospace;

  /* Font Sizes */
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;
  --font-size-4xl: 2.25rem;

  /* Font Weights */
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;

  /* Line Heights */
  --line-height-tight: 1.25;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.75;

  /* Spacing */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;
  --spacing-3xl: 4rem;

  /* Border Radius */
  --radius-sm: 0.25rem;
  --radius-md: 0.375rem;
  --radius-lg: 0.5rem;
  --radius-xl: 0.75rem;
  --radius-2xl: 1rem;
  --radius-full: 9999px;

  /* Transitions */
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 250ms ease-in-out;
  --transition-slow: 350ms ease-in-out;

  /* Z-Index */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;

  /* Layout */
  --container-max-width: 1200px;
  --sidebar-width: 280px;
  --header-height: 70px;
  --footer-height: 60px;

  /* Breakpoints (for reference in CSS) */
  --breakpoint-sm: 576px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 992px;
  --breakpoint-xl: 1200px;
  --breakpoint-xxl: 1400px;
}

/* Dark Theme Variables */
[data-bs-theme="dark"] {
  --bg-primary: transparent;
  --bg-secondary: transparent;
  --bg-tertiary: transparent;
  --bg-card: var(--gray-800);
  --text-primary: var(--white);
  --text-secondary: var(--gray-300);
  --text-tertiary: var(--gray-400);
  --border-color: var(--gray-700);
  --border-light: var(--gray-600);
  --border-dark: var(--gray-500);
}

/* RTL Support Variables */
[dir="rtl"] {
  --text-align-start: right;
  --text-align-end: left;
  --float-start: right;
  --float-end: left;
  --border-start: border-right;
  --border-end: border-left;
  --margin-start: margin-right;
  --margin-end: margin-left;
  --padding-start: padding-right;
  --padding-end: padding-left;
}

[dir="ltr"] {
  --text-align-start: left;
  --text-align-end: right;
  --float-start: left;
  --float-end: right;
  --border-start: border-left;
  --border-end: border-right;
  --margin-start: margin-left;
  --margin-end: margin-right;
  --padding-start: padding-left;
  --padding-end: padding-right;
}

/* Performance Optimizations */
* {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: var(--font-family-primary);
  font-size: var(--font-size-base);
  line-height: var(--line-height-normal);
  color: var(--text-primary);
  background-color: transparent;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Utility Classes */
.text-primary { color: var(--primary-color) !important; }
.text-secondary { color: var(--secondary-color) !important; }
.text-success { color: var(--success-color) !important; }
.text-warning { color: var(--warning-color) !important; }
.text-error { color: var(--error-color) !important; }
.text-info { color: var(--info-color) !important; }

.bg-primary { background-color: var(--primary-color) !important; }
.bg-secondary { background-color: var(--secondary-color) !important; }
.bg-success { background-color: var(--success-color) !important; }
.bg-warning { background-color: var(--warning-color) !important; }
.bg-error { background-color: var(--error-color) !important; }
.bg-info { background-color: var(--info-color) !important; }

.border-primary { border-color: var(--primary-color) !important; }
.border-secondary { border-color: var(--secondary-color) !important; }

.shadow-sm { box-shadow: var(--shadow-sm) !important; }
.shadow-md { box-shadow: var(--shadow-md) !important; }
.shadow-lg { box-shadow: var(--shadow-lg) !important; }
.shadow-xl { box-shadow: var(--shadow-xl) !important; }

.transition-fast { transition: all var(--transition-fast) !important; }
.transition-normal { transition: all var(--transition-normal) !important; }
.transition-slow { transition: all var(--transition-slow) !important; }

/* Performance optimizations for animations */
.will-change-transform { will-change: transform; }
.will-change-opacity { will-change: opacity; }
.will-change-scroll { will-change: scroll-position; }

/* Reduce motion for accessibility */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}
