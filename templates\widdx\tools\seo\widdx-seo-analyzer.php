<?php

if (!defined("WHMCS")) {
    die("This file cannot be accessed directly");
}

use WHMCS\Database\Capsule;

class WiddxSeoAnalyzer {
    private $url;
    private $template;
    private $webRoot;
    private $results = [];
    private $content;
    private $headers;
    private $dom;
    private $wordCount = 0;
    private $keywordDensity = [];
    private $brokenLinks = [];
    private $brokenImages = [];

    public function __construct($url, $template, $webRoot) {
        $this->url = $url;
        $this->template = $template;
        $this->webRoot = $webRoot;
    }

    public function analyze() {
        try {
            // Initialize results array
            $this->results = [
                'error' => null,
                'SeoPreview' => [
                    'title' => '',
                    'description' => '',
                    'favicon' => '',
                    'url' => $this->url
                ],
                'basicSeo' => [
                    'title' => '',
                    'metaDescription' => '',
                    'keywords' => '',
                    'h1Tags' => [],
                    'h2Tags' => [],
                    'imagesWithoutAlt' => [],
                    'linkRatio' => [
                        'internal' => 0,
                        'external' => 0,
                        'total' => 0,
                        'ratio' => 0
                    ],
                    'wordCount' => 0,
                    'keywordDensity' => [],
                    'brokenLinks' => [],
                    'brokenImages' => []
                ],
                'advancedSeo' => [
                    'canonicalTag' => ['status' => '', 'value' => ''],
                    'noindex' => ['status' => '', 'value' => ''],
                    'wwwCanonizalization' => ['status' => '', 'value' => ''],
                    'robotsRules' => ['status' => '', 'value' => ''],
                    'openGraph' => ['status' => '', 'value' => []],
                    'schema' => ['status' => '', 'value' => []],
                    'sitemap' => ['status' => '', 'value' => ''],
                    'robotsTxt' => ['status' => '', 'value' => '']
                ],
                'performance' => [
                    'pageSize' => 0,
                    'responseTime' => 0,
                    'imageExpiresHeaders' => '',
                    'unminifiedJs' => '',
                    'unminifiedCss' => '',
                    'pageObjects' => ['total' => 0],
                    'httpRequests' => 0,
                    'compressedImages' => [],
                    'uncompressedFiles' => []
                ],
                'security' => [
                    'sslCertificate' => ['status' => '', 'value' => ''],
                    'securityHeaders' => ['status' => '', 'value' => []],
                    'vulnerableScripts' => ['status' => '', 'value' => []],
                    'contentSecurityPolicy' => ['status' => '', 'value' => ''],
                    'safeBrowsing' => ['status' => '', 'value' => '']
                ]
            ];

            // Validate URL
            if (!filter_var($this->url, FILTER_VALIDATE_URL)) {
                throw new Exception("Invalid URL provided");
            }

            // Get page content and headers
            $this->fetchPageContent();
            
            // Create DOM object
            $this->dom = new DOMDocument();
            @$this->dom->loadHTML($this->content, LIBXML_NOERROR);

            // Analyze SEO components
            $this->analyzeSeoPreview();
            $this->analyzeBasicSeo();
            $this->analyzeAdvancedSeo();
            $this->analyzePerformance();
            $this->analyzeSecurity();

            // Calculate scores
            $this->calculateScores();

            return $this->results;

        } catch (Exception $e) {
            $this->results['error'] = $e->getMessage();
            return $this->results;
        }
    }

    private function fetchPageContent() {
        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => $this->url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_HEADER => true,
            CURLOPT_USERAGENT => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            CURLOPT_TIMEOUT => 30
        ]);
        
        $response = curl_exec($ch);
        $headerSize = curl_getinfo($ch, CURLINFO_HEADER_SIZE);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        
        if ($httpCode !== 200) {
            throw new Exception("Failed to fetch page content. HTTP Code: " . $httpCode);
        }

        $this->headers = substr($response, 0, $headerSize);
        $this->content = substr($response, $headerSize);
        
        curl_close($ch);
    }

    private function analyzeSeoPreview() {
        // Extract title
        $title = $this->dom->getElementsByTagName('title')->item(0);
        $this->results['SeoPreview']['title'] = $title ? $title->textContent : 'No title found';

        // Extract meta description
        $metaTags = $this->dom->getElementsByTagName('meta');
        $description = '';
        foreach ($metaTags as $tag) {
            if ($tag->getAttribute('name') === 'description') {
                $description = $tag->getAttribute('content');
                break;
            }
        }
        $this->results['SeoPreview']['description'] = $description ?: 'No meta description found';

        // Extract favicon
        $links = $this->dom->getElementsByTagName('link');
        $favicon = '';
        foreach ($links as $link) {
            if (in_array($link->getAttribute('rel'), ['icon', 'shortcut icon'])) {
                $favicon = $link->getAttribute('href');
                if (!filter_var($favicon, FILTER_VALIDATE_URL)) {
                    $favicon = $this->url . (strpos($favicon, '/') === 0 ? '' : '/') . $favicon;
                }
                break;
            }
        }
        $this->results['SeoPreview']['favicon'] = $favicon ?: 'No favicon found';
    }

    private function analyzeBasicSeo() {
        // Title analysis
        $title = $this->results['SeoPreview']['title'];
        $titleLength = strlen($title);
        $this->results['basicSeo']['title'] = [
            'status' => $titleLength >= 30 && $titleLength <= 60 ? 'success' : 'warning',
            'length' => $titleLength,
            'message' => $titleLength < 30 ? 'Title is too short' : ($titleLength > 60 ? 'Title is too long' : 'Title length is optimal'),
            'recommendation' => 'Keep title between 30-60 characters'
        ];

        // Meta description analysis
        $description = $this->results['SeoPreview']['description'];
        $descLength = strlen($description);
        $this->results['basicSeo']['metaDescription'] = [
            'status' => $descLength >= 120 && $descLength <= 160 ? 'success' : 'warning',
            'length' => $descLength,
            'message' => $descLength < 120 ? 'Description is too short' : ($descLength > 160 ? 'Description is too long' : 'Description length is optimal'),
            'recommendation' => 'Keep description between 120-160 characters'
        ];

        // Heading structure analysis
        $headings = [];
        for ($i = 1; $i <= 6; $i++) {
            $hTags = $this->dom->getElementsByTagName('h' . $i);
            $headings['h' . $i] = [
                'count' => $hTags->length,
                'texts' => []
            ];
            foreach ($hTags as $hTag) {
                $headings['h' . $i]['texts'][] = $hTag->textContent;
            }
        }
        $this->results['basicSeo']['h1Tags'] = $headings['h1'];
        $this->results['basicSeo']['h2Tags'] = $headings['h2'];

        // Image analysis
        $images = $this->dom->getElementsByTagName('img');
        $imageAnalysis = [
            'total' => $images->length,
            'withAlt' => 0,
            'withoutAlt' => 0,
            'details' => []
        ];
        foreach ($images as $img) {
            $alt = $img->getAttribute('alt');
            $imageAnalysis['details'][] = [
                'src' => $img->getAttribute('src'),
                'alt' => $alt,
                'status' => $alt ? 'success' : 'warning'
            ];
            if ($alt) {
                $imageAnalysis['withAlt']++;
            } else {
                $imageAnalysis['withoutAlt']++;
            }
        }
        $this->results['basicSeo']['imagesWithoutAlt'] = $imageAnalysis['withoutAlt'];

        // Link analysis
        $links = $this->dom->getElementsByTagName('a');
        $linkAnalysis = [
            'total' => $links->length,
            'internal' => 0,
            'external' => 0,
            'broken' => 0,
            'details' => []
        ];
        foreach ($links as $link) {
            $href = $link->getAttribute('href');
            $isInternal = strpos($href, $this->url) === 0 || strpos($href, '/') === 0;
            $linkAnalysis['details'][] = [
                'href' => $href,
                'text' => $link->textContent,
                'type' => $isInternal ? 'internal' : 'external'
            ];
            if ($isInternal) {
                $linkAnalysis['internal']++;
            } else {
                $linkAnalysis['external']++;
            }
        }
        $this->results['basicSeo']['linkRatio'] = [
            'internal' => $linkAnalysis['internal'],
            'external' => $linkAnalysis['external'],
            'total' => $linkAnalysis['total'],
            'ratio' => $linkAnalysis['external'] / $linkAnalysis['total']
        ];

        // New basic SEO analysis
        $this->analyzeWordCount();
        $this->analyzeKeywordDensity();
        $this->checkBrokenLinks();
        $this->checkBrokenImages();
    }

    private function analyzeAdvancedSeo() {
        // Canonical tag analysis
        $canonical = '';
        $links = $this->dom->getElementsByTagName('link');
        foreach ($links as $link) {
            if ($link->getAttribute('rel') === 'canonical') {
                $canonical = $link->getAttribute('href');
                break;
            }
        }
        $this->results['advancedSeo']['canonicalTag'] = [
            'status' => $canonical ? 'success' : 'warning',
            'value' => $canonical,
            'message' => $canonical ? 'Canonical tag found' : 'No canonical tag found',
            'recommendation' => 'Add canonical tag to prevent duplicate content issues'
        ];

        // Robots meta tag analysis
        $robots = '';
        $metaTags = $this->dom->getElementsByTagName('meta');
        foreach ($metaTags as $tag) {
            if ($tag->getAttribute('name') === 'robots') {
                $robots = $tag->getAttribute('content');
                break;
            }
        }
        $this->results['advancedSeo']['robotsRules'] = [
            'status' => $robots ? 'success' : 'warning',
            'value' => $robots,
            'message' => $robots ? 'Robots meta tag found' : 'No robots meta tag found',
            'recommendation' => 'Add robots meta tag to control search engine crawling'
        ];

        // Schema markup analysis
        $schema = [];
        $scripts = $this->dom->getElementsByTagName('script');
        foreach ($scripts as $script) {
            if ($script->getAttribute('type') === 'application/ld+json') {
                $schema[] = json_decode($script->textContent, true);
            }
        }
        $this->results['advancedSeo']['schema'] = [
            'status' => !empty($schema) ? 'success' : 'warning',
            'found' => count($schema),
            'message' => !empty($schema) ? 'Schema markup found' : 'No schema markup found',
            'recommendation' => 'Add schema markup to improve search results appearance'
        ];
    }

    private function analyzePerformance() {
        // Response time analysis
        $startTime = microtime(true);
        $ch = curl_init($this->url);
        curl_setopt_array($ch, [
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_HEADER => true,
            CURLOPT_NOBODY => true,
            CURLOPT_TIMEOUT => 30
        ]);
        curl_exec($ch);
        $responseTime = (microtime(true) - $startTime) * 1000;
        curl_close($ch);

        $this->results['performance']['responseTime'] = [
            'status' => $responseTime < 500 ? 'success' : ($responseTime < 1000 ? 'warning' : 'danger'),
            'value' => round($responseTime, 2),
            'message' => $responseTime < 500 ? 'Fast response time' : ($responseTime < 1000 ? 'Moderate response time' : 'Slow response time'),
            'recommendation' => 'Aim for response time under 500ms'
        ];

        // Page size analysis
        $pageSize = strlen($this->content);
        $this->results['performance']['pageSize'] = [
            'status' => $pageSize < 500000 ? 'success' : ($pageSize < 1000000 ? 'warning' : 'danger'),
            'value' => $this->formatBytes($pageSize),
            'message' => $pageSize < 500000 ? 'Good page size' : ($pageSize < 1000000 ? 'Moderate page size' : 'Large page size'),
            'recommendation' => 'Aim for page size under 500KB'
        ];

        // Resource analysis
        $resources = [
            'scripts' => $this->dom->getElementsByTagName('script')->length,
            'stylesheets' => $this->dom->getElementsByTagName('link')->length,
            'images' => $this->dom->getElementsByTagName('img')->length
        ];
        $this->results['performance']['pageObjects'] = $resources;
    }

    private function analyzeSecurity() {
        // SSL analysis
        $isHttps = strpos($this->url, 'https://') === 0;
        $this->results['security']['sslCertificate'] = [
            'status' => $isHttps ? 'success' : 'danger',
            'enabled' => $isHttps,
            'message' => $isHttps ? 'SSL is enabled' : 'SSL is not enabled',
            'recommendation' => 'Enable SSL for secure data transmission'
        ];

        // Security headers analysis
        $securityHeaders = [
            'X-Frame-Options' => false,
            'X-XSS-Protection' => false,
            'X-Content-Type-Options' => false,
            'Strict-Transport-Security' => false,
            'Content-Security-Policy' => false
        ];

        foreach (explode("\n", $this->headers) as $header) {
            foreach ($securityHeaders as $headerName => &$found) {
                if (stripos($header, $headerName) === 0) {
                    $found = true;
                }
            }
        }

        $this->results['security']['securityHeaders'] = [
            'status' => count(array_filter($securityHeaders)) >= 3 ? 'success' : 'warning',
            'headers' => $securityHeaders,
            'message' => count(array_filter($securityHeaders)) >= 3 ? 'Good security headers' : 'Missing important security headers',
            'recommendation' => 'Implement all recommended security headers'
        ];
    }

    private function calculateScores() {
        // Calculate individual scores
        $scores = [
            'basicSeo' => $this->calculateBasicScore(),
            'advancedSeo' => $this->calculateAdvancedScore(),
            'performance' => $this->calculatePerformanceScore(),
            'security' => $this->calculateSecurityScore()
        ];

        // Calculate total score
        $totalScore = array_sum($scores) / count($scores);
        
        $this->results['score'] = [
            'scorePercentage' => round($totalScore),
            'totalChecks' => $this->countTotalChecks(),
            'passedChecks' => $this->countPassedChecks(),
            'failedChecks' => $this->countFailedChecks(),
            'improveChecks' => $this->countImproveChecks()
        ];
    }

    private function calculateBasicScore() {
        $score = 0;
        $total = 0;

        // Title score
        $titleLength = strlen($this->results['SeoPreview']['title']);
        if ($titleLength >= 30 && $titleLength <= 60) {
            $score += 20;
        }
        $total += 20;

        // Description score
        $descLength = strlen($this->results['SeoPreview']['description']);
        if ($descLength >= 120 && $descLength <= 160) {
            $score += 20;
        }
        $total += 20;

        // Headings score
        $h1Count = $this->results['basicSeo']['h1Tags']['count'];
        if ($h1Count == 1) {
            $score += 20;
        }
        $total += 20;

        // Images score
        $images = $this->results['basicSeo']['imagesWithoutAlt'];
        if ($images > 0) {
            $score += 20;
        }
        $total += 20;

        // Links score
        $links = $this->results['basicSeo']['linkRatio'];
        if ($links['internal'] > 0) {
            $score += 20;
        }
        $total += 20;

        return ($score / $total) * 100;
    }

    private function calculateAdvancedScore() {
        $score = 0;
        $total = 0;

        // Canonical score
        if ($this->results['advancedSeo']['canonicalTag']['status'] === 'success') {
            $score += 25;
        }
        $total += 25;

        // Robots score
        if ($this->results['advancedSeo']['robotsRules']['status'] === 'success') {
            $score += 25;
        }
        $total += 25;

        // Schema score
        if ($this->results['advancedSeo']['schema']['status'] === 'success') {
            $score += 25;
        }
        $total += 25;

        // WWW canonicalization score
        $urlParts = parse_url($this->url);
        if (strpos($urlParts['host'], 'www.') === 0) {
            $score += 25;
        }
        $total += 25;

        return ($score / $total) * 100;
    }

    private function calculatePerformanceScore() {
        $score = 0;
        $total = 0;

        // Response time score
        $responseTime = $this->results['performance']['responseTime']['value'];
        if ($responseTime < 500) {
            $score += 50;
        } elseif ($responseTime < 1000) {
            $score += 25;
        }
        $total += 50;

        // Page size score
        $pageSize = $this->results['performance']['pageSize']['value'];
        $pageSizeBytes = $this->parseBytes($pageSize);
        if ($pageSizeBytes < 500000) {
            $score += 50;
        } elseif ($pageSizeBytes < 1000000) {
            $score += 25;
        }
        $total += 50;

        return ($score / $total) * 100;
    }

    private function calculateSecurityScore() {
        $score = 0;
        $total = 0;

        // SSL score
        if ($this->results['security']['sslCertificate']['status'] === 'success') {
            $score += 50;
        }
        $total += 50;

        // Security headers score
        $headers = $this->results['security']['securityHeaders']['headers'];
        $enabledHeaders = count(array_filter($headers));
        $score += ($enabledHeaders / count($headers)) * 50;
        $total += 50;

        return ($score / $total) * 100;
    }

    private function countTotalChecks() {
        return 20; // Total number of checks across all categories
    }

    private function countPassedChecks() {
        $passed = 0;
        
        // Basic SEO checks
        if (strlen($this->results['SeoPreview']['title']) >= 30 && strlen($this->results['SeoPreview']['title']) <= 60) $passed++;
        if (strlen($this->results['SeoPreview']['description']) >= 120 && strlen($this->results['SeoPreview']['description']) <= 160) $passed++;
        if ($this->results['basicSeo']['h1Tags']['count'] == 1) $passed++;
        if ($this->results['basicSeo']['imagesWithoutAlt'] > 0) $passed++;
        if ($this->results['basicSeo']['linkRatio']['internal'] > 0) $passed++;

        // Advanced SEO checks
        if ($this->results['advancedSeo']['canonicalTag']['status'] === 'success') $passed++;
        if ($this->results['advancedSeo']['robotsRules']['status'] === 'success') $passed++;
        if ($this->results['advancedSeo']['schema']['status'] === 'success') $passed++;
        if (strpos(parse_url($this->url, PHP_URL_HOST), 'www.') === 0) $passed++;

        // Performance checks
        if ($this->results['performance']['responseTime']['value'] < 500) $passed++;
        if ($this->parseBytes($this->results['performance']['pageSize']['value']) < 500000) $passed++;

        // Security checks
        if ($this->results['security']['sslCertificate']['status'] === 'success') $passed++;
        if (count(array_filter($this->results['security']['securityHeaders']['headers'])) >= 3) $passed++;

        return $passed;
    }

    private function countFailedChecks() {
        return $this->countTotalChecks() - $this->countPassedChecks() - $this->countImproveChecks();
    }

    private function countImproveChecks() {
        $improve = 0;
        
        // Basic SEO improvements
        if (strlen($this->results['SeoPreview']['title']) > 60) $improve++;
        if (strlen($this->results['SeoPreview']['description']) > 160) $improve++;
        if ($this->results['basicSeo']['h1Tags']['count'] > 1) $improve++;
        if ($this->results['basicSeo']['imagesWithoutAlt'] > 0) $improve++;
        if ($this->results['basicSeo']['linkRatio']['broken'] > 0) $improve++;

        // Advanced SEO improvements
        if ($this->results['advancedSeo']['canonicalTag']['status'] === 'warning') $improve++;
        if ($this->results['advancedSeo']['robotsRules']['status'] === 'warning') $improve++;
        if ($this->results['advancedSeo']['schema']['status'] === 'warning') $improve++;
        if (strpos(parse_url($this->url, PHP_URL_HOST), 'www.') !== 0) $improve++;

        // Performance improvements
        if ($this->results['performance']['responseTime']['value'] >= 500 && $this->results['performance']['responseTime']['value'] < 1000) $improve++;
        if ($this->parseBytes($this->results['performance']['pageSize']['value']) >= 500000 && $this->parseBytes($this->results['performance']['pageSize']['value']) < 1000000) $improve++;

        // Security improvements
        if ($this->results['security']['sslCertificate']['status'] === 'warning') $improve++;
        if (count(array_filter($this->results['security']['securityHeaders']['headers'])) < 3) $improve++;

        return $improve;
    }

    private function formatBytes($bytes) {
        $units = ['B', 'KB', 'MB', 'GB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);
        $bytes /= pow(1024, $pow);
        return round($bytes, 2) . ' ' . $units[$pow];
    }

    private function parseBytes($size) {
        $units = ['B' => 1, 'KB' => 1024, 'MB' => 1024 * 1024, 'GB' => 1024 * 1024 * 1024];
        $size = strtoupper($size);
        foreach ($units as $unit => $multiplier) {
            if (strpos($size, $unit) !== false) {
                return (float) $size * $multiplier;
            }
        }
        return (float) $size;
    }

    private function analyzeWordCount() {
        $text = strip_tags($this->content);
        $words = mb_split('\s+', mb_strtolower($text, 'UTF-8'));
        $this->wordCount = count(array_filter($words, function($w) { return mb_strlen($w, 'UTF-8') > 0; }));
        $this->results['basicSeo']['wordCount'] = $this->wordCount;
    }

    private function analyzeKeywordDensity() {
        $text = strip_tags($this->content);
        $words = mb_split('\s+', mb_strtolower($text, 'UTF-8'));
        $words = array_filter($words, function($w) { return mb_strlen($w, 'UTF-8') > 0; });
        $wordCount = count($words);
        $keywordCount = [];
        foreach ($words as $word) {
            if (mb_strlen($word, 'UTF-8') > 3) {
                if (!isset($keywordCount[$word])) {
                    $keywordCount[$word] = 0;
                }
                $keywordCount[$word]++;
            }
        }
        arsort($keywordCount);
        $density = [];
        foreach ($keywordCount as $word => $count) {
            $percentage = ($count / $wordCount) * 100;
            if ($percentage > 0.5) {
                $density[$word] = round($percentage, 2);
            }
        }
        $this->results['basicSeo']['keywordDensity'] = array_slice($density, 0, 10);
    }

    private function checkBrokenLinks() {
        $links = $this->dom->getElementsByTagName('a');
        foreach ($links as $link) {
            $href = $link->getAttribute('href');
            if ($href && $href !== '#' && $href !== 'javascript:void(0)') {
                $fullUrl = $this->getFullUrl($href);
                if (!$this->checkUrlExists($fullUrl)) {
                    $this->brokenLinks[] = [
                        'url' => $fullUrl,
                        'text' => $link->textContent
                    ];
                }
            }
        }
        $this->results['basicSeo']['brokenLinks'] = $this->brokenLinks;
    }

    private function checkBrokenImages() {
        $images = $this->dom->getElementsByTagName('img');
        foreach ($images as $image) {
            $src = $image->getAttribute('src');
            if ($src) {
                $fullUrl = $this->getFullUrl($src);
                if (!$this->checkUrlExists($fullUrl)) {
                    $this->brokenImages[] = [
                        'url' => $fullUrl,
                        'alt' => $image->getAttribute('alt')
                    ];
                }
            }
        }
        $this->results['basicSeo']['brokenImages'] = $this->brokenImages;
    }

    private function getFullUrl($url) {
        if (strpos($url, 'http') === 0) {
            return $url;
        }
        return rtrim($this->url, '/') . '/' . ltrim($url, '/');
    }

    private function checkUrlExists($url) {
        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_NOBODY, true);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        return $httpCode == 200;
    }
}

// Handle the request
if (isset($_GET['url'])) {
    $url = $_GET['url'];
    $template = $template;
    $webRoot = $WEB_ROOT;

    $analyzer = new WiddxSeoAnalyzer($url, $template, $webRoot);
    $results = $analyzer->analyze();

    // Return results as JSON
    header('Content-Type: application/json');
    echo json_encode($results);
    exit;
} 