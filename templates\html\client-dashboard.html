<!DOCTYPE html>
<html lang="en" dir="ltr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Client Dashboard - WIDDX Digital Solutions</title>
    <meta name="description" content="Manage your WIDDX services, billing, and support tickets from your client dashboard.">

    <!-- Performance Optimizations -->
    <meta name="theme-color" content="#010815">
    <link rel="dns-prefetch" href="//fonts.googleapis.com">
    <link rel="dns-prefetch" href="//cdnjs.cloudflare.com">
    <link rel="preconnect" href="https://fonts.googleapis.com" crossorigin>
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>

    <!-- Critical CSS Inline -->
    <style>
        :root{--primary-color:#010815;--secondary-color:#6c5bb9;--accent-color:#c0a5d5;--text-primary:#ffffff;--text-secondary:rgba(255,255,255,0.8);--text-muted:rgba(255,255,255,0.6);--background-primary:#010815;--background-secondary:#0f1419;--background-tertiary:rgba(255,255,255,0.05);--border-color:rgba(192,165,213,0.1);--card-background:rgba(255,255,255,0.05);--navbar-background:rgba(1,8,21,0.95);--footer-background:#000510;--white:#ffffff;--gray-light:#f8f9fa;--gray-dark:#2c3e50;--transition:all 0.3s ease;--border-radius:12px;--box-shadow:0 10px 30px rgba(108,91,185,0.1);--container-max-width:1200px;--section-padding:80px 0}
        *{margin:0;padding:0;box-sizing:border-box}
        body{font-family:'Inter',sans-serif;background:var(--background-primary);color:var(--text-primary);line-height:1.6;overflow-x:hidden}
        .navbar{position:fixed;top:0;left:0;right:0;z-index:1000;padding:15px 0;background:var(--navbar-background);backdrop-filter:blur(10px);border-bottom:1px solid var(--border-color)}
        .nav-container{display:flex;justify-content:space-between;align-items:center;max-width:var(--container-max-width);margin:0 auto;padding:0 20px}
        .nav-logo a{font-size:1.8rem;font-weight:700;color:var(--accent-color);text-decoration:none;letter-spacing:2px}
        .nav-menu{display:flex;gap:30px;align-items:center}
        .nav-link{color:var(--text-primary);text-decoration:none;font-weight:500;transition:var(--transition);position:relative;padding:8px 0}
        .nav-link:hover,.nav-link.active{color:var(--accent-color)}
        .nav-link::after{content:'';position:absolute;bottom:0;left:0;width:0;height:2px;background:var(--accent-color);transition:width 0.3s ease}
        .nav-link:hover::after,.nav-link.active::after{width:100%}
        .client-area{background:linear-gradient(135deg,var(--secondary-color),var(--accent-color));padding:8px 16px!important;border-radius:20px;color:var(--white)!important}
        .client-area::after{display:none}
        .theme-toggle{background:none;border:2px solid var(--accent-color);color:var(--accent-color);padding:8px 12px;border-radius:50%;cursor:pointer;transition:var(--transition);font-size:14px}
        .theme-toggle:hover{background:var(--accent-color);color:var(--background-primary)}
        .hamburger{display:none;flex-direction:column;cursor:pointer;gap:4px}
        .hamburger span{width:25px;height:3px;background:var(--text-primary);transition:var(--transition)}
        .whmcs-dashboard{padding:100px 0 40px;min-height:100vh}
        .whmcs-container{max-width:var(--container-max-width);margin:0 auto;padding:0 20px}
        .whmcs-layout{display:grid;grid-template-columns:280px 1fr;gap:30px;align-items:start}
        .loading-overlay{position:fixed;top:0;left:0;right:0;bottom:0;background:rgba(0,0,0,0.8);display:flex;align-items:center;justify-content:center;z-index:9999;backdrop-filter:blur(5px);opacity:0;visibility:hidden;transition:all 0.3s ease}
        .loading-overlay.show{opacity:1;visibility:visible}
        .loading-spinner{width:50px;height:50px;border:3px solid var(--border-color);border-top:3px solid var(--accent-color);border-radius:50%;animation:spin 1s linear infinite}
        @keyframes spin{0%{transform:rotate(0deg)}100%{transform:rotate(360deg)}}
        .error-message{background:rgba(231,76,60,0.1);border:1px solid rgba(231,76,60,0.3);color:#e74c3c;padding:12px 16px;border-radius:8px;margin:10px 0;display:none}
        .error-message.show{display:block;animation:slideDown 0.3s ease}
        @keyframes slideDown{from{opacity:0;transform:translateY(-10px)}to{opacity:1;transform:translateY(0)}}
        @media(max-width:1024px){.whmcs-layout{grid-template-columns:1fr;gap:20px}}
        @media(max-width:768px){.whmcs-dashboard{padding:80px 0 40px}.whmcs-container{padding:0 15px}.nav-menu{display:none}.hamburger{display:flex}}
    </style>

    <!-- Google Fonts with Arabic Support -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Tajawal:wght@300;400;500;700&family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/client-area.css">

    <!-- GSAP -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/ScrollTrigger.min.js"></script>
</head>
<body>
    <!-- Loading Overlay -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-spinner"></div>
    </div>

    <!-- Navigation -->
    <nav class="navbar" id="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <a href="index.html">WIDDX</a>
            </div>
            <div class="nav-menu" id="nav-menu">
                <a href="index.html" class="nav-link" data-translate="home">Home</a>
                <a href="about.html" class="nav-link" data-translate="about">About</a>
                <a href="services.html" class="nav-link" data-translate="services">Services</a>
                <a href="portfolio.html" class="nav-link" data-translate="portfolio">Portfolio</a>
                <a href="contact.html" class="nav-link" data-translate="contact">Contact</a>
                <a href="client-area.html" class="nav-link client-area active" data-translate="dashboard">Dashboard</a>
                <button class="language-toggle" id="languageToggle" aria-label="Toggle language">
                    <i class="fas fa-language" id="languageIcon"></i>
                    <span id="languageText">عربي</span>
                </button>
                <button class="theme-toggle" id="themeToggle" aria-label="Toggle theme">
                    <i class="fas fa-moon" id="themeIcon"></i>
                </button>
                <div class="user-menu">
                    <button class="user-menu-toggle" id="userMenuToggle" aria-label="User menu">
                        <i class="fas fa-user-circle"></i>
                        <span id="userName">Loading...</span>
                        <i class="fas fa-chevron-down"></i>
                    </button>
                    <div class="user-dropdown" id="userDropdown">
                        <a href="#" class="dropdown-item" onclick="showProfileSettings()">
                            <i class="fas fa-user"></i>
                            Profile Settings
                        </a>
                        <a href="#" class="dropdown-item" onclick="showAccountSettings()">
                            <i class="fas fa-cog"></i>
                            Account Settings
                        </a>
                        <div class="dropdown-divider"></div>
                        <a href="#" class="dropdown-item" onclick="handleLogout()">
                            <i class="fas fa-sign-out-alt"></i>
                            Logout
                        </a>
                    </div>
                </div>
            </div>
            <div class="hamburger" id="hamburger">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>
    </nav>

    <!-- Dashboard Main Content -->
    <main class="whmcs-dashboard">
        <div class="whmcs-container">
            <!-- Error Message Container -->
            <div class="error-message" id="errorMessage"></div>

            <!-- WHMCS Layout -->
            <div class="whmcs-layout">
                <!-- Left Sidebar -->
                <aside class="whmcs-sidebar" id="sidebar">
                    <!-- Your Info Section -->
                    <div class="sidebar-section" id="userInfoSection">
                        <div class="sidebar-header">
                            <i class="fas fa-user"></i>
                            <span>Your Info</span>
                        </div>
                        <div class="user-info">
                            <div class="user-name" id="sidebarUserName">Loading...</div>
                            <div class="user-details" id="userDetails">
                                <div id="userFullName">Loading...</div>
                                <div id="userCity">Loading...</div>
                                <div id="userAddress">Loading...</div>
                                <div id="userCountry">Loading...</div>
                            </div>
                            <button class="update-btn" onclick="showProfileSettings()">
                                <i class="fas fa-edit"></i>
                                Update
                            </button>
                        </div>
                    </div>

                    <!-- Contacts Section -->
                    <div class="sidebar-section" id="contactsSection">
                        <div class="sidebar-header">
                            <i class="fas fa-address-book"></i>
                            <span>Contacts</span>
                        </div>
                        <div class="contacts-content">
                            <div class="contacts-list" id="contactsList">
                                <div class="no-contacts">No Contacts Found</div>
                            </div>
                            <button class="new-contact-btn" onclick="showNewContactModal()">
                                <i class="fas fa-plus"></i>
                                New Contact...
                            </button>
                        </div>
                    </div>

                    <!-- Shortcuts Section -->
                    <div class="sidebar-section" id="shortcutsSection">
                        <div class="sidebar-header">
                            <i class="fas fa-bookmark"></i>
                            <span>Shortcuts</span>
                        </div>
                        <div class="shortcuts-content">
                            <a href="#" class="shortcut-item" onclick="navigateToServices()">
                                <i class="fas fa-shopping-cart"></i>
                                Order New Services
                            </a>
                            <a href="#" class="shortcut-item" onclick="navigateToDomains()">
                                <i class="fas fa-globe"></i>
                                Register a New Domain
                            </a>
                            <a href="#" class="shortcut-item" onclick="handleLogout()">
                                <i class="fas fa-sign-out-alt"></i>
                                Logout
                            </a>
                        </div>
                    </div>
                </aside>

                <!-- Main Content Area -->
                <div class="whmcs-main-content" id="mainContent">
                    <!-- Welcome Header -->
                    <div class="welcome-header">
                        <h1>Welcome Back, <span id="dashboardUserName">Loading...</span>!</h1>
                        <div class="breadcrumb">
                            <a href="index.html">Portal Home</a> > <span>Client Area</span>
                        </div>
                    </div>

                    <!-- Stats Cards -->
                    <div class="stats-cards" id="statsCards">
                        <div class="stat-card" onclick="navigateToServices()">
                            <div class="stat-icon">
                                <i class="fas fa-cube"></i>
                            </div>
                            <div class="stat-content">
                                <div class="stat-number" id="servicesCount">0</div>
                                <div class="stat-label">SERVICES</div>
                            </div>
                        </div>
                        <div class="stat-card" onclick="navigateToDomains()">
                            <div class="stat-icon">
                                <i class="fas fa-globe"></i>
                            </div>
                            <div class="stat-content">
                                <div class="stat-number" id="domainsCount">0</div>
                                <div class="stat-label">DOMAINS</div>
                            </div>
                        </div>
                        <div class="stat-card" onclick="navigateToTickets()">
                            <div class="stat-icon">
                                <i class="fas fa-ticket-alt"></i>
                            </div>
                            <div class="stat-content">
                                <div class="stat-number" id="ticketsCount">0</div>
                                <div class="stat-label">TICKETS</div>
                            </div>
                        </div>
                        <div class="stat-card" onclick="navigateToInvoices()">
                            <div class="stat-icon">
                                <i class="fas fa-file-invoice"></i>
                            </div>
                            <div class="stat-content">
                                <div class="stat-number" id="invoicesCount">0</div>
                                <div class="stat-label">INVOICES</div>
                            </div>
                        </div>
                    </div>

                    <!-- Search Bar -->
                    <div class="search-section">
                        <div class="search-bar">
                            <i class="fas fa-search"></i>
                            <input type="text" id="knowledgebaseSearch" placeholder="Enter a question here to search our knowledgebase for answers..." onkeypress="handleSearchKeypress(event)">
                        </div>
                    </div>

                    <!-- Dynamic Content Sections -->
                    <div class="content-sections" id="contentSections">
                        <!-- Content will be dynamically loaded here -->
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Modern Background -->
    <div class="modern-gradient-background">
        <div class="gradient-layer-1"></div>
        <div class="gradient-layer-2"></div>
        <div class="gradient-layer-3"></div>
        <div class="gradient-sphere-1"></div>
        <div class="gradient-sphere-2"></div>
        <div class="gradient-sphere-3"></div>
        <div class="gradient-sphere-4"></div>
        <div class="wave-element"></div>
        <div class="particle-system"></div>
        <div class="noise-texture"></div>
    </div>

    <!-- Scripts -->
    <script defer src="js/language-manager.js"></script>
    <script defer src="js/modern-background.js"></script>
    <script defer src="js/main.js"></script>
    <script defer src="js/client-dashboard.js"></script>
    <script defer src="js/gsap-animations.js"></script>

    <!-- Background System -->
    <script src="background/background.js"></script>
</body>
</html>
