# دليل استكشاف الأخطاء وإصلاحها | Troubleshooting Guide
## Lahza.io Payment Gateway for WHMCS WIDDX

---

## 🚨 **المشكلة الأكثر شيوعاً | Most Common Issue**

### ❌ **الدفع تم بنجاح لكن الفاتورة لم تُحدث | Payment Successful but Invoice Not Updated**

هذه هي المشكلة الأكثر شيوعاً وإليك الحلول المرتبة حسب الأولوية:

---

## 🔧 **الحلول السريعة | Quick Fixes**

### **1. تفعيل السجلات | Enable Logging**

```php
// في إعدادات بوابة الدفع
// In payment gateway settings
WHMCS Admin → Setup → Payments → Payment Gateways → Lahza.io
Enable Logging: YES
```

### **2. فحص السجلات | Check Logs**

```php
// عرض سجلات البوابة
// View gateway logs
WHMCS Admin → Utilities → Logs → Gateway Log
// ابحث عن "[Lahza.io]"
// Search for "[Lahza.io]"
```

### **3. اختبار الاتصال | Test Connectivity**

```bash
# اختبر إمكانية الوصول لـ callback URL
# Test callback URL accessibility
curl -I https://yourdomain.com/modules/gateways/callback/lahza.php
```

---

## 🔍 **أدوات التشخيص | Diagnostic Tools**

### **أداة التشخيص الشاملة | Comprehensive Debug Tool**
```
https://yourdomain.com/modules/gateways/callback/lahza_debug.php?debug_key=lahza_debug_2024
```

### **أداة اختبار Callback | Callback Test Tool**
```
https://yourdomain.com/modules/gateways/callback/test_callback.php?test_key=callback_test_2024
```

---

## 📋 **قائمة التحقق | Checklist**

### ✅ **التحقق من الإعدادات الأساسية | Basic Settings Check**

- [ ] البوابة مفعلة في WHMCS | Gateway activated in WHMCS
- [ ] Public Key صحيح | Correct Public Key
- [ ] Secret Key صحيح | Correct Secret Key  
- [ ] Test Mode مطابق للمفاتيح | Test Mode matches keys
- [ ] Enable Logging مفعل | Logging enabled

### ✅ **التحقق من URLs | URLs Check**

- [ ] Callback URL يعمل | Callback URL accessible
- [ ] HTTPS مفعل | HTTPS enabled
- [ ] لا يوجد حماية إضافية تمنع الوصول | No additional protection blocking access

### ✅ **التحقق من Lahza.io Dashboard | Lahza.io Dashboard Check**

- [ ] Webhook URL مُعرف | Webhook URL configured
- [ ] Callback URL مُعرف | Callback URL configured
- [ ] المفاتيح صحيحة | Keys are correct
- [ ] الحساب مفعل | Account is active

---

## 🐛 **الأخطاء الشائعة والحلول | Common Errors & Solutions**

### **1. "Module Not Activated"**

**السبب | Cause:** البوابة غير مفعلة في WHMCS

**الحل | Solution:**
```php
WHMCS Admin → Setup → Payments → Payment Gateways → Lahza.io → Activate
```

### **2. "Missing transaction reference"**

**السبب | Cause:** لم يتم إرسال reference في callback

**الحل | Solution:**
- تحقق من إعدادات Callback URL في Lahza.io
- تأكد من أن URL صحيح ويمكن الوصول إليه

### **3. "Payment verification failed"**

**السبب | Cause:** فشل في التحقق من الدفع عبر API

**الحل | Solution:**
- تحقق من Secret Key
- تحقق من اتصال الخادم بـ API Lahza.io
- تحقق من صحة transaction reference

### **4. "Invalid Invoice ID"**

**السبب | Cause:** لم يتم العثور على Invoice ID في البيانات

**الحل | Solution:**
- تحقق من metadata في الدفع
- تحقق من تنسيق transaction reference
- استخدم أداة التشخيص لفحص البيانات

### **5. "Duplicate Transaction ID"**

**السبب | Cause:** تم معالجة نفس المعاملة مسبقاً

**الحل | Solution:**
- هذا طبيعي للمعاملات المكررة
- تحقق من سجلات WHMCS للتأكد من معالجة الدفع

---

## 🔧 **حلول متقدمة | Advanced Solutions**

### **إصلاح مشكلة Invoice ID المفقود | Fix Missing Invoice ID**

```php
// إذا كان transaction reference يتبع النمط: WHMCS_123_timestamp_hash
// If transaction reference follows pattern: WHMCS_123_timestamp_hash

// يمكن استخراج Invoice ID من reference
// Invoice ID can be extracted from reference
preg_match('/^WHMCS_(\d+)_/', $transactionReference, $matches);
$invoiceId = $matches[1] ?? null;
```

### **معالجة يدوية للدفع | Manual Payment Processing**

```php
// في حالة فشل المعالجة التلقائية
// If automatic processing fails

// استخدم أداة التشخيص مع transaction reference
// Use debug tool with transaction reference
https://yourdomain.com/modules/gateways/callback/lahza_debug.php?debug_key=lahza_debug_2024

// أدخل transaction reference واضغط "Test Verification"
// Enter transaction reference and click "Test Verification"
```

### **إضافة دفع يدوياً | Add Payment Manually**

```php
// في WHMCS Admin
// In WHMCS Admin
Billing → Invoices → [Select Invoice] → Add Payment

Transaction ID: [Lahza Transaction Reference]
Amount: [Payment Amount]
Payment Method: Lahza.io Payment Gateway
```

---

## 📊 **مراقبة الأداء | Performance Monitoring**

### **فحص دوري للسجلات | Regular Log Monitoring**

```bash
# فحص سجلات البوابة يومياً
# Check gateway logs daily
grep "Lahza.io" /path/to/whmcs/logs/gateway.log
```

### **إحصائيات الدفع | Payment Statistics**

```sql
-- فحص المدفوعات الناجحة
-- Check successful payments
SELECT COUNT(*) FROM tblaccounts 
WHERE gateway = 'lahza' 
AND date >= DATE_SUB(NOW(), INTERVAL 30 DAY);
```

---

## 🆘 **طلب المساعدة | Getting Help**

### **معلومات مطلوبة عند طلب الدعم | Required Information for Support**

1. **إصدار WHMCS | WHMCS Version**
2. **إعدادات البوابة | Gateway Settings** (بدون المفاتيح السرية)
3. **سجلات الأخطاء | Error Logs**
4. **Transaction Reference** للدفع المتأثر
5. **لقطة شاشة من أداة التشخيص | Screenshot from debug tool**

### **قنوات الدعم | Support Channels**

- 📧 **البريد الإلكتروني | Email:** <EMAIL>
- 🌐 **الموقع | Website:** [lahza.io](https://lahza.io)
- 📚 **التوثيق | Documentation:** [docs.lahza.io](https://docs.lahza.io)

---

## 🔒 **أمان الإنتاج | Production Security**

### **ملفات يجب حذفها من الإنتاج | Files to Remove from Production**

```bash
# احذف هذه الملفات من الخادم المباشر
# Delete these files from live server
rm modules/gateways/callback/lahza_debug.php
rm modules/gateways/callback/test_callback.php
rm templates/widdx/payment/lahza/test.php
```

### **إعدادات الأمان | Security Settings**

```php
// تأكد من هذه الإعدادات
// Ensure these settings
HTTPS: Enabled
PHP Error Display: Disabled
Gateway Logging: Enabled (for monitoring)
```

---

## 📈 **تحسين الأداء | Performance Optimization**

### **تحسين Callback Processing | Optimize Callback Processing**

```php
// في إعدادات PHP
// In PHP settings
max_execution_time = 60
memory_limit = 256M
```

### **تحسين قاعدة البيانات | Database Optimization**

```sql
-- إضافة فهرس للبحث السريع
-- Add index for faster searches
ALTER TABLE tblgatewaylog ADD INDEX idx_gateway_date (gateway, date);
```

---

## ✅ **التحقق من نجاح الإصلاح | Verify Fix Success**

### **اختبار شامل | Comprehensive Test**

1. **إنشاء فاتورة تجريبية | Create Test Invoice**
2. **إجراء دفع تجريبي | Make Test Payment**
3. **فحص السجلات | Check Logs**
4. **التحقق من تحديث الفاتورة | Verify Invoice Update**
5. **اختبار Webhook | Test Webhook**

### **علامات النجاح | Success Indicators**

- ✅ الفاتورة تُحدث تلقائياً بعد الدفع
- ✅ السجلات تظهر "Payment processed successfully"
- ✅ لا توجد أخطاء في Gateway Log
- ✅ العميل يُعاد توجيهه للفاتورة المدفوعة

---

**💡 نصيحة:** احتفظ بهذا الدليل مرجعاً سريعاً لحل المشاكل المستقبلية

**💡 Tip:** Keep this guide as a quick reference for future troubleshooting
