/**
 * WebGL Background Animation for WHMCS Template
 * Lightweight Three.js particle system
 */

class WebGLBackground {
  constructor(options = {}) {
    // Default settings
    this.settings = {
      container: document.body,
      particleCount: window.innerWidth < 768 ? 0 : window.innerWidth < 1200 ? 500 : 800,
      colorPalette: ['#4a89dc', '#967adc', '#3baeda'], // Brand colors
      ...options
    };

    // Initialize Three.js
    this.initThreeJS();
    this.createParticles();
    this.addEventListeners();
  }

  initThreeJS() {
      console.log('Initializing Three.js WebGL background');
      
      try {
          // Scene setup
          this.scene = new THREE.Scene();
          this.camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
          this.camera.position.z = 30;

          // Renderer with transparent background
          this.renderer = new THREE.WebGLRenderer({
              alpha: true,
              antialias: true
          });
          this.renderer.setSize(window.innerWidth, window.innerHeight);
          this.renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));
          
          if (!this.settings.container) {
              console.error('Container element not found');
              return false;
          }
          
          this.settings.container.appendChild(this.renderer.domElement);
          console.log('Three.js initialized successfully');
          return true;
      } catch (error) {
          console.error('Three.js initialization failed:', error);
          return false;
      }

    // Animation loop
    this.animate = this.animate.bind(this);
    this.animate();
  }

  createParticles() {
    const particlesGeometry = new THREE.BufferGeometry();
    const particlesCnt = this.settings.particleCount;
    
    // Random particle positions
    const posArray = new Float32Array(particlesCnt * 3);
    for(let i = 0; i < particlesCnt * 3; i++) {
      posArray[i] = (Math.random() - 0.5) * 50;
    }

    particlesGeometry.setAttribute('position', new THREE.BufferAttribute(posArray, 3));

    // Particle material
    const particlesMaterial = new THREE.PointsMaterial({
      size: 0.2,
      color: this.settings.colorPalette[0],
      transparent: true,
      opacity: 0.8,
      blending: THREE.AdditiveBlending
    });

    this.particles = new THREE.Points(particlesGeometry, particlesMaterial);
    this.scene.add(this.particles);
  }

  animate() {
    if (document.hidden) return; // Pause when tab inactive
    
    requestAnimationFrame(this.animate);
    
    // Gentle particle movement
    this.particles.rotation.x += 0.0005;
    this.particles.rotation.y += 0.0007;
    
    this.renderer.render(this.scene, this.camera);
  }

  addEventListeners() {
    // Debounced resize handler
    window.addEventListener('resize', () => {
      clearTimeout(this.resizeTimer);
      this.resizeTimer = setTimeout(() => {
        this.camera.aspect = window.innerWidth / window.innerHeight;
        this.camera.updateProjectionMatrix();
        this.renderer.setSize(window.innerWidth, window.innerHeight);
      }, 200);
    });
  }
}

// Initialize with debug checks
document.addEventListener('DOMContentLoaded', () => {
    console.log('DOMContentLoaded - checking WebGL support');
    
    const container = document.querySelector('.main-background');
    if (!container) {
        console.error('Main background container not found');
        return;
    }

    if (!window.WebGLRenderingContext) {
        console.warn('WebGL not supported in this browser');
        return;
    }

    if (window.innerWidth < 768) {
        console.log('Skipping on mobile device');
        return;
    }

    console.log('Creating WebGL background instance');
    const bg = new WebGLBackground({ container });
    
    if (!bg.initThreeJS()) {
        console.warn('WebGL background initialization failed');
    }
});