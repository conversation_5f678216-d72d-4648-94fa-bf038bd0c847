// ===== LANGUAGE MANAGEMENT SYSTEM =====
// Comprehensive RTL/LTR language support for Arabic and English

class LanguageManager {
    constructor() {
        this.currentLanguage = 'en';
        this.supportedLanguages = ['en', 'ar'];
        this.translations = {};
        this.isRTL = false;

        // Initialize language system
        this.init();
    }

    async init() {
        console.log('🌐 Initializing Language Manager...');

        // Detect and set initial language
        this.detectLanguage();

        // Load translations for the current language
        await this.loadTranslations(this.currentLanguage);

        // Set up language toggle
        this.initLanguageToggle();

        // Apply initial language
        this.applyLanguage(this.currentLanguage);

        console.log('✅ Language Manager initialized');
    }

    async loadTranslations(language) {
        try {
            const response = await fetch(`js/translations/${language}.json`);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            this.translations = await response.json();
            console.log(`🌐 Translations for '${language}' loaded successfully.`);
        } catch (error) {
            console.error(`❌ Failed to load translations for '${language}':`, error);
            // Fallback to English if loading fails
            if (language !== 'en') {
                await this.loadTranslations('en');
            }
        }
    }

    detectLanguage() {
        // Check localStorage first
        const savedLanguage = localStorage.getItem('language');
        if (savedLanguage && this.supportedLanguages.includes(savedLanguage)) {
            this.currentLanguage = savedLanguage;
            return;
        }

        // Check browser language
        const browserLang = navigator.language || navigator.userLanguage;
        if (browserLang.startsWith('ar')) {
            this.currentLanguage = 'ar';
        } else {
            this.currentLanguage = 'en';
        }

        // Save detected language
        localStorage.setItem('language', this.currentLanguage);
    }

    initLanguageToggle() {
        const languageToggle = document.getElementById('languageToggle');
        const languageText = document.getElementById('languageText');

        if (languageToggle) {
            languageToggle.addEventListener('click', () => {
                this.toggleLanguage();
            });
        }

        // Update toggle text
        this.updateLanguageToggle();
    }

    toggleLanguage() {
        const newLanguage = this.currentLanguage === 'en' ? 'ar' : 'en';
        this.setLanguage(newLanguage);
    }

    setLanguage(language) {
        if (!this.supportedLanguages.includes(language)) {
            console.warn('⚠️ Unsupported language:', language);
            return;
        }

        console.log('🌐 Switching language from', this.currentLanguage, 'to', language);

        this.currentLanguage = language;
        this.isRTL = language === 'ar';

        // Save language preference
        localStorage.setItem('language', language);

        // Apply language changes
        this.applyLanguage(language);

        // Update language toggle
        this.updateLanguageToggle();

        // Trigger language change event
        window.dispatchEvent(new CustomEvent('languageChanged', {
            detail: {
                language: language,
                isRTL: this.isRTL,
                translations: this.translations[language]
            }
        }));
    }

    applyLanguage(language) {
        const html = document.documentElement;
        const body = document.body;

        // Set HTML attributes
        html.setAttribute('lang', language);
        html.setAttribute('dir', language === 'ar' ? 'rtl' : 'ltr');

        // Add language class to body
        body.classList.remove('lang-en', 'lang-ar');
        body.classList.add(`lang-${language}`);

        // Update text content
        this.updateTextContent(language);

        // Update document title and meta
        this.updateDocumentMeta(language);
    }

    updateTextContent() {
        const translations = this.translations;
        if (!translations) return;

        // Update elements with data-translate attribute
        document.querySelectorAll('[data-translate]').forEach(element => {
            const key = element.getAttribute('data-translate');
            if (translations[key]) {
                element.innerHTML = translations[key]; // Use innerHTML to support simple HTML tags
            }
        });

        // Update placeholders
        document.querySelectorAll('[data-translate-placeholder]').forEach(element => {
            const key = element.getAttribute('data-translate-placeholder');
            if (translations[key]) {
                element.setAttribute('placeholder', translations[key]);
            }
        });

        // Update aria-labels
        document.querySelectorAll('[data-translate-aria]').forEach(element => {
            const key = element.getAttribute('data-translate-aria');
            if (translations[key]) {
                element.setAttribute('aria-label', translations[key]);
            }
        });
    }

    updateDocumentMeta() {
        const translations = this.translations;
        if (!translations) return;

        // Update title if translation exists
        if (translations.pageTitle) {
            document.title = translations.pageTitle;
        }

        // Update meta description
        const metaDesc = document.querySelector('meta[name="description"]');
        if (metaDesc && translations.pageDescription) {
            metaDesc.setAttribute('content', translations.pageDescription);
        }
    }

    updateLanguageToggle() {
        const languageToggle = document.getElementById('languageToggle');
        const languageText = document.getElementById('languageText');

        if (languageToggle && languageText) {
            const isArabic = this.currentLanguage === 'ar';
            languageToggle.setAttribute('aria-pressed', isArabic);
            languageText.textContent = isArabic ? this.translations.switchToEnglish : this.translations.switchToArabic;
        }
    }

    // Get current language
    getCurrentLanguage() {
        return this.currentLanguage;
    }

    // Get current translations
    getCurrentTranslations() {
        return this.translations[this.currentLanguage];
    }

    // Check if current language is RTL
    isCurrentLanguageRTL() {
        return this.isRTL;
    }

    // Get translation by key
    translate(key) {
        return this.translations[this.currentLanguage][key] || key;
    }
}

// Initialize language manager
let languageManager;

// Export for global use
window.LanguageManager = LanguageManager;

// Auto-initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    languageManager = new LanguageManager();
    window.languageManager = languageManager;
});
