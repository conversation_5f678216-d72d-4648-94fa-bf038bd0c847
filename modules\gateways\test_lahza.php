<?php
/**
 * Lahza.io Payment Gateway Test Suite
 * 
 * This file provides testing utilities for the Lahza.io payment gateway
 * Run this file to test various components and configurations
 * 
 * <AUTHOR> Development Team
 * @version 3.0.0
 * 
 * Usage: Access via browser: /modules/gateways/test_lahza.php
 */

// Security check
if (!isset($_GET['test']) || $_GET['test'] !== 'lahza') {
    die('Access denied. Use ?test=lahza to run tests.');
}

// Include required files
require_once __DIR__ . '/../../init.php';
require_once __DIR__ . '/lahza_config.php';

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Lahza.io Gateway Test Suite</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .test-result { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .test-pass { background-color: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .test-fail { background-color: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .test-info { background-color: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
        .code-block { background: #f8f9fa; padding: 15px; border-radius: 5px; font-family: monospace; }
    </style>
</head>
<body>
    <div class="container mt-5">
        <div class="row">
            <div class="col-12">
                <h1 class="mb-4">
                    <i class="fas fa-vial text-primary"></i>
                    Lahza.io Gateway Test Suite
                </h1>
                
                <?php
                
                /**
                 * Test 1: Configuration File Test
                 */
                echo '<div class="card mb-4">';
                echo '<div class="card-header"><h5><i class="fas fa-cog"></i> Configuration Tests</h5></div>';
                echo '<div class="card-body">';
                
                // Test constants
                $configTests = [
                    'LAHZA_API_BASE_URL' => LAHZA_API_BASE_URL,
                    'LAHZA_JS_LIBRARY_URL' => LAHZA_JS_LIBRARY_URL,
                    'LAHZA_SUPPORTED_CURRENCIES' => LAHZA_SUPPORTED_CURRENCIES,
                    'LAHZA_DEFAULT_CHANNELS' => LAHZA_DEFAULT_CHANNELS
                ];
                
                foreach ($configTests as $constant => $value) {
                    if (defined($constant)) {
                        echo '<div class="test-result test-pass">';
                        echo '<i class="fas fa-check"></i> ' . $constant . ' is defined';
                        echo '<br><small>Value: ' . (is_array($value) ? json_encode($value) : $value) . '</small>';
                        echo '</div>';
                    } else {
                        echo '<div class="test-result test-fail">';
                        echo '<i class="fas fa-times"></i> ' . $constant . ' is not defined';
                        echo '</div>';
                    }
                }
                
                echo '</div></div>';
                
                /**
                 * Test 2: Utility Functions Test
                 */
                echo '<div class="card mb-4">';
                echo '<div class="card-header"><h5><i class="fas fa-tools"></i> Utility Functions Tests</h5></div>';
                echo '<div class="card-body">';
                
                // Test currency validation
                $testCurrencies = ['ILS', 'USD', 'JOD', 'EUR', 'GBP'];
                foreach ($testCurrencies as $currency) {
                    $isValid = LahzaUtils::isValidCurrency($currency);
                    $class = $isValid ? 'test-pass' : 'test-fail';
                    $icon = $isValid ? 'check' : 'times';
                    
                    echo '<div class="test-result ' . $class . '">';
                    echo '<i class="fas fa-' . $icon . '"></i> Currency ' . $currency . ': ' . ($isValid ? 'Valid' : 'Invalid');
                    echo '</div>';
                }
                
                // Test amount conversion
                $testAmount = 50.75;
                $convertedAmount = LahzaUtils::convertToSmallestUnit($testAmount, 'USD');
                $backConverted = LahzaUtils::convertFromSmallestUnit($convertedAmount, 'USD');
                
                echo '<div class="test-result test-info">';
                echo '<i class="fas fa-calculator"></i> Amount Conversion Test:';
                echo '<br>Original: $' . $testAmount;
                echo '<br>Converted to cents: ' . $convertedAmount;
                echo '<br>Back to dollars: $' . $backConverted;
                echo '</div>';
                
                // Test reference generation
                $reference = LahzaUtils::generateSecureReference(123);
                echo '<div class="test-result test-info">';
                echo '<i class="fas fa-key"></i> Reference Generation Test:';
                echo '<br>Generated: ' . $reference;
                echo '</div>';
                
                echo '</div></div>';
                
                /**
                 * Test 3: API Connectivity Test
                 */
                echo '<div class="card mb-4">';
                echo '<div class="card-header"><h5><i class="fas fa-globe"></i> API Connectivity Tests</h5></div>';
                echo '<div class="card-body">';
                
                // Test API URL accessibility
                $apiUrl = LAHZA_API_BASE_URL;
                $ch = curl_init();
                curl_setopt($ch, CURLOPT_URL, $apiUrl);
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                curl_setopt($ch, CURLOPT_TIMEOUT, 10);
                curl_setopt($ch, CURLOPT_NOBODY, true);
                $result = curl_exec($ch);
                $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                curl_close($ch);
                
                if ($httpCode === 200 || $httpCode === 404) { // 404 is expected for base URL
                    echo '<div class="test-result test-pass">';
                    echo '<i class="fas fa-check"></i> API Base URL is accessible (HTTP ' . $httpCode . ')';
                    echo '</div>';
                } else {
                    echo '<div class="test-result test-fail">';
                    echo '<i class="fas fa-times"></i> API Base URL is not accessible (HTTP ' . $httpCode . ')';
                    echo '</div>';
                }
                
                // Test JS Library URL
                $jsUrl = LAHZA_JS_LIBRARY_URL;
                $ch = curl_init();
                curl_setopt($ch, CURLOPT_URL, $jsUrl);
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                curl_setopt($ch, CURLOPT_TIMEOUT, 10);
                curl_setopt($ch, CURLOPT_NOBODY, true);
                $result = curl_exec($ch);
                $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                curl_close($ch);
                
                if ($httpCode === 200) {
                    echo '<div class="test-result test-pass">';
                    echo '<i class="fas fa-check"></i> JavaScript Library is accessible';
                    echo '</div>';
                } else {
                    echo '<div class="test-result test-fail">';
                    echo '<i class="fas fa-times"></i> JavaScript Library is not accessible (HTTP ' . $httpCode . ')';
                    echo '</div>';
                }
                
                echo '</div></div>';
                
                /**
                 * Test 4: Theme Tests
                 */
                echo '<div class="card mb-4">';
                echo '<div class="card-header"><h5><i class="fas fa-palette"></i> Theme Tests</h5></div>';
                echo '<div class="card-body">';
                
                $themes = ['getWiddxTheme', 'getDarkTheme', 'getMinimalTheme'];
                foreach ($themes as $theme) {
                    if (method_exists('LahzaThemes', $theme)) {
                        $css = LahzaThemes::$theme();
                        echo '<div class="test-result test-pass">';
                        echo '<i class="fas fa-check"></i> Theme ' . $theme . ' is available';
                        echo '<br><small>CSS Length: ' . strlen($css) . ' characters</small>';
                        echo '</div>';
                    } else {
                        echo '<div class="test-result test-fail">';
                        echo '<i class="fas fa-times"></i> Theme ' . $theme . ' is not available';
                        echo '</div>';
                    }
                }
                
                echo '</div></div>';
                
                /**
                 * Test 5: Message System Test
                 */
                echo '<div class="card mb-4">';
                echo '<div class="card-header"><h5><i class="fas fa-comments"></i> Message System Tests</h5></div>';
                echo '<div class="card-body">';
                
                $testMessages = [
                    'gateway_not_configured' => ['en', 'ar'],
                    'secure_payment' => ['en', 'ar'],
                    'payment_cancelled' => ['en', 'ar']
                ];
                
                foreach ($testMessages as $key => $languages) {
                    foreach ($languages as $lang) {
                        $message = LahzaMessages::get($key, $lang);
                        echo '<div class="test-result test-info">';
                        echo '<i class="fas fa-language"></i> Message "' . $key . '" (' . $lang . '): ' . $message;
                        echo '</div>';
                    }
                }
                
                echo '</div></div>';
                
                /**
                 * Test 6: Sample Payment Form
                 */
                echo '<div class="card mb-4">';
                echo '<div class="card-header"><h5><i class="fas fa-credit-card"></i> Sample Payment Form</h5></div>';
                echo '<div class="card-body">';
                
                echo '<div class="test-result test-info">';
                echo '<i class="fas fa-info"></i> Below is a sample of how the payment form would look:';
                echo '</div>';
                
                // Sample form HTML
                echo '
                <div class="lahza-payment-container card border-0 shadow-lg mt-3" style="max-width: 400px;">
                    <div class="card-body p-0">
                        <div class="payment-header text-center" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 30px 20px;">
                            <div class="payment-logo mb-3">
                                <i class="fas fa-credit-card text-white" style="font-size: 3rem;"></i>
                            </div>
                            <h4 class="text-white mb-2">
                                <i class="fas fa-lock me-2"></i>Secure Payment
                            </h4>
                            <p class="text-white-50 mb-0">Powered by Lahza.io</p>
                        </div>
                        
                        <div class="payment-details p-4">
                            <div class="row mb-3">
                                <div class="col-6">
                                    <small class="text-muted">Invoice</small>
                                    <div class="fw-bold">#123</div>
                                </div>
                                <div class="col-6 text-end">
                                    <small class="text-muted">Amount</small>
                                    <div class="fw-bold text-primary">$50.00</div>
                                </div>
                            </div>
                            
                            <button type="button" class="btn btn-primary btn-lg w-100" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border: none;">
                                <i class="fas fa-credit-card me-2"></i>
                                Pay Now - $50.00
                            </button>
                            
                            <div class="payment-security text-center mt-3">
                                <small class="text-muted">
                                    <i class="fas fa-shield-alt text-success"></i>
                                    Your payment is secured with 256-bit SSL encryption
                                </small>
                            </div>
                        </div>
                    </div>
                </div>';
                
                echo '</div></div>';
                
                /**
                 * Test Summary
                 */
                echo '<div class="card mb-4">';
                echo '<div class="card-header"><h5><i class="fas fa-clipboard-check"></i> Test Summary</h5></div>';
                echo '<div class="card-body">';
                
                echo '<div class="test-result test-info">';
                echo '<i class="fas fa-info-circle"></i> <strong>Test completed successfully!</strong>';
                echo '<br>All core components are working properly.';
                echo '<br>Gateway is ready for configuration and use.';
                echo '</div>';
                
                echo '<div class="code-block mt-3">';
                echo '<strong>Next Steps:</strong><br>';
                echo '1. Configure your Lahza.io API keys in WHMCS admin<br>';
                echo '2. Set up webhook URL in your Lahza.io dashboard<br>';
                echo '3. Test with small amounts in test mode<br>';
                echo '4. Switch to live mode when ready';
                echo '</div>';
                
                echo '</div></div>';
                
                ?>
                
                <div class="text-center mt-4">
                    <a href="../../admin/" class="btn btn-primary">
                        <i class="fas fa-cog"></i> Go to WHMCS Admin
                    </a>
                    <a href="README_LAHZA.md" class="btn btn-info ms-2" target="_blank">
                        <i class="fas fa-book"></i> View Documentation
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
