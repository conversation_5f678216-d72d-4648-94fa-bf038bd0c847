# WIDDX Template File Handling Guidelines
# إرشادات التعامل مع ملفات قالب WIDDX

## General Rules | القواعد العامة

### File Organization | تنظيم الملفات
1. **Directory Structure**
   - Maintain the existing directory structure
   - Place new files in appropriate directories
   - Follow WHMCS naming conventions

2. **File Naming**
   - Use lowercase letters
   - Use hyphens (-) for spaces
   - Include descriptive names
   - Example: `client-area-dashboard.tpl`

3. **File Extensions**
   - `.tpl` for template files
   - `.css` for stylesheets
   - `.js` for JavaScript files
   - `.php` for PHP includes

## Template Files | ملفات القالب

### Smarty Template Rules | قواعد قوالب Smarty
1. **Variable Usage**
   ```smarty
   {* Correct *}
   {$variable}
   
   {* Incorrect *}
   {$ variable}
   ```

2. **Comments**
   ```smarty
   {* Use comments for documentation *}
   {* Block comments for sections *}
   ```

3. **Conditional Statements**
   ```smarty
   {if $condition}
       {* Content *}
   {elseif $other_condition}
       {* Other content *}
   {else}
       {* Default content *}
   {/if}
   ```

### CSS Guidelines | إرشادات CSS
1. **Variable Usage**
   ```css
   :root {
       /* Use existing variables */
       --primary-color: var(--whmcs-primary);
       --secondary-color: var(--whmcs-secondary);
   }
   ```

2. **Class Naming**
   ```css
   /* Use BEM methodology */
   .block__element--modifier {
       /* Styles */
   }
   ```

3. **Media Queries**
   ```css
   /* Mobile first approach */
   @media (min-width: 768px) {
       /* Tablet styles */
   }
   @media (min-width: 1024px) {
       /* Desktop styles */
   }
   ```

## JavaScript Guidelines | إرشادات JavaScript

### Code Structure | هيكل الكود
1. **jQuery Usage**
   ```javascript
   $(document).ready(function() {
       // Initialize components
   });
   ```

2. **Function Naming**
   ```javascript
   // Use camelCase for functions
   function handleFormSubmit() {
       // Function code
   }
   ```

3. **Event Handling**
   ```javascript
   // Use event delegation
   $(document).on('click', '.button', function() {
       // Event handler
   });
   ```

## PHP Guidelines | إرشادات PHP

### Code Standards | معايير الكود
1. **Function Naming**
   ```php
   // Use camelCase for functions
   function processUserData() {
       // Function code
   }
   ```

2. **Class Structure**
   ```php
   class CustomModule {
       private $property;
       
       public function __construct() {
           // Constructor
       }
       
       public function processData() {
           // Method code
       }
   }
   ```

3. **Security Practices**
   ```php
   // Always sanitize input
   $input = filter_input(INPUT_POST, 'field', FILTER_SANITIZE_STRING);
   
   // Escape output
   echo htmlspecialchars($output, ENT_QUOTES, 'UTF-8');
   ```

## File Modification Rules | قواعد تعديل الملفات

### Template Modifications | تعديلات القالب
1. **Backup**
   - Create backup before modifications
   - Use version control
   - Document changes

2. **Testing**
   - Test in development environment
   - Check all affected pages
   - Verify responsive design

3. **Documentation**
   - Update documentation
   - Add comments
   - Record changes

### Asset Management | إدارة الأصول
1. **Images**
   - Optimize before use
   - Use appropriate formats
   - Maintain consistent naming

2. **CSS/JS**
   - Minify for production
   - Keep source files
   - Document dependencies

3. **Language Files**
   - Update all language versions
   - Maintain consistency
   - Document new strings

## Version Control | التحكم في الإصدارات

### Git Guidelines | إرشادات Git
1. **Branching**
   ```bash
   # Create feature branch
   git checkout -b feature/new-feature
   
   # Create hotfix branch
   git checkout -b hotfix/issue-fix
   ```

2. **Commits**
   ```bash
   # Use descriptive messages
   git commit -m "Add new feature: user dashboard"
   ```

3. **Merging**
   ```bash
   # Merge feature branch
   git checkout main
   git merge feature/new-feature
   ```

## Security Guidelines | إرشادات الأمان

### File Permissions | صلاحيات الملفات
1. **Directory Permissions**
   - 755 for directories
   - 644 for files
   - 600 for sensitive files

2. **Access Control**
   - Restrict sensitive directories
   - Use .htaccess when needed
   - Implement proper authentication

3. **Data Protection**
   - Encrypt sensitive data
   - Use secure connections
   - Implement proper validation

## Maintenance Procedures | إجراءات الصيانة

### Regular Maintenance | الصيانة المنتظمة
1. **File Cleanup**
   - Remove unused files
   - Archive old versions
   - Update documentation

2. **Performance**
   - Optimize assets
   - Clean cache
   - Update dependencies

3. **Security**
   - Regular updates
   - Security scans
   - Permission checks

---
*This documentation is provided in both English and Arabic for better accessibility.*
*تم تقديم هذا التوثيق باللغتين الإنجليزية والعربية لسهولة الوصول.* 