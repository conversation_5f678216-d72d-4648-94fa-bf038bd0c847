<?php
/**
 * Lahza.io Callback Test Tool
 * 
 * This tool simulates callback requests to test the callback handler
 * Use this to debug callback issues
 * 
 * <AUTHOR> Development Team
 * @version 2.0.0
 * 
 * IMPORTANT: Remove this file from production environment
 */

// Security check
if (!isset($_GET['test_key']) || $_GET['test_key'] !== 'callback_test_2024') {
    die('Access denied. Add ?test_key=callback_test_2024 to URL');
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Lahza.io Callback Test Tool</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body { background: #f8f9fa; }
        .test-container { max-width: 800px; margin: 2rem auto; }
        .code-block { background: #f8f9fa; padding: 1rem; border-radius: 5px; font-family: monospace; font-size: 0.9rem; }
    </style>
</head>
<body>
    <div class="container test-container">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h2><i class="fas fa-test-tube"></i> Lahza.io Callback Test Tool</h2>
            </div>
            <div class="card-body">
                
                <!-- Test GET Callback -->
                <div class="mb-4">
                    <h4>Test GET Callback (Redirect)</h4>
                    <p class="text-muted">This simulates a successful payment redirect from Lahza.io</p>
                    
                    <form method="post" action="#get-test">
                        <input type="hidden" name="test_type" value="get">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Transaction Reference</label>
                                    <input type="text" class="form-control" name="reference" 
                                           placeholder="WHMCS_123_1234567890_abc123" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Status</label>
                                    <select class="form-control" name="status">
                                        <option value="success">Success</option>
                                        <option value="failed">Failed</option>
                                        <option value="cancelled">Cancelled</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <button type="submit" class="btn btn-primary">Test GET Callback</button>
                    </form>
                </div>

                <!-- Test POST Webhook -->
                <div class="mb-4">
                    <h4>Test POST Webhook</h4>
                    <p class="text-muted">This simulates a webhook notification from Lahza.io</p>
                    
                    <form method="post" action="#webhook-test">
                        <input type="hidden" name="test_type" value="webhook">
                        <div class="mb-3">
                            <label class="form-label">Webhook Event</label>
                            <select class="form-control" name="event">
                                <option value="charge.success">charge.success</option>
                                <option value="refund.processed">refund.processed</option>
                            </select>
                        </div>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">Transaction Reference</label>
                                    <input type="text" class="form-control" name="reference" 
                                           placeholder="WHMCS_123_1234567890_abc123" required>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">Invoice ID</label>
                                    <input type="number" class="form-control" name="invoice_id" 
                                           placeholder="123" required>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">Amount (in cents)</label>
                                    <input type="number" class="form-control" name="amount" 
                                           placeholder="10000" required>
                                </div>
                            </div>
                        </div>
                        <button type="submit" class="btn btn-success">Test Webhook</button>
                    </form>
                </div>

                <!-- Test Results -->
                <?php if ($_POST): ?>
                <div class="mb-4" id="test-results">
                    <h4>Test Results</h4>
                    
                    <?php
                    $testType = $_POST['test_type'] ?? '';
                    
                    if ($testType === 'get') {
                        // Test GET callback
                        $reference = $_POST['reference'] ?? '';
                        $status = $_POST['status'] ?? 'success';
                        
                        $callbackUrl = 'http' . (isset($_SERVER['HTTPS']) ? 's' : '') . '://' . 
                                      $_SERVER['HTTP_HOST'] . 
                                      dirname($_SERVER['REQUEST_URI']) . '/lahza.php';
                        
                        $testUrl = $callbackUrl . '?reference=' . urlencode($reference) . '&status=' . urlencode($status);
                        
                        echo '<div class="alert alert-info">';
                        echo '<strong>Testing GET Callback:</strong><br>';
                        echo '<strong>URL:</strong> <code>' . htmlspecialchars($testUrl) . '</code><br>';
                        echo '</div>';
                        
                        // Make the request
                        $ch = curl_init();
                        curl_setopt($ch, CURLOPT_URL, $testUrl);
                        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, false);
                        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
                        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
                        
                        $response = curl_exec($ch);
                        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                        $error = curl_error($ch);
                        curl_close($ch);
                        
                        if ($error) {
                            echo '<div class="alert alert-danger"><strong>cURL Error:</strong> ' . $error . '</div>';
                        } else {
                            echo '<div class="alert alert-' . ($httpCode >= 200 && $httpCode < 400 ? 'success' : 'warning') . '">';
                            echo '<strong>HTTP Code:</strong> ' . $httpCode . '<br>';
                            echo '<strong>Response:</strong><br>';
                            echo '<div class="code-block">' . htmlspecialchars($response) . '</div>';
                            echo '</div>';
                        }
                        
                    } elseif ($testType === 'webhook') {
                        // Test webhook
                        $event = $_POST['event'] ?? 'charge.success';
                        $reference = $_POST['reference'] ?? '';
                        $invoiceId = $_POST['invoice_id'] ?? '';
                        $amount = $_POST['amount'] ?? 10000;
                        
                        $webhookData = [
                            'event' => $event,
                            'data' => [
                                'reference' => $reference,
                                'status' => 'success',
                                'amount' => (int)$amount,
                                'currency' => 'USD',
                                'fees' => 100,
                                'metadata' => [
                                    'invoice_id' => $invoiceId,
                                    'client_id' => '1',
                                    'company_name' => 'Test Company'
                                ]
                            ]
                        ];
                        
                        $payload = json_encode($webhookData);
                        
                        $callbackUrl = 'http' . (isset($_SERVER['HTTPS']) ? 's' : '') . '://' . 
                                      $_SERVER['HTTP_HOST'] . 
                                      dirname($_SERVER['REQUEST_URI']) . '/lahza.php';
                        
                        echo '<div class="alert alert-info">';
                        echo '<strong>Testing Webhook:</strong><br>';
                        echo '<strong>URL:</strong> <code>' . htmlspecialchars($callbackUrl) . '</code><br>';
                        echo '<strong>Payload:</strong><br>';
                        echo '<div class="code-block">' . htmlspecialchars($payload) . '</div>';
                        echo '</div>';
                        
                        // Make the request
                        $ch = curl_init();
                        curl_setopt($ch, CURLOPT_URL, $callbackUrl);
                        curl_setopt($ch, CURLOPT_POST, true);
                        curl_setopt($ch, CURLOPT_POSTFIELDS, $payload);
                        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                        curl_setopt($ch, CURLOPT_HTTPHEADER, [
                            'Content-Type: application/json',
                            'X-Lahza-Signature: test_signature'
                        ]);
                        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
                        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
                        
                        $response = curl_exec($ch);
                        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                        $error = curl_error($ch);
                        curl_close($ch);
                        
                        if ($error) {
                            echo '<div class="alert alert-danger"><strong>cURL Error:</strong> ' . $error . '</div>';
                        } else {
                            echo '<div class="alert alert-' . ($httpCode >= 200 && $httpCode < 400 ? 'success' : 'warning') . '">';
                            echo '<strong>HTTP Code:</strong> ' . $httpCode . '<br>';
                            echo '<strong>Response:</strong><br>';
                            echo '<div class="code-block">' . htmlspecialchars($response) . '</div>';
                            echo '</div>';
                        }
                    }
                    ?>
                </div>
                <?php endif; ?>

                <!-- Instructions -->
                <div class="mb-4">
                    <h4>Instructions</h4>
                    <div class="alert alert-info">
                        <strong>How to use this tool:</strong>
                        <ol>
                            <li><strong>GET Callback Test:</strong> Simulates the redirect that happens after payment completion</li>
                            <li><strong>Webhook Test:</strong> Simulates the POST notification sent by Lahza.io</li>
                            <li>Use real transaction references from completed payments for accurate testing</li>
                            <li>Check WHMCS Gateway Logs after testing to see what was logged</li>
                            <li>Make sure your callback URL is accessible from the internet</li>
                        </ol>
                    </div>
                </div>

                <!-- Callback URL Info -->
                <div class="mb-4">
                    <h4>Callback URL Information</h4>
                    <div class="alert alert-secondary">
                        <strong>Your Callback URL:</strong><br>
                        <code><?php 
                        echo 'http' . (isset($_SERVER['HTTPS']) ? 's' : '') . '://' . 
                             $_SERVER['HTTP_HOST'] . 
                             dirname($_SERVER['REQUEST_URI']) . '/lahza.php';
                        ?></code><br><br>
                        
                        <strong>Configure this URL in your Lahza.io dashboard as:</strong>
                        <ul>
                            <li><strong>Callback URL:</strong> For payment redirects</li>
                            <li><strong>Webhook URL:</strong> For payment notifications</li>
                        </ul>
                    </div>
                </div>

                <!-- Security Warning -->
                <div class="alert alert-warning">
                    <strong>⚠️ Security Warning:</strong> This test file should be removed from production environments. 
                    It's intended for development and troubleshooting purposes only.
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
