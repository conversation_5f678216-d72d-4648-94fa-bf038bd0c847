{if !empty($productGroups) || $registerdomainenabled || $transferdomainenabled}
<section class="productgroups-section transparent-section">
    <div class="container">

        <h2 class="text-center m-4 ptb-100">{lang key='clientHomePanels.productsAndServices'}</h2>

        <!-- سلايدر Owl Carousel -->
        <div class="owl-carousel owl-theme">
            {foreach $productGroups as $productGroup}
                <div class="item">
                    <div class="card h-100 mb-3">
                        <div class="widdxcard-body d-flex flex-column text-center">
                            {if !empty($productGroup->name)}
                                <img src="{$WEB_ROOT}/templates/widdx/frontend/assets/img/product-groups/{$productGroup->name|lower}.png"
                                    onerror="this.onerror=null;this.src='{$WEB_ROOT}/templates/widdx/frontend/assets/img/product-groups/{$productGroup->name|lower}.svg';"
                                    alt="{$productGroup->name|default:'Unknown Product'}" class="img-fluid mx-auto mb-3"
                                    style="max-width: 200px; max-height: 200px;">
                            {else}
                                <img src="{$WEB_ROOT}/templates/widdx/frontend/assets/img/product-groups/default.png"
                                    alt="{lang key='unknownProduct'}" class="img-fluid mx-auto mb-3"
                                    style="max-width: 200px; max-height: 200px;">
                            {/if}
                            <h3 class="card-title mt-auto">
                                {$productGroup->name|default:'Unknown Product'}
                            </h3>
                            <p>{$productGroup->tagline}</p>
                            <a href="{$productGroup->getRoutePath()}" class="btn btn-outline-primary btn-block mt-auto">
                                {lang key='browseProducts'}
                            </a>
                        </div>
                    </div>
                </div>
            {/foreach}
        </div>
    {/if}
    </div>
</section>
