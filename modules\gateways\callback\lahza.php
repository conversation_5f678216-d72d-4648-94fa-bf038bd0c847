<?php
/**
 * Lahza.io Payment Gateway Callback Handler for WHMCS WIDDX
 *
 * Complete callback handler with full Lahza.io API v2024 integration
 * Supports webhooks, redirects, card type detection, and comprehensive logging
 *
 * <AUTHOR> Development Team
 * @version 3.0.0
 * @link https://docs.lahza.io/payments/webhooks
 * @link https://api-docs.lahza.io/
 *
 * Features:
 * - Webhook signature verification
 * - Card type and bank information extraction
 * - Comprehensive transaction logging
 * - Error handling and recovery
 * - IP whitelist validation
 * - Duplicate transaction prevention
 */

// Require libraries needed for gateway module functions.
require_once __DIR__ . '/../../../init.php';
require_once __DIR__ . '/../../../includes/gatewayfunctions.php';
require_once __DIR__ . '/../../../includes/invoicefunctions.php';

// Detect module name from filename.
$gatewayModuleName = basename(__FILE__, '.php');

// Fetch gateway configuration parameters.
$gatewayParams = getGatewayVariables($gatewayModuleName);

// Die if module is not active.
if (!$gatewayParams['type']) {
    lahza_logActivity("Module Not Activated", $_REQUEST, true);
    header("HTTP/1.1 403 Forbidden");
    exit("Module Not Activated");
}

/**
 * Log activity for debugging - Always log important events
 */
function lahza_logActivity($message, $data = null, $forceLog = false) {
    global $gatewayParams, $gatewayModuleName;

    // Always log important events, or when logging is enabled
    if ($forceLog || $gatewayParams['enableLogging']) {
        $logMessage = '[Lahza.io] ' . $message;
        if ($data) {
            $logMessage .= ' - Data: ' . json_encode($data);
        }
        if (function_exists('logTransaction')) {
            logTransaction($gatewayModuleName, $logMessage, 'Debug');
        }
    }
}

/**
 * Verify payment with Lahza.io API and extract comprehensive transaction data
 */
function lahza_verifyPayment($reference, $secretKey) {
    $url = 'https://api.lahza.io/transaction/verify/' . $reference;

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Authorization: Bearer ' . $secretKey,
        'Content-Type: application/json',
        'Cache-Control: no-cache',
        'User-Agent: WHMCS-Lahza/3.0'
    ]);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true);

    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);

    if ($error) {
        lahza_logActivity('cURL Error during verification', ['error' => $error, 'reference' => $reference], true);
        return false;
    }

    if ($httpCode !== 200) {
        lahza_logActivity('HTTP Error during verification', [
            'code' => $httpCode,
            'response' => $response,
            'reference' => $reference
        ], true);
        return false;
    }

    $result = json_decode($response, true);

    if (!$result || !isset($result['status']) || $result['status'] !== true) {
        lahza_logActivity('Invalid verification response', [
            'result' => $result,
            'reference' => $reference
        ], true);
        return false;
    }

    // Extract comprehensive transaction data
    $transactionData = $result['data'];

    // Add card type and bank information if available
    if (isset($transactionData['authorization'])) {
        $auth = $transactionData['authorization'];
        $transactionData['card_type'] = $auth['card_type'] ?? null;
        $transactionData['bank'] = $auth['bank'] ?? null;
        $transactionData['last4'] = $auth['last4'] ?? null;
        $transactionData['exp_month'] = $auth['exp_month'] ?? null;
        $transactionData['exp_year'] = $auth['exp_year'] ?? null;
        $transactionData['brand'] = $auth['brand'] ?? null;
    }

    lahza_logActivity('Payment verification successful', [
        'reference' => $reference,
        'status' => $transactionData['status'],
        'amount' => $transactionData['amount'],
        'currency' => $transactionData['currency'],
        'card_type' => $transactionData['card_type'] ?? 'N/A',
        'bank' => $transactionData['bank'] ?? 'N/A'
    ], true);

    return $transactionData;
}

/**
 * Verify webhook signature according to Lahza.io documentation
 */
function lahza_verifyWebhookSignature($payload, $signature, $secretKey) {
    // Remove 'sha256=' prefix if present
    $signature = str_replace('sha256=', '', $signature);

    $expectedSignature = hash_hmac('sha256', $payload, $secretKey);

    lahza_logActivity('Webhook signature verification', [
        'provided_signature' => $signature,
        'expected_signature' => $expectedSignature,
        'payload_length' => strlen($payload)
    ]);

    return hash_equals($expectedSignature, $signature);
}

/**
 * Validate webhook IP address
 */
function lahza_validateWebhookIP($ipWhitelist) {
    $clientIP = $_SERVER['REMOTE_ADDR'] ?? '';
    $forwardedIP = $_SERVER['HTTP_X_FORWARDED_FOR'] ?? '';

    // Get real IP address
    $realIP = $forwardedIP ? explode(',', $forwardedIP)[0] : $clientIP;
    $realIP = trim($realIP);

    $allowedIPs = array_map('trim', explode(',', $ipWhitelist));

    lahza_logActivity('IP validation', [
        'client_ip' => $clientIP,
        'forwarded_ip' => $forwardedIP,
        'real_ip' => $realIP,
        'allowed_ips' => $allowedIPs
    ]);

    return in_array($realIP, $allowedIPs);
}

/**
 * Extract card information for display
 */
function lahza_extractCardInfo($transactionData) {
    $cardInfo = [
        'type' => 'Unknown',
        'bank' => 'Unknown',
        'last4' => '',
        'brand' => '',
        'display' => 'Card Payment'
    ];

    if (isset($transactionData['authorization'])) {
        $auth = $transactionData['authorization'];

        $cardInfo['type'] = $auth['card_type'] ?? 'Unknown';
        $cardInfo['bank'] = $auth['bank'] ?? 'Unknown';
        $cardInfo['last4'] = $auth['last4'] ?? '';
        $cardInfo['brand'] = $auth['brand'] ?? '';

        // Create display string
        if ($cardInfo['last4']) {
            $cardInfo['display'] = $cardInfo['brand'] . ' ending in ' . $cardInfo['last4'];
            if ($cardInfo['bank'] !== 'Unknown') {
                $cardInfo['display'] .= ' (' . $cardInfo['bank'] . ')';
            }
        }
    }

    return $cardInfo;
}

// Handle different types of requests
$requestMethod = $_SERVER['REQUEST_METHOD'];
$success = false;
$transactionId = '';
$paymentAmount = '';
$paymentFee = '';
$invoiceId = '';
$transactionStatus = '';

// Always log callback attempts
lahza_logActivity('Callback received', [
    'method' => $requestMethod,
    'get' => $_GET,
    'post_exists' => !empty(file_get_contents('php://input')),
    'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
    'ip' => $_SERVER['REMOTE_ADDR'] ?? '',
    'forwarded_ip' => $_SERVER['HTTP_X_FORWARDED_FOR'] ?? ''
], true);

if ($requestMethod === 'POST') {
    // Handle webhook notification
    $payload = file_get_contents('php://input');
    $signature = $_SERVER['HTTP_X_LAHZA_SIGNATURE'] ?? '';

    lahza_logActivity('Webhook received', [
        'payload_length' => strlen($payload),
        'signature_present' => !empty($signature),
        'content_type' => $_SERVER['CONTENT_TYPE'] ?? ''
    ], true);

    // Validate webhook IP if whitelist is configured
    if (!empty($gatewayParams['ipWhitelist'])) {
        if (!lahza_validateWebhookIP($gatewayParams['ipWhitelist'])) {
            lahza_logActivity('Webhook IP not whitelisted', [
                'ip' => $_SERVER['REMOTE_ADDR'] ?? '',
                'forwarded_ip' => $_SERVER['HTTP_X_FORWARDED_FOR'] ?? ''
            ], true);
            http_response_code(403);
            die('IP not whitelisted');
        }
    }

    // Verify webhook signature
    if (!lahza_verifyWebhookSignature($payload, $signature, $gatewayParams['secretKey'])) {
        lahza_logActivity('Invalid webhook signature', [
            'signature' => $signature,
            'payload_sample' => substr($payload, 0, 100)
        ], true);
        http_response_code(401);
        die('Invalid signature');
    }

    $webhookData = json_decode($payload, true);

    if (!$webhookData || !isset($webhookData['event'])) {
        lahza_logActivity('Invalid webhook data', ['payload_sample' => substr($payload, 0, 100)], true);
        http_response_code(400);
        die('Invalid webhook data');
    }

    $event = $webhookData['event'];
    $data = $webhookData['data'] ?? [];

    lahza_logActivity('Processing webhook event', [
        'event' => $event,
        'has_data' => !empty($data),
        'reference' => $data['reference'] ?? 'N/A'
    ], true);

    // Handle different webhook events
    switch ($event) {
        case 'charge.success':
            if (isset($data['reference']) && isset($data['metadata']['invoice_id'])) {
                $transactionId = $data['reference'];
                $invoiceId = $data['metadata']['invoice_id'];
                $paymentAmount = $data['amount'] / 100; // Convert from cents
                $paymentFee = ($data['fees'] ?? 0) / 100;
                $transactionStatus = $data['status'];

                // Extract card information
                $cardInfo = lahza_extractCardInfo($data);

                if ($transactionStatus === 'success') {
                    $success = true;
                    lahza_logActivity('Payment successful via webhook', [
                        'invoice_id' => $invoiceId,
                        'transaction_id' => $transactionId,
                        'amount' => $paymentAmount,
                        'card_info' => $cardInfo
                    ], true);
                }
            }
            break;

        case 'refund.processed':
            lahza_logActivity('Refund processed via webhook', [
                'reference' => $data['reference'] ?? 'N/A',
                'amount' => ($data['amount'] ?? 0) / 100,
                'reason' => $data['reason'] ?? 'N/A'
            ], true);
            break;

        default:
            lahza_logActivity('Unhandled webhook event', [
                'event' => $event,
                'data_keys' => array_keys($data)
            ], true);
            break;
    }

    // Respond with 200 OK to acknowledge webhook
    http_response_code(200);
    echo 'OK';

} else {
    // Handle GET request (redirect callback)
    $reference = $_GET['reference'] ?? '';
    $status = $_GET['status'] ?? '';

    lahza_logActivity('Redirect callback received', [
        'reference' => $reference,
        'status' => $status,
        'all_params' => $_GET
    ], true);

    if (empty($reference)) {
        lahza_logActivity('Missing transaction reference in callback', $_GET, true);
        die('Missing transaction reference');
    }

    // Verify payment with Lahza.io API
    $paymentData = lahza_verifyPayment($reference, $gatewayParams['secretKey']);

    if (!$paymentData) {
        lahza_logActivity('Payment verification failed', [
            'reference' => $reference,
            'status' => $status
        ], true);
        die('Payment verification failed');
    }

    $transactionId = $paymentData['reference'];
    $paymentAmount = $paymentData['amount'] / 100; // Convert from cents
    $paymentFee = ($paymentData['fees'] ?? 0) / 100;
    $transactionStatus = $paymentData['status'];

    // Extract card information
    $cardInfo = lahza_extractCardInfo($paymentData);

    // Extract invoice ID from metadata or reference
    if (isset($paymentData['metadata']['invoice_id'])) {
        $invoiceId = $paymentData['metadata']['invoice_id'];
        lahza_logActivity('Invoice ID found in metadata', [
            'invoice_id' => $invoiceId,
            'card_info' => $cardInfo
        ], true);
    } else {
        // Try to extract from reference if it follows our pattern
        if (preg_match('/^WHMCS_(\d+)_/', $transactionId, $matches)) {
            $invoiceId = $matches[1];
            lahza_logActivity('Invoice ID extracted from reference', [
                'invoice_id' => $invoiceId,
                'reference' => $transactionId,
                'card_info' => $cardInfo
            ], true);
        } else {
            // Try alternative patterns
            if (preg_match('/invoice[_-]?(\d+)/i', $transactionId, $matches)) {
                $invoiceId = $matches[1];
                lahza_logActivity('Invoice ID found using alternative pattern', [
                    'invoice_id' => $invoiceId,
                    'reference' => $transactionId,
                    'card_info' => $cardInfo
                ], true);
            } else {
                lahza_logActivity('Could not extract invoice ID', [
                    'reference' => $transactionId,
                    'metadata' => $paymentData['metadata'] ?? [],
                    'card_info' => $cardInfo
                ], true);
            }
        }
    }

    if ($transactionStatus === 'success') {
        $success = true;
        lahza_logActivity('Payment verified successfully', [
            'invoice_id' => $invoiceId,
            'transaction_id' => $transactionId,
            'amount' => $paymentAmount,
            'card_info' => $cardInfo
        ], true);
    } else {
        lahza_logActivity('Payment not successful', [
            'status' => $transactionStatus,
            'reference' => $reference,
            'card_info' => $cardInfo
        ], true);
    }
}

// Process the payment if successful
if ($success && !empty($invoiceId) && !empty($transactionId)) {

    lahza_logActivity('Processing payment', [
        'invoice_id' => $invoiceId,
        'transaction_id' => $transactionId,
        'amount' => $paymentAmount,
        'fee' => $paymentFee,
        'card_info' => $cardInfo ?? []
    ], true);

    try {
        /**
         * Validate Callback Invoice ID.
         */
        if (function_exists('checkCbInvoiceID')) {
            $invoiceId = checkCbInvoiceID($invoiceId, $gatewayParams['name']);
            lahza_logActivity('Invoice ID validated', ['invoice_id' => $invoiceId], true);
        }

        /**
         * Check Callback Transaction ID.
         */
        if (function_exists('checkCbTransID')) {
            checkCbTransID($transactionId);
            lahza_logActivity('Transaction ID validated', ['transaction_id' => $transactionId], true);
        }
    } catch (Exception $e) {
        lahza_logActivity('Validation error', [
            'error' => $e->getMessage(),
            'invoice_id' => $invoiceId,
            'transaction_id' => $transactionId
        ], true);

        if ($requestMethod === 'GET') {
            die('Validation error: ' . $e->getMessage());
        } else {
            http_response_code(400);
            die('Validation error');
        }
    }

    /**
     * Log Transaction with comprehensive information
     */
    $logData = [
        'Transaction ID' => $transactionId,
        'Invoice ID' => $invoiceId,
        'Amount' => $paymentAmount,
        'Fee' => $paymentFee,
        'Status' => $transactionStatus,
        'Method' => $requestMethod,
    ];

    // Add card information if available and enabled
    if (!empty($cardInfo) && $gatewayParams['showCardType']) {
        $logData['Card Type'] = $cardInfo['type'];
        $logData['Bank'] = $cardInfo['bank'];
        $logData['Card Display'] = $cardInfo['display'];
        if (!empty($cardInfo['last4'])) {
            $logData['Last 4 Digits'] = $cardInfo['last4'];
        }
    }

    if (function_exists('logTransaction')) {
        logTransaction($gatewayParams['name'], $logData, 'Successful');
    }

    /**
     * Add Invoice Payment
     */
    try {
        if (function_exists('addInvoicePayment')) {
            addInvoicePayment(
                $invoiceId,
                $transactionId,
                $paymentAmount,
                $paymentFee,
                $gatewayModuleName
            );
        }

        lahza_logActivity('Payment processed successfully', [
            'invoice_id' => $invoiceId,
            'transaction_id' => $transactionId,
            'amount' => $paymentAmount,
            'fee' => $paymentFee,
            'card_info' => $cardInfo ?? []
        ], true);

    } catch (Exception $e) {
        lahza_logActivity('Payment processing error', [
            'error' => $e->getMessage(),
            'invoice_id' => $invoiceId,
            'transaction_id' => $transactionId,
            'amount' => $paymentAmount
        ], true);

        if ($requestMethod === 'GET') {
            die('Payment processing error: ' . $e->getMessage());
        } else {
            http_response_code(500);
            die('Payment processing error');
        }
    }

    // Redirect to invoice if this was a GET request
    if ($requestMethod === 'GET') {
        header('Location: ' . $gatewayParams['systemurl'] . 'viewinvoice.php?id=' . $invoiceId . '&paymentsuccess=true');
        exit;
    }

} elseif ($requestMethod === 'GET') {
    // Handle failed payment redirect or missing data
    if (!empty($invoiceId)) {
        lahza_logActivity('Payment failed or cancelled', [
            'invoice_id' => $invoiceId,
            'reference' => $_GET['reference'] ?? '',
            'success' => $success,
            'transaction_id' => $transactionId
        ], true);
        header('Location: ' . $gatewayParams['systemurl'] . 'viewinvoice.php?id=' . $invoiceId . '&paymentfailed=true');
    } else {
        lahza_logActivity('No invoice ID found in callback', [
            'reference' => $_GET['reference'] ?? '',
            'success' => $success,
            'transaction_id' => $transactionId,
            'get_params' => $_GET
        ], true);
        header('Location: ' . $gatewayParams['systemurl'] . 'clientarea.php');
    }
    exit;
} else {
    // Handle POST webhook without redirect
    lahza_logActivity('Webhook processed', [
        'success' => $success,
        'invoice_id' => $invoiceId,
        'transaction_id' => $transactionId
    ], true);
}
