# WIDDXCrypto Payment Gateway Module Specification

## Overview
Custom WHMCS payment gateway for manual USDT (TRC20) payments with proof of payment verification.

## Module Structure
```
modules/gateways/
├── widdxcrypto.php (Main gateway file)
└── callback/ (Future webhook handling)
```

## Core Functions
1. `widdxcrypto_config()` - Gateway configuration
2. `widdxcrypto_link()` - Payment page display
3. `widdxcrypto_capture()` - Webhook handler (future)

## Configuration Fields
| Field | Type | Description | Default |
|-------|------|-------------|---------|
| walletAddress | text | USDT wallet address | - |
| network | dropdown | TRC20/ERC20/BEP20 | TRC20 |
| instructions | textarea | Payment instructions | - |

## Payment Flow
```mermaid
sequenceDiagram
    Client->>Gateway: Views invoice
    Gateway->>Client: Shows wallet + QR
    Client->>Gateway: Uploads TXID + proof
    Gateway->>WHMCS: Stores payment data
    WHMCS->>Admin: Sends notification
```

## Technical Requirements
- PHP 7.4+
- WHMCS 8.x+
- QR code generation library
- File upload handling
- Secure input validation

## Security Measures
- Wallet address validation
- File upload restrictions (2MB max, image/PDF only)
- CSRF protection
- Input sanitization

## Future Enhancements
- Webhook auto-verification
- Multi-currency support
- Admin dashboard for proofs