/**
 * Modern Tools Section JavaScript
 * For WHMCS WIDDX Template
 * Professional animations and interactions for tools section
 */

document.addEventListener('DOMContentLoaded', function() {
    // Initialize AOS animations if available
    if (typeof AOS !== 'undefined') {
        AOS.init({
            duration: 800,
            easing: 'ease-out-cubic',
            once: true,
            offset: 100
        });
    }

    // Tool cards hover enhancement
    const toolCards = document.querySelectorAll('.modern-tool-card');
    toolCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            // Add glow effect to icon
            const iconGlow = this.querySelector('.icon-glow');
            if (iconGlow) {
                iconGlow.style.opacity = '0.2';
                iconGlow.style.transform = 'translate(-50%, -50%) scale(1.1)';
            }

            // Enhance tool icon animation
            const toolIcon = this.querySelector('.tool-icon');
            if (toolIcon) {
                toolIcon.style.transform = 'scale(1.05) rotate(3deg)';
            }
        });

        card.addEventListener('mouseleave', function() {
            // Reset glow effect
            const iconGlow = this.querySelector('.icon-glow');
            if (iconGlow) {
                iconGlow.style.opacity = '0';
                iconGlow.style.transform = 'translate(-50%, -50%) scale(1)';
            }

            // Reset tool icon
            const toolIcon = this.querySelector('.tool-icon');
            if (toolIcon) {
                toolIcon.style.transform = 'scale(1) rotate(0deg)';
            }
        });
    });

    // Feature items interactive enhancement
    const featureItems = document.querySelectorAll('.feature-item');
    featureItems.forEach(item => {
        item.addEventListener('mouseenter', function() {
            this.style.background = 'var(--bg-secondary)';
            this.style.transform = 'translateX(5px)';
        });

        item.addEventListener('mouseleave', function() {
            this.style.background = 'transparent';
            this.style.transform = 'translateX(0)';
        });
    });

    // Modern button ripple effect
    const modernButtons = document.querySelectorAll('.modern-btn');
    modernButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            const ripple = this.querySelector('.btn-ripple');
            if (ripple) {
                // Reset ripple
                ripple.style.transform = 'scale(0)';
                
                // Trigger ripple animation
                setTimeout(() => {
                    ripple.style.transform = 'scale(1)';
                }, 10);
                
                // Reset after animation
                setTimeout(() => {
                    ripple.style.transform = 'scale(0)';
                }, 600);
            }
        });

        // Enhanced hover effects for buttons
        button.addEventListener('mouseenter', function() {
            const btnText = this.querySelector('.btn-text');
            const btnIcon = this.querySelector('.btn-icon');
            
            if (btnText) btnText.style.transform = 'translateX(-5px)';
            if (btnIcon) btnIcon.style.transform = 'translateX(5px)';
        });

        button.addEventListener('mouseleave', function() {
            const btnText = this.querySelector('.btn-text');
            const btnIcon = this.querySelector('.btn-icon');
            
            if (btnText) btnText.style.transform = 'translateX(0)';
            if (btnIcon) btnIcon.style.transform = 'translateX(0)';
        });
    });

    // Feature highlights enhanced interactions
    const featureHighlights = document.querySelectorAll('.feature-highlight');
    featureHighlights.forEach(highlight => {
        highlight.addEventListener('mouseenter', function() {
            // Enhanced hover effect for feature icons
            const featureIcon = this.querySelector('.feature-icon');
            if (featureIcon) {
                featureIcon.style.transform = 'scale(1.15) rotate(8deg)';
                
                // Trigger icon glow effect
                const iconAfter = featureIcon.querySelector('::after');
                if (featureIcon.style.setProperty) {
                    featureIcon.style.setProperty('--icon-glow-scale', '1.5');
                    featureIcon.style.setProperty('--icon-glow-opacity', '0.2');
                }
            }

            // Enhanced background gradient
            this.style.setProperty('--gradient-opacity', '0.05');
        });

        highlight.addEventListener('mouseleave', function() {
            // Reset feature icon
            const featureIcon = this.querySelector('.feature-icon');
            if (featureIcon) {
                featureIcon.style.transform = 'scale(1) rotate(0deg)';
                
                if (featureIcon.style.setProperty) {
                    featureIcon.style.setProperty('--icon-glow-scale', '0');
                    featureIcon.style.setProperty('--icon-glow-opacity', '0');
                }
            }

            // Reset background
            this.style.setProperty('--gradient-opacity', '0');
        });
    });

    // Floating shapes animation enhancement
    const floatingShapes = document.querySelectorAll('.floating-shape');
    let animationId;
    
    function animateShapes() {
        floatingShapes.forEach((shape, index) => {
            const time = Date.now() * 0.001;
            const offset = index * 2;
            const x = Math.sin(time + offset) * 5;
            const y = Math.cos(time + offset) * 3;
            const rotation = Math.sin(time + offset) * 10;
            
            shape.style.transform = `translate(${x}px, ${y}px) rotate(${rotation}deg)`;
        });
        
        animationId = requestAnimationFrame(animateShapes);
    }

    // Start floating animation only if shapes exist and user doesn't prefer reduced motion
    if (floatingShapes.length > 0 && !window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
        animateShapes();
    }

    // Intersection Observer for progressive enhancement
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const sectionObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('section-visible');
                
                // Trigger staggered animations for tool cards
                const toolCards = entry.target.querySelectorAll('.modern-tool-card');
                toolCards.forEach((card, index) => {
                    setTimeout(() => {
                        card.classList.add('card-animated');
                    }, index * 100);
                });
            }
        });
    }, observerOptions);

    // Observe tools section
    const toolsSection = document.querySelector('.modern-tools-section');
    if (toolsSection) {
        sectionObserver.observe(toolsSection);
    }

    // Observe why choose section
    const whyChooseSection = document.querySelector('.why-choose-section');
    if (whyChooseSection) {
        sectionObserver.observe(whyChooseSection);
    }

    // Smooth scroll for tool links
    const toolLinks = document.querySelectorAll('.modern-btn[href^="/page/"]');
    toolLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            // Add loading state
            this.style.opacity = '0.7';
            this.style.pointerEvents = 'none';
            
            // Reset after a short delay (in case navigation is slow)
            setTimeout(() => {
                this.style.opacity = '1';
                this.style.pointerEvents = 'auto';
            }, 2000);
        });
    });

    // Performance optimization: Clean up animations when section is not visible
    const performanceObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (!entry.isIntersecting && animationId) {
                cancelAnimationFrame(animationId);
                animationId = null;
            } else if (entry.isIntersecting && !animationId && floatingShapes.length > 0) {
                animateShapes();
            }
        });
    }, { threshold: 0 });

    if (toolsSection) {
        performanceObserver.observe(toolsSection);
    }
});

// Accessibility enhancements
document.addEventListener('keydown', function(e) {
    // Enhanced keyboard navigation for tool cards
    if (e.key === 'Enter' || e.key === ' ') {
        const focusedElement = document.activeElement;
        if (focusedElement.classList.contains('modern-tool-card')) {
            const link = focusedElement.querySelector('.modern-btn');
            if (link) {
                e.preventDefault();
                link.click();
            }
        }
    }
});

// Reduce motion support
if (window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
    document.addEventListener('DOMContentLoaded', function() {
        const style = document.createElement('style');
        style.textContent = `
            .modern-tools-section *,
            .modern-tools-section *::before,
            .modern-tools-section *::after {
                animation-duration: 0.01ms !important;
                animation-iteration-count: 1 !important;
                transition-duration: 0.01ms !important;
            }
            .floating-shape {
                animation: none !important;
            }
        `;
        document.head.appendChild(style);
    });
}
