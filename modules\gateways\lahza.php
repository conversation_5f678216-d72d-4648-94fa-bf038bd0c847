<?php
/**
 * Lahza.io Payment Gateway Module for WHMCS WIDDX Template
 *
 * Complete integration with Lahza.io API v2024
 * Supports all payment channels, card types, and comprehensive transaction data
 * Optimized for WHMCS 8.x with modern security and error handling
 *
 * <AUTHOR> Development Team
 * @version 3.0.0
 * @link https://lahza.io/
 * @link https://docs.lahza.io/
 * @link https://api-docs.lahza.io/
 * @copyright Copyright (c) 2024 WIDDX
 * @license MIT License
 *
 * Features:
 * - Complete API integration with proper error handling
 * - Card type detection and display
 * - Comprehensive transaction logging
 * - Webhook signature verification
 * - Multi-currency support (ILS, USD, JOD)
 * - RTL language support
 * - Mobile-optimized UI
 */

if (!defined("WHMCS")) {
    die("This file cannot be accessed directly");
}

/**
 * Define module related meta data.
 *
 * Enhanced metadata for better WHMCS integration
 *
 * @return array
 */
function lahza_MetaData()
{
    return array(
        'DisplayName' => 'Lahza.io Payment Gateway',
        'APIVersion' => '1.1',
        'DisableLocalCreditCardInput' => true,
        'TokenisedStorage' => false,
        'RequiresSSL' => true,
        'SupportsSubscriptions' => true,
        'SupportsRefunds' => true,
    );
}

/**
 * Define gateway configuration options.
 *
 * Complete configuration based on Lahza.io API documentation
 *
 * @return array
 */
function lahza_config()
{
    return [
        'FriendlyName' => [
            'Type' => 'System',
            'Value' => 'Lahza.io Payment Gateway',
        ],
        'publicKey' => [
            'FriendlyName' => 'Public Key',
            'Type' => 'text',
            'Size' => '70',
            'Default' => '',
            'Description' => 'Your Lahza.io Public Key (pk_test_... for test, pk_live_... for live)',
        ],
        'secretKey' => [
            'FriendlyName' => 'Secret Key',
            'Type' => 'password',
            'Size' => '70',
            'Default' => '',
            'Description' => 'Your Lahza.io Secret Key (sk_test_... for test, sk_live_... for live)',
        ],
        'testMode' => [
            'FriendlyName' => 'Test Mode',
            'Type' => 'yesno',
            'Description' => 'Enable test mode (uses test keys and sandbox environment)',
        ],
        'paymentMethod' => [
            'FriendlyName' => 'Payment Method',
            'Type' => 'dropdown',
            'Options' => [
                'popup' => 'Popup (Recommended - Better UX)',
                'redirect' => 'Redirect (Better Compatibility)',
            ],
            'Default' => 'popup',
            'Description' => 'Payment flow method',
        ],
        'allowedChannels' => [
            'FriendlyName' => 'Payment Channels',
            'Type' => 'text',
            'Size' => '100',
            'Default' => 'card,bank,mobile_money',
            'Description' => 'Allowed channels: card,bank,ussd,qr,mobile_money,bank_transfer (comma-separated)',
        ],
        'webhookUrl' => [
            'FriendlyName' => 'Webhook URL',
            'Type' => 'text',
            'Size' => '120',
            'Default' => '',
            'Description' => 'Custom webhook URL (auto-generated if empty)',
        ],
        'callbackUrl' => [
            'FriendlyName' => 'Callback URL',
            'Type' => 'text',
            'Size' => '120',
            'Default' => '',
            'Description' => 'Custom callback URL (auto-generated if empty)',
        ],
        'enableLogging' => [
            'FriendlyName' => 'Enable Logging',
            'Type' => 'yesno',
            'Description' => 'Enable detailed transaction logging',
        ],
        'showCardType' => [
            'FriendlyName' => 'Show Card Type',
            'Type' => 'yesno',
            'Description' => 'Display card type and bank information in transaction logs',
        ],
        'customCSS' => [
            'FriendlyName' => 'Custom CSS',
            'Type' => 'textarea',
            'Rows' => '5',
            'Description' => 'Custom CSS for payment form styling',
        ],
        'ipWhitelist' => [
            'FriendlyName' => 'IP Whitelist',
            'Type' => 'text',
            'Size' => '100',
            'Default' => '*************,**************',
            'Description' => 'Lahza.io webhook IP addresses (comma-separated)',
        ],
    ];
}

/**
 * Payment link generation with complete Lahza.io API integration
 *
 * @param array $params Payment Gateway Module Parameters
 * @return string HTML output for payment form
 */
function lahza_link($params)
{
    // Extract and validate configuration
    $config = lahza_extractConfig($params);
    if (isset($config['error'])) {
        return lahza_renderError($config['error']);
    }

    // Extract payment data
    $paymentData = lahza_extractPaymentData($params);

    // Generate transaction reference
    $transactionRef = lahza_generateReference($paymentData['invoiceId']);

    // Prepare metadata according to Lahza.io documentation
    $metadata = lahza_prepareMetadata($params, $paymentData);

    // Log payment attempt
    if ($config['enableLogging']) {
        lahza_logTransaction($params['paymentmethod'], [
            'action' => 'payment_initiated',
            'invoice_id' => $paymentData['invoiceId'],
            'amount' => $paymentData['amount'],
            'currency' => $paymentData['currency'],
            'method' => $config['paymentMethod'],
            'reference' => $transactionRef
        ], 'Payment Initiated');
    }

    // Handle payment method
    if ($config['paymentMethod'] === 'redirect') {
        return lahza_handleRedirectPayment($config, $paymentData, $transactionRef, $metadata);
    } else {
        return lahza_handlePopupPayment($config, $paymentData, $transactionRef, $metadata);
    }
}

/**
 * Extract and validate gateway configuration
 */
function lahza_extractConfig($params)
{
    $config = [
        'publicKey' => $params['publicKey'] ?? '',
        'secretKey' => $params['secretKey'] ?? '',
        'testMode' => $params['testMode'] ?? false,
        'paymentMethod' => $params['paymentMethod'] ?? 'popup',
        'allowedChannels' => $params['allowedChannels'] ?? 'card,bank,mobile_money',
        'webhookUrl' => $params['webhookUrl'] ?? '',
        'callbackUrl' => $params['callbackUrl'] ?? '',
        'enableLogging' => $params['enableLogging'] ?? false,
        'showCardType' => $params['showCardType'] ?? false,
        'customCSS' => $params['customCSS'] ?? '',
        'ipWhitelist' => $params['ipWhitelist'] ?? '*************,**************',
        'systemUrl' => $params['systemurl'],
        'returnUrl' => $params['returnurl'],
        'langPayNow' => $params['langpaynow'],
        'companyName' => $params['companyname'],
        'template' => $params['template'] ?? 'default'
    ];

    // Validate required fields
    if (empty($config['publicKey']) || empty($config['secretKey'])) {
        return ['error' => 'Lahza.io gateway not configured properly. Please contact administrator.'];
    }

    // Validate key format
    if (!preg_match('/^pk_(test_|live_)?[a-zA-Z0-9]+$/', $config['publicKey'])) {
        return ['error' => 'Invalid Public Key format.'];
    }

    if (!preg_match('/^sk_(test_|live_)?[a-zA-Z0-9]+$/', $config['secretKey'])) {
        return ['error' => 'Invalid Secret Key format.'];
    }

    // Auto-generate URLs if not provided
    if (empty($config['webhookUrl'])) {
        $config['webhookUrl'] = $config['systemUrl'] . 'modules/gateways/callback/lahza.php';
    }

    if (empty($config['callbackUrl'])) {
        $config['callbackUrl'] = $config['returnUrl'];
    }

    return $config;
}

/**
 * Extract payment data from params
 */
function lahza_extractPaymentData($params)
{
    $currency = $params['currency'];
    $amount = $params['amount'];

    // Validate currency
    $supportedCurrencies = ['ILS', 'USD', 'JOD'];
    if (!in_array($currency, $supportedCurrencies)) {
        throw new Exception('Currency ' . $currency . ' is not supported. Supported: ' . implode(', ', $supportedCurrencies));
    }

    // Convert to lowest currency unit (agora for ILS, qirsh for JOD, cents for USD)
    $amountInCents = (int)($amount * 100);

    return [
        'invoiceId' => $params['invoiceid'],
        'description' => $params['description'],
        'amount' => $amount,
        'amountInCents' => $amountInCents,
        'currency' => $currency,
        'firstname' => $params['clientdetails']['firstname'],
        'lastname' => $params['clientdetails']['lastname'],
        'email' => $params['clientdetails']['email'],
        'mobile' => $params['clientdetails']['phonenumber'],
        'address1' => $params['clientdetails']['address1'],
        'city' => $params['clientdetails']['city'],
        'country' => $params['clientdetails']['country'],
        'clientId' => $params['clientdetails']['userid']
    ];
}

    // Check if custom template exists
    $templatePath = ROOTDIR . '/templates/' . $params['template'] . '/payment/lahza/payment-form.tpl';

    if (file_exists($templatePath)) {
        // Use custom template
        $smarty = new Smarty();
        $smarty->setTemplateDir(ROOTDIR . '/templates/' . $params['template'] . '/');
        $smarty->setCompileDir(ROOTDIR . '/templates_c/');

        // Assign variables to template
        $smarty->assign('invoiceId', $invoiceId);
        $smarty->assign('description', $description);
        $smarty->assign('amount', $amount);
        $smarty->assign('currencyCode', $currencyCode);
        $smarty->assign('currency', $currencyCode);
        $smarty->assign('amountInCents', $amountInCents);
        $smarty->assign('firstname', $firstname);
        $smarty->assign('lastName', $lastname);
        $smarty->assign('firstName', $firstname);
        $smarty->assign('email', $email);
        $smarty->assign('phone', $phone);
        $smarty->assign('mobile', $phone);
        $smarty->assign('publicKey', $publicKey);
        $smarty->assign('transactionRef', $transactionRef);
        $smarty->assign('channelsJson', $channelsJson);
        $smarty->assign('metadataJson', json_encode($metadata));
        $smarty->assign('returnUrl', $returnUrl);
        $smarty->assign('langPayNow', $langPayNow);
        $smarty->assign('enableLogging', $enableLogging);
        $smarty->assign('customCSS', $customCSS);
        $smarty->assign('allowedChannels', $channels);
        $smarty->assign('clientName', $firstname . ' ' . $lastname);
        $smarty->assign('WEB_ROOT', $systemUrl);
        $smarty->assign('template', $params['template']);
        $smarty->assign('language', $params['language'] ?? 'english');

        try {
            $htmlOutput = $smarty->fetch('payment/lahza/payment-form.tpl');
        } catch (Exception $e) {
            // Fallback to inline HTML if template fails
            $htmlOutput = lahza_generateFallbackHTML($params, $invoiceId, $description, $amount, $currencyCode, $amountInCents, $firstname, $lastname, $email, $phone, $publicKey, $transactionRef, $channelsJson, $metadata, $returnUrl, $langPayNow, $enableLogging);
        }
    } else {
        // Fallback to inline HTML
        $htmlOutput = lahza_generateFallbackHTML($params, $invoiceId, $description, $amount, $currencyCode, $amountInCents, $firstname, $lastname, $email, $phone, $publicKey, $transactionRef, $channelsJson, $metadata, $returnUrl, $langPayNow, $enableLogging);
    }

    // Add enhanced JavaScript for better UX
    $htmlOutput .= '<script>
    document.addEventListener("DOMContentLoaded", function() {
        const payButton = document.getElementById("lahza-pay-btn");
        const errorElement = document.getElementById("lahza-payment-errors");
        const loadingElement = document.getElementById("lahza-payment-loading");
        const errorMessageSpan = errorElement.querySelector(".error-message");

        function showError(message) {
            errorMessageSpan.textContent = message;
            errorElement.style.display = "block";
            loadingElement.style.display = "none";
            payButton.disabled = false;
            payButton.innerHTML = \'<i class="fas fa-credit-card me-2"></i>' . $langPayNow . ' - ' . $amount . ' ' . $currencyCode . '\';
        }

        function showLoading() {
            errorElement.style.display = "none";
            loadingElement.style.display = "block";
            payButton.disabled = true;
            payButton.innerHTML = \'<i class="fas fa-spinner fa-spin me-2"></i>Processing...\';
        }

        payButton.addEventListener("click", function() {
            showLoading();

            try {
                const lahza = new LahzaPopup();
                lahza.newTransaction({
                    key: "' . $publicKey . '",
                    email: "' . $email . '",
                    mobile: "' . $phone . '",
                    firstName: "' . $firstname . '",
                    lastName: "' . $lastname . '",
                    amount: ' . $amountInCents . ',
                    currency: "' . $currencyCode . '",
                    ref: "' . $transactionRef . '",
                    channels: ' . $channelsJson . ',
                    metadata: ' . json_encode($metadata) . ',
                    onSuccess: function(transaction) {
                        // Log success if logging enabled
                        ' . ($enableLogging ? 'console.log("Lahza payment successful:", transaction);' : '') . '

                        // Redirect to return URL with transaction reference
                        window.location.href = "' . $returnUrl . '&reference=" + transaction.reference + "&status=success";
                    },
                    onCancel: function() {
                        showError("Payment was cancelled by user");
                        ' . ($enableLogging ? 'console.log("Lahza payment cancelled");' : '') . '
                    }
                });
            } catch (error) {
                console.error("Lahza payment initialization error:", error);
                showError("Error initializing payment: " + (error.message || "Unknown error"));
            }
        });
    });
    </script>';

    // Add custom CSS if provided
    if (!empty($customCSS)) {
        $htmlOutput .= '<style>' . $customCSS . '</style>';
    }

    return $htmlOutput;
}

/**
 * Generate unique transaction reference
 */
function lahza_generateReference($invoiceId)
{
    return 'WHMCS_' . $invoiceId . '_' . time() . '_' . substr(md5(uniqid()), 0, 8);
}

/**
 * Prepare metadata according to Lahza.io documentation
 */
function lahza_prepareMetadata($params, $paymentData)
{
    $metadata = [
        'invoice_id' => $paymentData['invoiceId'],
        'client_id' => $paymentData['clientId'],
        'company_name' => $params['companyname'],
        'whmcs_url' => $params['systemurl'],
        'payment_method' => $params['paymentmethod'],
        'client_name' => $paymentData['firstname'] . ' ' . $paymentData['lastname'],
        'custom_fields' => [
            [
                'display_name' => 'Invoice ID',
                'variable_name' => 'invoice_id',
                'value' => $paymentData['invoiceId']
            ],
            [
                'display_name' => 'Client Name',
                'variable_name' => 'client_name',
                'value' => $paymentData['firstname'] . ' ' . $paymentData['lastname']
            ],
            [
                'display_name' => 'Description',
                'variable_name' => 'description',
                'value' => $paymentData['description']
            ]
        ]
    ];

    return $metadata;
}

/**
 * Log transaction activity
 */
function lahza_logTransaction($gatewayName, $data, $result)
{
    if (function_exists('logTransaction')) {
        logTransaction($gatewayName, $data, $result);
    }
}

/**
 * Render error message
 */
function lahza_renderError($message)
{
    return '<div class="alert alert-danger"><i class="fas fa-exclamation-triangle"></i> ' . htmlspecialchars($message) . '</div>';
}

/**
 * Handle redirect payment method
 */
function lahza_handleRedirectPayment($config, $paymentData, $transactionRef, $metadata)
{
    // Initialize transaction via Lahza.io API
    $initData = [
        'email' => $paymentData['email'],
        'mobile' => $paymentData['mobile'],
        'first_name' => $paymentData['firstname'],
        'last_name' => $paymentData['lastname'],
        'amount' => $paymentData['amountInCents'],
        'currency' => $paymentData['currency'],
        'reference' => $transactionRef,
        'callback_url' => $config['callbackUrl'],
        'metadata' => $metadata,
    ];

    // Add channels if specified
    if (!empty($config['allowedChannels'])) {
        $channels = array_map('trim', explode(',', $config['allowedChannels']));
        $initData['channels'] = $channels;
    }

    $response = lahza_makeApiCall('transaction/initialize', $initData, $config['secretKey']);

    if ($response && $response['status'] === true && isset($response['data']['authorization_url'])) {
        $authUrl = $response['data']['authorization_url'];

        return lahza_renderRedirectForm($config, $paymentData, $authUrl);
    } else {
        $error = isset($response['message']) ? $response['message'] : 'Failed to initialize payment';
        return lahza_renderError($error);
    }
}

/**
 * Handle popup payment method
 */
function lahza_handlePopupPayment($config, $paymentData, $transactionRef, $metadata)
{
    return lahza_renderPopupForm($config, $paymentData, $transactionRef, $metadata);
}

/**
 * Make API call to Lahza.io
 */
function lahza_makeApiCall($endpoint, $data, $secretKey)
{
    $url = 'https://api.lahza.io/' . $endpoint;

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Authorization: Bearer ' . $secretKey,
        'Content-Type: application/x-www-form-urlencoded',
        'Cache-Control: no-cache',
    ]);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true);

    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);

    if ($error) {
        lahza_logTransaction('lahza', ['curl_error' => $error, 'endpoint' => $endpoint], 'API Error');
        return false;
    }

    if ($httpCode !== 200) {
        lahza_logTransaction('lahza', ['http_code' => $httpCode, 'response' => $response], 'HTTP Error');
        return false;
    }

    return json_decode($response, true);
}



/**
 * Render redirect payment form
 */
function lahza_renderRedirectForm($config, $paymentData, $authUrl)
{
    $html = '
    <div class="lahza-payment-container card border-0 shadow-lg">
        <div class="card-body p-0">
            <div class="payment-header text-center">
                <div class="payment-logo mb-3">
                    <i class="fas fa-credit-card text-white" style="font-size: 3rem;"></i>
                </div>
                <h4 class="text-white mb-2">
                    <i class="fas fa-lock me-2"></i>Secure Payment
                </h4>
                <p class="text-white-50 mb-0">Powered by Lahza.io</p>
            </div>

            <div class="payment-details p-4">
                <div class="row mb-3">
                    <div class="col-md-6">
                        <small class="text-muted">Invoice</small>
                        <div class="fw-bold">#' . $paymentData['invoiceId'] . '</div>
                    </div>
                    <div class="col-md-6 text-md-end">
                        <small class="text-muted">Amount</small>
                        <div class="fw-bold text-primary">' . $paymentData['amount'] . ' ' . $paymentData['currency'] . '</div>
                    </div>
                </div>

                <div class="row mb-4">
                    <div class="col-12">
                        <small class="text-muted">Description</small>
                        <div class="fw-bold">' . htmlspecialchars($paymentData['description']) . '</div>
                    </div>
                </div>

                <a href="' . $authUrl . '" class="btn btn-primary btn-lg w-100 mb-3" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border: none;">
                    <i class="fas fa-external-link-alt me-2"></i>
                    ' . $config['langPayNow'] . ' - ' . $paymentData['amount'] . ' ' . $paymentData['currency'] . '
                </a>

                <div class="payment-security text-center">
                    <small class="text-muted">
                        <i class="fas fa-shield-alt text-success"></i>
                        Secure payment powered by Lahza.io
                    </small>
                </div>
            </div>
        </div>
    </div>';

    // Add custom CSS
    if (!empty($config['customCSS'])) {
        $html .= '<style>' . $config['customCSS'] . '</style>';
    }

    return $html;
}

/**
 * Render popup payment form
 */
function lahza_renderPopupForm($config, $paymentData, $transactionRef, $metadata)
{
    $channels = array_map('trim', explode(',', $config['allowedChannels']));
    $channelsJson = json_encode($channels);
    $metadataJson = json_encode($metadata);

    $html = '
    <div class="lahza-payment-container card border-0 shadow-lg">
        <div class="card-body p-0">
            <div class="payment-header text-center">
                <div class="payment-logo mb-3">
                    <i class="fas fa-credit-card text-white" style="font-size: 3rem;"></i>
                </div>
                <h4 class="text-white mb-2">
                    <i class="fas fa-lock me-2"></i>Secure Payment
                </h4>
                <p class="text-white-50 mb-0">Powered by Lahza.io</p>
            </div>

            <div class="payment-details p-4">
                <div class="row mb-3">
                    <div class="col-md-6">
                        <small class="text-muted">Invoice</small>
                        <div class="fw-bold">#' . $paymentData['invoiceId'] . '</div>
                    </div>
                    <div class="col-md-6 text-md-end">
                        <small class="text-muted">Amount</small>
                        <div class="fw-bold text-primary">' . $paymentData['amount'] . ' ' . $paymentData['currency'] . '</div>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-12">
                        <small class="text-muted">Description</small>
                        <div class="fw-bold">' . htmlspecialchars($paymentData['description']) . '</div>
                    </div>
                </div>

                <div class="row mb-4">
                    <div class="col-12">
                        <small class="text-muted">Customer</small>
                        <div class="fw-bold">' . htmlspecialchars($paymentData['firstname'] . ' ' . $paymentData['lastname']) . '</div>
                    </div>
                </div>

                <button type="button" id="lahza-pay-btn" class="btn btn-primary btn-lg w-100 mb-3"
                        data-public-key="' . $config['publicKey'] . '"
                        data-email="' . $paymentData['email'] . '"
                        data-mobile="' . $paymentData['mobile'] . '"
                        data-first-name="' . $paymentData['firstname'] . '"
                        data-last-name="' . $paymentData['lastname'] . '"
                        data-amount="' . $paymentData['amountInCents'] . '"
                        data-currency="' . $paymentData['currency'] . '"
                        data-reference="' . $transactionRef . '"
                        data-channels=\'' . $channelsJson . '\'
                        data-metadata=\'' . $metadataJson . '\'
                        style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border: none;">
                    <i class="fas fa-credit-card me-2"></i>
                    ' . $config['langPayNow'] . ' - ' . $paymentData['amount'] . ' ' . $paymentData['currency'] . '
                </button>

                <div class="payment-security text-center">
                    <small class="text-muted">
                        <i class="fas fa-shield-alt text-success"></i>
                        Your payment is secured with 256-bit SSL encryption
                    </small>
                </div>

                <div id="lahza-payment-errors" class="alert alert-danger mt-3" style="display: none;">
                    <i class="fas fa-exclamation-triangle"></i>
                    <span class="error-message"></span>
                </div>

                <div id="lahza-payment-loading" class="text-center mt-3" style="display: none;">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <div class="mt-2">
                        <small class="text-muted">Processing payment...</small>
                    </div>
                </div>
            </div>
        </div>
    </div>';

    // Add Lahza.io JavaScript library
    $html .= '<script src="https://js.lahza.io/inline.min.js"></script>';

    // Add enhanced JavaScript
    $html .= '
    <script>
    document.addEventListener("DOMContentLoaded", function() {
        const payButton = document.getElementById("lahza-pay-btn");
        const errorElement = document.getElementById("lahza-payment-errors");
        const loadingElement = document.getElementById("lahza-payment-loading");
        const errorMessageSpan = errorElement.querySelector(".error-message");

        function showError(message) {
            errorMessageSpan.textContent = message;
            errorElement.style.display = "block";
            loadingElement.style.display = "none";
            payButton.disabled = false;
            payButton.innerHTML = \'<i class="fas fa-credit-card me-2"></i>' . $config['langPayNow'] . ' - ' . $paymentData['amount'] . ' ' . $paymentData['currency'] . '\';
        }

        function showLoading() {
            errorElement.style.display = "none";
            loadingElement.style.display = "block";
            payButton.disabled = true;
            payButton.innerHTML = \'<i class="fas fa-spinner fa-spin me-2"></i>Processing...\';
        }

        payButton.addEventListener("click", function() {
            showLoading();

            try {
                const lahza = new LahzaPopup();
                lahza.newTransaction({
                    key: "' . $config['publicKey'] . '",
                    email: "' . $paymentData['email'] . '",
                    mobile: "' . $paymentData['mobile'] . '",
                    firstName: "' . $paymentData['firstname'] . '",
                    lastName: "' . $paymentData['lastname'] . '",
                    amount: ' . $paymentData['amountInCents'] . ',
                    currency: "' . $paymentData['currency'] . '",
                    ref: "' . $transactionRef . '",
                    channels: ' . $channelsJson . ',
                    metadata: ' . $metadataJson . ',
                    onSuccess: function(transaction) {
                        ' . ($config['enableLogging'] ? 'console.log("Lahza payment successful:", transaction);' : '') . '
                        window.location.href = "' . $config['returnUrl'] . '&reference=" + transaction.reference + "&status=success";
                    },
                    onCancel: function() {
                        showError("Payment was cancelled by user");
                        ' . ($config['enableLogging'] ? 'console.log("Lahza payment cancelled");' : '') . '
                    }
                });
            } catch (error) {
                console.error("Lahza payment initialization error:", error);
                showError("Error initializing payment: " + (error.message || "Unknown error"));
            }
        });
    });
    </script>';

    // Add default CSS and custom CSS
    $html .= lahza_getDefaultCSS();

    if (!empty($config['customCSS'])) {
        $html .= '<style>' . $config['customCSS'] . '</style>';
    }

    return $html;
}

/**
 * Get default CSS for Lahza payment forms
 */
function lahza_getDefaultCSS()
{
    return '
    <style>
    .lahza-payment-container {
        max-width: 500px;
        margin: 20px auto;
        border-radius: 15px;
        overflow: hidden;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        background: #fff;
    }

    .payment-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        padding: 30px 20px;
        position: relative;
    }

    .payment-header::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url("data:image/svg+xml,%3Csvg width=\'60\' height=\'60\' viewBox=\'0 0 60 60\' xmlns=\'http://www.w3.org/2000/svg\'%3E%3Cg fill=\'none\' fill-rule=\'evenodd\'%3E%3Cg fill=\'%23ffffff\' fill-opacity=\'0.1\'%3E%3Ccircle cx=\'30\' cy=\'30\' r=\'2\'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        opacity: 0.3;
    }

    .payment-logo {
        position: relative;
        z-index: 1;
    }

    .payment-details {
        background: #fff;
    }

    .lahza-payment-container .btn-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        border-radius: 10px;
        padding: 15px 30px;
        font-weight: 600;
        font-size: 16px;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .lahza-payment-container .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
    }

    .lahza-payment-container .btn-primary:active {
        transform: translateY(0);
    }

    .lahza-payment-container .btn-primary:disabled {
        opacity: 0.7;
        transform: none;
        box-shadow: none;
    }

    .payment-security {
        padding: 15px 0;
        border-top: 1px solid #f0f0f0;
        margin-top: 20px;
    }

    .alert-danger {
        border-radius: 10px;
        border: none;
        background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
        color: white;
        border-left: 4px solid #ff5252;
    }

    .spinner-border {
        width: 2rem;
        height: 2rem;
    }

    /* RTL Support */
    [dir="rtl"] .lahza-payment-container {
        text-align: right;
    }

    [dir="rtl"] .payment-header h4 {
        direction: rtl;
    }

    /* Mobile Responsive */
    @media (max-width: 768px) {
        .lahza-payment-container {
            margin: 10px;
            max-width: none;
        }

        .payment-header {
            padding: 20px 15px;
        }

        .payment-details {
            padding: 20px 15px;
        }

        .lahza-payment-container .btn-primary {
            padding: 12px 20px;
            font-size: 14px;
        }
    }

    /* Dark mode support */
    @media (prefers-color-scheme: dark) {
        .lahza-payment-container {
            background: #2d3748;
            color: #e2e8f0;
        }

        .payment-details {
            background: #2d3748;
        }

        .text-muted {
            color: #a0aec0 !important;
        }

        .payment-security {
            border-top-color: #4a5568;
        }
    }

    /* Animation for loading state */
    @keyframes pulse {
        0% { opacity: 1; }
        50% { opacity: 0.5; }
        100% { opacity: 1; }
    }

    .lahza-payment-container .btn-primary:disabled {
        animation: pulse 1.5s infinite;
    }
    </style>';
}
