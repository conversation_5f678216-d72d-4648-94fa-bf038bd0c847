/**
 * Homepage Sections CSS
 * Transparent backgrounds for all sections
 * Company Brand Colors: Purple (#4a338d) and Magenta (#cc00bb)
 */

/* Import company brand colors and variables */
@import url('variables.css');

/* ===================================
   GLOBAL SECTION STYLES
   =================================== */

/* All sections have transparent background */
.transparent-section {
    background: transparent !important;
    position: relative;
    padding: 80px 0;
}

/* Remove any background overrides for dark theme */
[data-bs-theme="dark"] .transparent-section {
    background: transparent !important;
}

/* ===================================
   PRICING SECTION
   =================================== */

.pricing-section {
    background: transparent;
    padding: 80px 0;
}

.pricing-feature-list {
    padding: 0;
    margin: 0;
    list-style: none;
}

.pricing-feature-list li {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    padding: 0.5rem 0;
    border-bottom: 1px solid var(--border-color);
    transition: all 0.3s ease;
}

.pricing-feature-list li:hover {
    background: var(--bg-secondary);
    padding-left: 0.5rem;
    border-radius: 8px;
    border-bottom: 1px solid transparent;
}

.pricing-feature-list i {
    font-size: 1.2rem;
    margin-right: 0.75rem;
    color: var(--primary-color);
    width: 20px;
    text-align: center;
    flex-shrink: 0;
}

.feature-label {
    font-weight: 600;
    margin-right: 0.5rem;
    color: var(--text-primary);
}

.pricing-feature-list span:last-child {
    color: var(--text-secondary);
    margin-left: auto;
}

/* ===================================
   HOSTING FEATURES SECTION
   =================================== */

.hosting-features {
    background: transparent;
    padding: 80px 0;
}

.hosting-features .section-title {
    color: var(--text-primary);
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 2rem;
}

.hosting-features .features-content {
    padding: 2rem 0;
}

.hosting-features .hosting-item {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: 16px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
    box-shadow: var(--shadow-sm);
}

.hosting-features .hosting-item:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-color);
}

/* ===================================
   PRODUCT GROUPS SECTION
   =================================== */

.productgroups-section {
    background: transparent;
    padding: 80px 0;
}

/* ===================================
   ALL-IN-ONE SECTION
   =================================== */

.all-in-one-section {
    background: transparent;
    padding: 80px 0;
}

.all-in-one-section h1,
.all-in-one-section h2,
.all-in-one-section h3 {
    color: var(--text-primary);
}

.all-in-one-section p {
    color: var(--text-secondary);
    line-height: 1.6;
}

.all-in-one-section ul li {
    color: var(--text-secondary);
    padding: 0.5rem 0;
    border-bottom: 1px solid var(--border-color);
}

.all-in-one-section ul li:last-child {
    border-bottom: none;
}

.all-in-one-section ul li i {
    color: var(--primary-color);
    margin-right: 0.75rem;
    width: 20px;
    text-align: center;
}

/* ===================================
   MAXIMIZE WEBSITE SPEED SECTION
   =================================== */

.maximize-website-speed-section {
    background: transparent;
    padding: 80px 0;
}

.maximize-website-speed-section h1 {
    color: var(--text-primary);
    font-size: 2.5rem;
    font-weight: 700;
}

.maximize-website-speed-section ul li {
    color: var(--text-secondary);
    padding: 0.5rem 0;
    border-bottom: 1px solid var(--border-color);
}

.maximize-website-speed-section ul li:last-child {
    border-bottom: none;
}

.maximize-website-speed-section ul li i {
    color: var(--primary-color);
    margin-right: 0.75rem;
    width: 20px;
    text-align: center;
}

/* ===================================
   NETWORK MAP SECTION
   =================================== */

.network-map-section {
    background: transparent;
    padding: 80px 0;
}

/* ===================================
   WHY WIDDX HOSTING SECTION
   =================================== */

.why-widdx-hosting-section {
    background: transparent;
    padding: 80px 0;
}

/* ===================================
   FAQ SECTION
   =================================== */

.faq-section {
    background: transparent !important;
    padding: 80px 0;
}

#faq {
    background: transparent !important;
}

.accordion-item {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: 12px !important;
    margin-bottom: 1rem;
    box-shadow: var(--shadow-sm);
}

.accordion-button {
    background: var(--bg-card) !important;
    color: var(--text-primary) !important;
    border: none !important;
    border-radius: 12px !important;
    font-weight: 600;
}

.accordion-button:not(.collapsed) {
    background: var(--bg-card) !important;
    color: var(--primary-color) !important;
    box-shadow: none !important;
}

.accordion-body {
    background: var(--bg-card);
    color: var(--text-secondary);
    border-top: 1px solid var(--border-color);
}

/* ===================================
   ANNOUNCEMENTS SECTION
   =================================== */

.announcements-section {
    background: transparent;
    padding: 80px 0;
}

.announcements-carousel .card {
    margin: 10px;
    height: 100%;
    box-shadow: var(--shadow-sm);
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: 16px;
    transition: all 0.3s ease;
}

.announcements-carousel .card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.announcements-carousel .card-img-wrapper {
    position: relative;
    padding-top: 70%; /* 16:9 Aspect Ratio */
    overflow: hidden;
    border-radius: 16px 16px 0 0;
}

.announcements-carousel .card-img-wrapper img {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.announcements-carousel .card:hover .card-img-wrapper img {
    transform: scale(1.05);
}

.announcements-carousel .card-body {
    padding: 1.5rem;
    display: flex;
    flex-direction: column;
    height: calc(100% - 200px);
}

.announcements-carousel .card-title {
    font-size: 1.1rem;
    margin-bottom: 0.75rem;
    font-weight: 600;
}

.announcements-carousel .card-title a {
    color: var(--text-primary);
    text-decoration: none;
    transition: color 0.3s ease;
}

.announcements-carousel .card-title a:hover {
    color: var(--primary-color);
}

.announcements-carousel .card-subtitle {
    color: var(--text-secondary);
    font-size: 0.9rem;
    margin-bottom: 1rem;
}

.announcements-carousel .card-text {
    font-size: 0.9rem;
    color: var(--text-secondary);
    line-height: 1.6;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    flex-grow: 1;
}

.announcements-carousel .btn {
    margin-top: auto;
    align-self: flex-start;
}

@media (max-width: 767px) {
    .announcements-carousel .card {
        margin: 5px;
    }
    .announcements-carousel .card-body {
        padding: 1rem;
    }
    .announcements-carousel .card-title {
        font-size: 1rem;
    }
    .announcements-carousel .card-text {
        font-size: 0.85rem;
    }
}

/* ===================================
   CARDS AND COMPONENTS
   =================================== */

.card {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: 16px;
    box-shadow: var(--shadow-sm);
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.card-header {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border: none;
    border-radius: 16px 16px 0 0 !important;
}

.card-body {
    padding: 2rem;
}

/* ===================================
   BUTTONS
   =================================== */

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    border: none;
    border-radius: 12px;
    padding: 0.75rem 2rem;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(74, 51, 141, 0.3);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(74, 51, 141, 0.4);
    background: linear-gradient(135deg, var(--primary-dark), var(--primary-color));
}

/* ===================================
   RESPONSIVE DESIGN
   =================================== */

@media (max-width: 992px) {
    .transparent-section {
        padding: 60px 0;
    }
    
    .hosting-features .section-title,
    .maximize-website-speed-section h1 {
        font-size: 2rem;
    }
}

@media (max-width: 768px) {
    .transparent-section {
        padding: 40px 0;
    }
    
    .hosting-features .section-title,
    .maximize-website-speed-section h1 {
        font-size: 1.75rem;
    }
    
    .card-body {
        padding: 1.5rem;
    }
    
    .pricing-feature-list li {
        flex-direction: column;
        align-items: flex-start;
        text-align: left;
    }
    
    .pricing-feature-list span:last-child {
        margin-left: 0;
        margin-top: 0.25rem;
    }
}

/* ===================================
   DARK THEME ADJUSTMENTS
   =================================== */

[data-bs-theme="dark"] .card {
    background: var(--gray-800);
    border-color: var(--gray-700);
}

[data-bs-theme="dark"] .accordion-item {
    background: var(--gray-800);
    border-color: var(--gray-700);
}

[data-bs-theme="dark"] .accordion-button {
    background: var(--gray-800) !important;
    color: var(--text-primary) !important;
}

[data-bs-theme="dark"] .accordion-body {
    background: var(--gray-800);
    border-color: var(--gray-700);
}
