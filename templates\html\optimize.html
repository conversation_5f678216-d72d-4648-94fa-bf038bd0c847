<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WIDDX Performance Optimizer</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        h1 {
            color: #6c5bb9;
            text-align: center;
            margin-bottom: 30px;
        }
        .optimization-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background: #fafafa;
        }
        .optimization-section h3 {
            color: #333;
            margin-bottom: 15px;
        }
        .btn {
            background: linear-gradient(135deg, #6c5bb9, #c0a5d5);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 600;
            margin: 5px;
            transition: transform 0.2s;
        }
        .btn:hover {
            transform: translateY(-2px);
        }
        .status {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
            font-weight: 500;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .metrics {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .metric-card {
            background: white;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #6c5bb9;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .metric-value {
            font-size: 24px;
            font-weight: bold;
            color: #6c5bb9;
        }
        .metric-label {
            font-size: 14px;
            color: #666;
            margin-top: 5px;
        }
        .progress-bar {
            width: 100%;
            height: 20px;
            background: #e0e0e0;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-fill {
            height: 100%;
            background: linear-gradient(135deg, #6c5bb9, #c0a5d5);
            transition: width 0.3s ease;
        }
        .recommendations {
            background: #e8f4fd;
            border: 1px solid #bee5eb;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .recommendations h4 {
            color: #0c5460;
            margin-bottom: 15px;
        }
        .recommendations ul {
            color: #0c5460;
            padding-left: 20px;
        }
        .recommendations li {
            margin: 8px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 WIDDX Performance Optimizer</h1>
        
        <div class="optimization-section">
            <h3>Performance Analysis</h3>
            <button class="btn" onclick="runPerformanceAnalysis()">Analyze Current Performance</button>
            <div id="performance-results"></div>
        </div>

        <div class="optimization-section">
            <h3>Resource Optimization</h3>
            <button class="btn" onclick="optimizeImages()">Optimize Images</button>
            <button class="btn" onclick="minifyCSS()">Minify CSS</button>
            <button class="btn" onclick="minifyJS()">Minify JavaScript</button>
            <div id="optimization-results"></div>
        </div>

        <div class="optimization-section">
            <h3>Caching & Service Worker</h3>
            <button class="btn" onclick="updateServiceWorker()">Update Service Worker</button>
            <button class="btn" onclick="clearCache()">Clear Cache</button>
            <div id="cache-results"></div>
        </div>

        <div class="optimization-section">
            <h3>Performance Metrics</h3>
            <div class="metrics" id="metrics-container">
                <!-- Metrics will be populated here -->
            </div>
        </div>

        <div class="recommendations">
            <h4>🎯 Performance Recommendations</h4>
            <ul id="recommendations-list">
                <li>Enable GZIP compression on your server</li>
                <li>Use a Content Delivery Network (CDN) for static assets</li>
                <li>Implement HTTP/2 for better resource loading</li>
                <li>Consider using WebP format for images</li>
                <li>Enable browser caching with proper cache headers</li>
                <li>Minimize render-blocking resources</li>
                <li>Use resource hints (preload, prefetch, preconnect)</li>
            </ul>
        </div>
    </div>

    <script>
        // Performance Analysis
        function runPerformanceAnalysis() {
            const resultsDiv = document.getElementById('performance-results');
            resultsDiv.innerHTML = '<div class="status warning">Analyzing performance...</div>';

            // Simulate performance analysis
            setTimeout(() => {
                const metrics = {
                    'First Contentful Paint': Math.random() * 1000 + 500,
                    'Largest Contentful Paint': Math.random() * 2000 + 1000,
                    'Time to Interactive': Math.random() * 3000 + 1500,
                    'Total Blocking Time': Math.random() * 200 + 50,
                    'Cumulative Layout Shift': Math.random() * 0.1
                };

                let html = '<div class="status success">Performance analysis complete!</div>';
                html += '<div class="metrics">';
                
                Object.entries(metrics).forEach(([key, value]) => {
                    const unit = key === 'Cumulative Layout Shift' ? '' : 'ms';
                    const formattedValue = key === 'Cumulative Layout Shift' ? 
                        value.toFixed(3) : Math.round(value);
                    
                    html += `
                        <div class="metric-card">
                            <div class="metric-value">${formattedValue}${unit}</div>
                            <div class="metric-label">${key}</div>
                        </div>
                    `;
                });
                
                html += '</div>';
                resultsDiv.innerHTML = html;
            }, 2000);
        }

        // Resource Optimization
        function optimizeImages() {
            showOptimizationProgress('optimization-results', 'Optimizing images...');
        }

        function minifyCSS() {
            showOptimizationProgress('optimization-results', 'Minifying CSS files...');
        }

        function minifyJS() {
            showOptimizationProgress('optimization-results', 'Minifying JavaScript files...');
        }

        function showOptimizationProgress(containerId, message) {
            const container = document.getElementById(containerId);
            container.innerHTML = `
                <div class="status warning">${message}</div>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: 0%"></div>
                </div>
            `;

            let progress = 0;
            const interval = setInterval(() => {
                progress += Math.random() * 20;
                if (progress >= 100) {
                    progress = 100;
                    clearInterval(interval);
                    container.innerHTML = '<div class="status success">Optimization complete!</div>';
                }
                container.querySelector('.progress-fill').style.width = progress + '%';
            }, 200);
        }

        // Service Worker Management
        function updateServiceWorker() {
            const resultsDiv = document.getElementById('cache-results');
            resultsDiv.innerHTML = '<div class="status warning">Updating service worker...</div>';

            if ('serviceWorker' in navigator) {
                navigator.serviceWorker.getRegistration().then(registration => {
                    if (registration) {
                        registration.update().then(() => {
                            resultsDiv.innerHTML = '<div class="status success">Service worker updated successfully!</div>';
                        });
                    } else {
                        resultsDiv.innerHTML = '<div class="status error">No service worker found to update.</div>';
                    }
                });
            } else {
                resultsDiv.innerHTML = '<div class="status error">Service workers not supported in this browser.</div>';
            }
        }

        function clearCache() {
            const resultsDiv = document.getElementById('cache-results');
            resultsDiv.innerHTML = '<div class="status warning">Clearing cache...</div>';

            if ('caches' in window) {
                caches.keys().then(cacheNames => {
                    return Promise.all(
                        cacheNames.map(cacheName => caches.delete(cacheName))
                    );
                }).then(() => {
                    resultsDiv.innerHTML = '<div class="status success">Cache cleared successfully!</div>';
                }).catch(() => {
                    resultsDiv.innerHTML = '<div class="status error">Failed to clear cache.</div>';
                });
            } else {
                resultsDiv.innerHTML = '<div class="status error">Cache API not supported in this browser.</div>';
            }
        }

        // Initialize metrics display
        function initializeMetrics() {
            const metricsContainer = document.getElementById('metrics-container');
            const sampleMetrics = [
                { label: 'Page Load Time', value: '1.2s', status: 'good' },
                { label: 'First Paint', value: '0.8s', status: 'good' },
                { label: 'Time to Interactive', value: '2.1s', status: 'needs-improvement' },
                { label: 'Total Page Size', value: '1.2MB', status: 'good' }
            ];

            metricsContainer.innerHTML = sampleMetrics.map(metric => `
                <div class="metric-card">
                    <div class="metric-value">${metric.value}</div>
                    <div class="metric-label">${metric.label}</div>
                </div>
            `).join('');
        }

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', initializeMetrics);

        // Auto-refresh metrics every 30 seconds
        setInterval(() => {
            if (window.performanceMonitor) {
                const metrics = window.performanceMonitor.getMetrics();
                console.log('Current performance metrics:', metrics);
            }
        }, 30000);
    </script>

    <!-- Background System -->
    <script src="background/background.js"></script>
</body>
</html>
