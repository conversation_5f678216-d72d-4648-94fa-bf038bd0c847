<?php
/**
 * Lahza.io Payment Gateway Debug Tool
 * 
 * This tool helps diagnose callback issues and test payment processing
 * Use this to troubleshoot why payments are not being marked as paid
 * 
 * <AUTHOR> Development Team
 * @version 2.0.0
 * 
 * IMPORTANT: Remove this file from production environment
 */

// Security check
if (!isset($_GET['debug_key']) || $_GET['debug_key'] !== 'lahza_debug_2024') {
    die('Access denied. Add ?debug_key=lahza_debug_2024 to URL');
}

// Require libraries needed for gateway module functions.
require_once __DIR__ . '/../../../init.php';
require_once __DIR__ . '/../../../includes/gatewayfunctions.php';
require_once __DIR__ . '/../../../includes/invoicefunctions.php';

// Detect module name from filename.
$gatewayModuleName = 'lahza';

// Fetch gateway configuration parameters.
$gatewayParams = getGatewayVariables($gatewayModuleName);

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Lahza.io Payment Debug Tool</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body { background: #f8f9fa; }
        .debug-container { max-width: 1200px; margin: 2rem auto; }
        .status-success { color: #28a745; }
        .status-error { color: #dc3545; }
        .status-warning { color: #ffc107; }
        .code-block { background: #f8f9fa; padding: 1rem; border-radius: 5px; font-family: monospace; }
    </style>
</head>
<body>
    <div class="container debug-container">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h2><i class="fas fa-bug"></i> Lahza.io Payment Debug Tool</h2>
            </div>
            <div class="card-body">
                
                <!-- Gateway Status -->
                <div class="row mb-4">
                    <div class="col-12">
                        <h4>Gateway Status</h4>
                        <?php if ($gatewayParams['type']): ?>
                            <div class="alert alert-success">
                                <strong>✅ Gateway is Active</strong><br>
                                Module Name: <?php echo $gatewayModuleName; ?><br>
                                Display Name: <?php echo $gatewayParams['name']; ?>
                            </div>
                        <?php else: ?>
                            <div class="alert alert-danger">
                                <strong>❌ Gateway is NOT Active</strong><br>
                                Please activate the Lahza.io gateway in WHMCS Admin.
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Configuration Check -->
                <div class="row mb-4">
                    <div class="col-12">
                        <h4>Configuration Check</h4>
                        <table class="table table-bordered">
                            <tr>
                                <td><strong>Public Key</strong></td>
                                <td>
                                    <?php if (!empty($gatewayParams['publicKey'])): ?>
                                        <span class="status-success">✅ Set</span> 
                                        (<?php echo substr($gatewayParams['publicKey'], 0, 10); ?>...)
                                    <?php else: ?>
                                        <span class="status-error">❌ Not Set</span>
                                    <?php endif; ?>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Secret Key</strong></td>
                                <td>
                                    <?php if (!empty($gatewayParams['secretKey'])): ?>
                                        <span class="status-success">✅ Set</span>
                                        (<?php echo substr($gatewayParams['secretKey'], 0, 10); ?>...)
                                    <?php else: ?>
                                        <span class="status-error">❌ Not Set</span>
                                    <?php endif; ?>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Test Mode</strong></td>
                                <td>
                                    <?php echo $gatewayParams['testMode'] ? '<span class="status-warning">⚠️ Enabled</span>' : '<span class="status-success">✅ Disabled (Live)</span>'; ?>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Enable Logging</strong></td>
                                <td>
                                    <?php echo $gatewayParams['enableLogging'] ? '<span class="status-success">✅ Enabled</span>' : '<span class="status-warning">⚠️ Disabled</span>'; ?>
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>

                <!-- Test Payment Verification -->
                <?php if (isset($_POST['test_reference'])): ?>
                <div class="row mb-4">
                    <div class="col-12">
                        <h4>Payment Verification Test</h4>
                        <?php
                        $testReference = $_POST['test_reference'];
                        
                        // Test API call
                        $url = 'https://api.lahza.io/transaction/verify/' . $testReference;
                        
                        $ch = curl_init();
                        curl_setopt($ch, CURLOPT_URL, $url);
                        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                        curl_setopt($ch, CURLOPT_HTTPHEADER, array(
                            'Authorization: Bearer ' . $gatewayParams['secretKey'],
                            'Content-Type: application/json',
                            'Cache-Control: no-cache',
                        ));
                        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
                        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true);
                        
                        $response = curl_exec($ch);
                        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                        $error = curl_error($ch);
                        curl_close($ch);
                        
                        if ($error) {
                            echo '<div class="alert alert-danger"><strong>cURL Error:</strong> ' . $error . '</div>';
                        } else {
                            echo '<div class="alert alert-info">';
                            echo '<strong>HTTP Code:</strong> ' . $httpCode . '<br>';
                            echo '<strong>Response:</strong><br>';
                            echo '<div class="code-block">' . htmlspecialchars($response) . '</div>';
                            echo '</div>';
                            
                            if ($httpCode === 200) {
                                $result = json_decode($response, true);
                                if ($result && isset($result['status']) && $result['status'] === true) {
                                    echo '<div class="alert alert-success"><strong>✅ Payment verification successful!</strong></div>';
                                    
                                    $paymentData = $result['data'];
                                    echo '<table class="table table-bordered">';
                                    echo '<tr><td><strong>Reference</strong></td><td>' . ($paymentData['reference'] ?? 'N/A') . '</td></tr>';
                                    echo '<tr><td><strong>Status</strong></td><td>' . ($paymentData['status'] ?? 'N/A') . '</td></tr>';
                                    echo '<tr><td><strong>Amount</strong></td><td>' . (($paymentData['amount'] ?? 0) / 100) . '</td></tr>';
                                    echo '<tr><td><strong>Currency</strong></td><td>' . ($paymentData['currency'] ?? 'N/A') . '</td></tr>';
                                    echo '<tr><td><strong>Fees</strong></td><td>' . (($paymentData['fees'] ?? 0) / 100) . '</td></tr>';
                                    echo '</table>';
                                    
                                    // Check if metadata contains invoice ID
                                    if (isset($paymentData['metadata']['invoice_id'])) {
                                        $invoiceId = $paymentData['metadata']['invoice_id'];
                                        echo '<div class="alert alert-info"><strong>Invoice ID found in metadata:</strong> ' . $invoiceId . '</div>';
                                        
                                        // Try to process payment
                                        if ($paymentData['status'] === 'success') {
                                            try {
                                                $transactionId = $paymentData['reference'];
                                                $paymentAmount = $paymentData['amount'] / 100;
                                                $paymentFee = ($paymentData['fees'] ?? 0) / 100;
                                                
                                                // Validate invoice ID
                                                $validInvoiceId = checkCbInvoiceID($invoiceId, $gatewayParams['name']);
                                                
                                                // Check for duplicate transaction
                                                checkCbTransID($transactionId);
                                                
                                                // Add payment
                                                addInvoicePayment(
                                                    $validInvoiceId,
                                                    $transactionId,
                                                    $paymentAmount,
                                                    $paymentFee,
                                                    $gatewayModuleName
                                                );
                                                
                                                echo '<div class="alert alert-success"><strong>✅ Payment processed successfully!</strong></div>';
                                                
                                            } catch (Exception $e) {
                                                echo '<div class="alert alert-danger"><strong>❌ Error processing payment:</strong> ' . $e->getMessage() . '</div>';
                                            }
                                        }
                                    } else {
                                        echo '<div class="alert alert-warning"><strong>⚠️ No invoice ID found in metadata</strong></div>';
                                    }
                                } else {
                                    echo '<div class="alert alert-danger"><strong>❌ Payment verification failed</strong></div>';
                                }
                            }
                        }
                        ?>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Test Form -->
                <div class="row mb-4">
                    <div class="col-12">
                        <h4>Test Payment Verification</h4>
                        <form method="post">
                            <div class="mb-3">
                                <label for="test_reference" class="form-label">Transaction Reference</label>
                                <input type="text" class="form-control" id="test_reference" name="test_reference" 
                                       placeholder="Enter Lahza transaction reference" required>
                                <div class="form-text">Enter the transaction reference from a completed payment to test verification.</div>
                            </div>
                            <button type="submit" class="btn btn-primary">Test Verification</button>
                        </form>
                    </div>
                </div>

                <!-- Recent Gateway Logs -->
                <div class="row mb-4">
                    <div class="col-12">
                        <h4>Recent Gateway Logs</h4>
                        <?php
                        try {
                            $pdo = Capsule::connection()->getPdo();
                            $stmt = $pdo->prepare("SELECT * FROM tblgatewaylog WHERE gateway = ? ORDER BY date DESC LIMIT 10");
                            $stmt->execute([$gatewayModuleName]);
                            $logs = $stmt->fetchAll(PDO::FETCH_ASSOC);
                            
                            if ($logs) {
                                echo '<div class="table-responsive">';
                                echo '<table class="table table-striped">';
                                echo '<thead><tr><th>Date</th><th>Data</th><th>Result</th></tr></thead>';
                                echo '<tbody>';
                                foreach ($logs as $log) {
                                    echo '<tr>';
                                    echo '<td>' . $log['date'] . '</td>';
                                    echo '<td><div class="code-block" style="max-height: 100px; overflow-y: auto;">' . htmlspecialchars($log['data']) . '</div></td>';
                                    echo '<td>' . htmlspecialchars($log['result']) . '</td>';
                                    echo '</tr>';
                                }
                                echo '</tbody></table>';
                                echo '</div>';
                            } else {
                                echo '<div class="alert alert-info">No gateway logs found. Make sure "Enable Logging" is turned on.</div>';
                            }
                        } catch (Exception $e) {
                            echo '<div class="alert alert-danger">Error fetching logs: ' . $e->getMessage() . '</div>';
                        }
                        ?>
                    </div>
                </div>

                <!-- Callback URL Test -->
                <div class="row mb-4">
                    <div class="col-12">
                        <h4>Callback URL Information</h4>
                        <div class="alert alert-info">
                            <strong>Your Callback URL:</strong><br>
                            <code><?php echo $gatewayParams['systemurl']; ?>modules/gateways/callback/lahza.php</code><br><br>
                            
                            <strong>Webhook URL (for Lahza.io dashboard):</strong><br>
                            <code><?php echo $gatewayParams['systemurl']; ?>modules/gateways/callback/lahza.php</code><br><br>
                            
                            Make sure this URL is accessible from the internet and configured in your Lahza.io dashboard.
                        </div>
                    </div>
                </div>

                <!-- Common Issues -->
                <div class="row mb-4">
                    <div class="col-12">
                        <h4>Common Issues & Solutions</h4>
                        <div class="accordion" id="issuesAccordion">
                            <div class="accordion-item">
                                <h2 class="accordion-header">
                                    <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#issue1">
                                        Payment completed but invoice not marked as paid
                                    </button>
                                </h2>
                                <div id="issue1" class="accordion-collapse collapse show">
                                    <div class="accordion-body">
                                        <strong>Possible causes:</strong>
                                        <ul>
                                            <li>Callback URL not accessible from internet</li>
                                            <li>Webhook not configured in Lahza.io dashboard</li>
                                            <li>Invoice ID not properly passed in metadata</li>
                                            <li>Transaction reference format mismatch</li>
                                        </ul>
                                        <strong>Solutions:</strong>
                                        <ul>
                                            <li>Test callback URL accessibility</li>
                                            <li>Enable logging and check gateway logs</li>
                                            <li>Verify webhook configuration in Lahza.io</li>
                                            <li>Use the test form above to manually verify payments</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Security Warning -->
                <div class="alert alert-warning">
                    <strong>⚠️ Security Warning:</strong> This debug file should be removed from production environments. 
                    It's intended for development and troubleshooting purposes only.
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
