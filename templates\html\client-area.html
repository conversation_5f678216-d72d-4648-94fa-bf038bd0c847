<!DOCTYPE html>
<html lang="en" dir="ltr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Client Area - WIDDX Digital Solutions</title>
    <meta name="description" content="Access your WIDDX client area to manage services, billing, and support tickets.">

    <!-- Performance Optimizations -->
    <meta name="theme-color" content="#010815">
    <link rel="dns-prefetch" href="//fonts.googleapis.com">
    <link rel="dns-prefetch" href="//cdnjs.cloudflare.com">
    <link rel="preconnect" href="https://fonts.googleapis.com" crossorigin>
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>

    <!-- Critical CSS Inline -->
    <style>
        :root{--primary-color:#010815;--secondary-color:#6c5bb9;--accent-color:#c0a5d5;--text-primary:#ffffff;--text-secondary:rgba(255,255,255,0.8);--text-muted:rgba(255,255,255,0.6);--background-primary:#010815;--background-secondary:#0f1419;--background-tertiary:rgba(255,255,255,0.05);--border-color:rgba(192,165,213,0.1);--card-background:rgba(255,255,255,0.05);--navbar-background:rgba(1,8,21,0.95);--footer-background:#000510;--white:#ffffff;--gray-light:#f8f9fa;--gray-dark:#2c3e50;--transition:all 0.3s ease;--border-radius:12px;--box-shadow:0 10px 30px rgba(108,91,185,0.1);--container-max-width:1200px;--section-padding:80px 0}
        *{margin:0;padding:0;box-sizing:border-box}
        body{font-family:'Inter',sans-serif;background:var(--background-primary);color:var(--text-primary);line-height:1.6;overflow-x:hidden}
        .container{max-width:var(--container-max-width);margin:0 auto;padding:0 20px}
        .navbar{position:fixed;top:0;left:0;right:0;z-index:1000;padding:15px 0;background:var(--navbar-background);backdrop-filter:blur(10px);border-bottom:1px solid var(--border-color)}
        .nav-container{display:flex;justify-content:space-between;align-items:center;max-width:var(--container-max-width);margin:0 auto;padding:0 20px}
        .nav-logo a{font-size:1.8rem;font-weight:700;color:var(--accent-color);text-decoration:none;letter-spacing:2px}
        .nav-menu{display:flex;gap:30px;align-items:center}
        .nav-link{color:var(--text-primary);text-decoration:none;font-weight:500;transition:var(--transition);position:relative;padding:8px 0}
        .nav-link:hover,.nav-link.active{color:var(--accent-color)}
        .nav-link::after{content:'';position:absolute;bottom:0;left:0;width:0;height:2px;background:var(--accent-color);transition:width 0.3s ease}
        .nav-link:hover::after,.nav-link.active::after{width:100%}
        .client-area{background:linear-gradient(135deg,var(--secondary-color),var(--accent-color));padding:8px 16px!important;border-radius:20px;color:var(--white)!important}
        .client-area::after{display:none}
        .theme-toggle{background:none;border:2px solid var(--accent-color);color:var(--accent-color);padding:8px 12px;border-radius:50%;cursor:pointer;transition:var(--transition);font-size:14px}
        .theme-toggle:hover{background:var(--accent-color);color:var(--background-primary)}
        .hamburger{display:none;flex-direction:column;cursor:pointer;gap:4px}
        .hamburger span{width:25px;height:3px;background:var(--text-primary);transition:var(--transition)}
        .client-area-hero{min-height:100vh;display:flex;align-items:center;justify-content:center;position:relative;padding:120px 0 80px}
        .client-area-content{text-align:center;max-width:600px;margin:0 auto}
        .client-area-title{font-size:3rem;font-weight:700;margin-bottom:20px;background:linear-gradient(135deg,var(--accent-color),var(--secondary-color));-webkit-background-clip:text;-webkit-text-fill-color:transparent;background-clip:text}
        .client-area-subtitle{font-size:1.2rem;color:var(--text-secondary);margin-bottom:40px}
        .auth-container{background:var(--card-background);border-radius:var(--border-radius);padding:40px;box-shadow:var(--box-shadow);border:1px solid var(--border-color);backdrop-filter:blur(10px)}
        .auth-tabs{display:flex;margin-bottom:30px;border-radius:8px;overflow:hidden;background:var(--background-tertiary)}
        .auth-tab{flex:1;padding:12px 20px;background:none;border:none;color:var(--text-secondary);cursor:pointer;transition:var(--transition);font-weight:500}
        .auth-tab.active{background:var(--secondary-color);color:var(--white)}
        .form-group{margin-bottom:20px;text-align:left}
        .form-label{display:block;margin-bottom:8px;color:var(--text-primary);font-weight:500}
        .form-input{width:100%;padding:12px 16px;background:var(--background-tertiary);border:1px solid var(--border-color);border-radius:8px;color:var(--text-primary);font-size:16px;transition:var(--transition)}
        .form-input:focus{outline:none;border-color:var(--accent-color);box-shadow:0 0 0 3px rgba(192,165,213,0.1)}
        .btn{display:inline-block;padding:12px 30px;background:linear-gradient(135deg,var(--secondary-color),var(--accent-color));color:var(--white);text-decoration:none;border-radius:25px;font-weight:600;transition:var(--transition);border:none;cursor:pointer;font-size:16px;width:100%}
        .btn:hover{transform:translateY(-2px);box-shadow:0 10px 25px rgba(108,91,185,0.3)}
        .forgot-password{text-align:center;margin-top:20px}
        .forgot-password a{color:var(--accent-color);text-decoration:none;font-size:14px}
        .forgot-password a:hover{text-decoration:underline}
        @media(max-width:768px){.nav-menu{display:none}.hamburger{display:flex}.client-area-title{font-size:2rem}.auth-container{padding:30px 20px}}
    </style>

    <!-- Google Fonts with Arabic Support -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Tajawal:wght@300;400;500;700&family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/client-area.css">

    <!-- GSAP -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/ScrollTrigger.min.js"></script>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar" id="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <a href="index.html">WIDDX</a>
            </div>
            <div class="nav-menu" id="nav-menu">
                <a href="index.html" class="nav-link" data-translate="home">Home</a>
                <a href="about.html" class="nav-link" data-translate="about">About</a>
                <a href="services.html" class="nav-link" data-translate="services">Services</a>
                <a href="portfolio.html" class="nav-link" data-translate="portfolio">Portfolio</a>
                <a href="knowledgebase.html" class="nav-link" data-translate="knowledgebase">Knowledgebase</a>
                <a href="contact.html" class="nav-link" data-translate="contact">Contact</a>
                <a href="client-area.html" class="nav-link client-area active" data-translate="clientArea">Client Area</a>
                <button class="language-toggle" id="languageToggle" aria-label="Toggle language">
                    <i class="fas fa-language" id="languageIcon"></i>
                    <span id="languageText">عربي</span>
                </button>
                <button class="theme-toggle" id="themeToggle" aria-label="Toggle theme">
                    <i class="fas fa-moon" id="themeIcon"></i>
                </button>
            </div>
            <div class="hamburger" id="hamburger">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>
    </nav>

    <!-- Client Area Hero Section -->
    <section class="client-area-hero" id="clientAreaHero">
        <div class="container">
            <div class="client-area-content">
                <h1 class="client-area-title">Client Portal</h1>
                <p class="client-area-subtitle">Access your services, manage billing, and get support</p>

                <div class="auth-container" id="authContainer">
                    <!-- Authentication Tabs -->
                    <div class="auth-tabs">
                        <button class="auth-tab active" id="loginTab">Sign In</button>
                        <button class="auth-tab" id="registerTab">Register</button>
                    </div>

                    <!-- Login Form -->
                    <form class="auth-form" id="loginForm">
                        <div class="form-group">
                            <label for="loginEmail" class="form-label">Email Address</label>
                            <input type="email" id="loginEmail" class="form-input" required>
                        </div>
                        <div class="form-group">
                            <label for="loginPassword" class="form-label">Password</label>
                            <input type="password" id="loginPassword" class="form-input" required>
                        </div>
                        <div class="form-group">
                            <label class="checkbox-container">
                                <input type="checkbox" id="rememberMe">
                                <span class="checkmark"></span>
                                Remember me
                            </label>
                        </div>
                        <button type="submit" class="btn">Sign In</button>
                        <div class="forgot-password">
                            <a href="#" id="forgotPasswordLink">Forgot your password?</a>
                        </div>
                    </form>

                    <!-- Register Form -->
                    <form class="auth-form" id="registerForm" style="display: none;">
                        <div class="form-group">
                            <label for="registerFirstName" class="form-label">First Name</label>
                            <input type="text" id="registerFirstName" class="form-input" required>
                        </div>
                        <div class="form-group">
                            <label for="registerLastName" class="form-label">Last Name</label>
                            <input type="text" id="registerLastName" class="form-input" required>
                        </div>
                        <div class="form-group">
                            <label for="registerEmail" class="form-label">Email Address</label>
                            <input type="email" id="registerEmail" class="form-input" required>
                        </div>
                        <div class="form-group">
                            <label for="registerPassword" class="form-label">Password</label>
                            <input type="password" id="registerPassword" class="form-input" required>
                        </div>
                        <div class="form-group">
                            <label for="registerConfirmPassword" class="form-label">Confirm Password</label>
                            <input type="password" id="registerConfirmPassword" class="form-input" required>
                        </div>
                        <div class="form-group">
                            <label class="checkbox-container">
                                <input type="checkbox" id="agreeTerms" required>
                                <span class="checkmark"></span>
                                I agree to the <a href="#" class="terms-link">Terms of Service</a>
                            </label>
                        </div>
                        <button type="submit" class="btn">Create Account</button>
                    </form>
                </div>
            </div>
        </div>
    </section>

    <!-- Modern Background -->
    <div class="modern-gradient-background">
        <div class="gradient-layer-1"></div>
        <div class="gradient-layer-2"></div>
        <div class="gradient-layer-3"></div>
        <div class="gradient-sphere-1"></div>
        <div class="gradient-sphere-2"></div>
        <div class="gradient-sphere-3"></div>
        <div class="gradient-sphere-4"></div>
        <div class="wave-element"></div>
        <div class="particle-system"></div>
        <div class="noise-texture"></div>
    </div>

    <!-- Scripts -->
    <script defer src="js/language-manager.js"></script>
    <script defer src="js/modern-background.js"></script>
    <script defer src="js/main.js"></script>
    <script defer src="js/client-area.js"></script>
    <script defer src="js/gsap-animations.js"></script>

    <!-- Background System -->
    <script src="background/background.js"></script>
</body>
</html>
