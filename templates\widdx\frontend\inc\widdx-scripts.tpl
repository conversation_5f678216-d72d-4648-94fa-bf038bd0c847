<!-- WebGL Background Animation -->
{if $templatefile == 'homepage'}
<script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r159/three.min.js"></script>
<script src="{$WEB_ROOT}/templates/{$template}/assets/js/webgl-background.js"></script>
<script>
    // Add background container if it doesn't exist
    if (!document.querySelector('.main-background')) {
        const bgContainer = document.createElement('div');
        bgContainer.className = 'main-background';
        bgContainer.style.position = 'fixed';
        bgContainer.style.top = '0';
        bgContainer.style.left = '0';
        bgContainer.style.width = '100%';
        bgContainer.style.height = '100%';
        bgContainer.style.zIndex = '-1';
        bgContainer.style.pointerEvents = 'none';
        document.body.appendChild(bgContainer);
    }
</script>
{/if}

<!-- Other scripts -->
<script src="{$WEB_ROOT}/templates/{$template}/frontend/assets/js/widdx-main.js"></script>