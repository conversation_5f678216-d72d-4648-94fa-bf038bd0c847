/* Modern Gradient Background System */
.modern-gradient-background {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  overflow: hidden;
  pointer-events: none;
}

/* Gradient Layers */
.gradient-layer-1 {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    135deg,
    var(--primary-color) 0%,
    var(--primary-light) 50%,
    var(--primary-dark) 100%
  );
  opacity: 0.1;
}

.gradient-layer-2 {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(
    circle at 50% 50%,
    var(--secondary-color) 0%,
    transparent 70%
  );
  opacity: 0.05;
}

.gradient-layer-3 {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    45deg,
    var(--info-color) 0%,
    transparent 100%
  );
  opacity: 0.05;
}

/* Gradient Spheres */
.gradient-sphere {
  position: absolute;
  border-radius: 50%;
  filter: blur(60px);
  opacity: 0.15;
  animation: float 20s infinite ease-in-out;
}

.gradient-sphere-1 {
  width: 300px;
  height: 300px;
  background: var(--primary-color);
  top: 20%;
  left: 10%;
  animation-delay: 0s;
}

.gradient-sphere-2 {
  width: 400px;
  height: 400px;
  background: var(--secondary-color);
  top: 60%;
  right: 10%;
  animation-delay: -5s;
}

.gradient-sphere-3 {
  width: 250px;
  height: 250px;
  background: var(--info-color);
  bottom: 20%;
  left: 30%;
  animation-delay: -10s;
}

.gradient-sphere-4 {
  width: 350px;
  height: 350px;
  background: var(--primary-light);
  top: 30%;
  right: 30%;
  animation-delay: -15s;
}

/* Wave Element */
.wave-element {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100px;
  background: linear-gradient(
    180deg,
    transparent 0%,
    var(--primary-color) 100%
  );
  opacity: 0.05;
  animation: wave 10s infinite ease-in-out;
}

/* Particle System */
.particle-system {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.particle {
  position: absolute;
  background: var(--primary-color);
  border-radius: 50%;
  pointer-events: none;
  animation: float-up var(--animation-duration) infinite ease-in-out;
  animation-delay: var(--animation-delay);
  transform: translateX(var(--horizontal-drift));
}

/* Noise Texture */
.noise-texture {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIzMDAiIGhlaWdodD0iMzAwIj48ZmlsdGVyIGlkPSJhIiB4PSIwIiB5PSIwIj48ZmVUdXJidWxlbmNlIGJhc2VGcmVxdWVuY3k9Ii43NSIgc3RpdGNoVGlsZXM9InN0aXRjaCIgdHlwZT0iZnJhY3RhbE5vaXNlIi8+PGZlQ29sb3JNYXRyaXggdHlwZT0ic2F0dXJhdGUiIHZhbHVlcz0iMCIvPjwvZmlsdGVyPjxwYXRoIGQ9Ik0wIDBoMzAwdjMwMEgweiIgZmlsdGVyPSJ1cmwoI2EpIiBvcGFjaXR5PSIuMDUiLz48L3N2Zz4=');
  opacity: 0.4;
  mix-blend-mode: overlay;
}

/* Animations */
@keyframes float {
  0%, 100% {
    transform: translate(0, 0);
  }
  25% {
    transform: translate(50px, 50px);
  }
  50% {
    transform: translate(0, 100px);
  }
  75% {
    transform: translate(-50px, 50px);
  }
}

@keyframes float-up {
  0% {
    transform: translateY(100vh) translateX(var(--horizontal-drift));
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateY(-100px) translateX(var(--horizontal-drift));
    opacity: 0;
  }
}

@keyframes wave {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(20px);
  }
}

/* Dark Theme Adjustments */
[data-bs-theme="dark"] .gradient-layer-1 {
  opacity: 0.15;
}

[data-bs-theme="dark"] .gradient-layer-2 {
  opacity: 0.08;
}

[data-bs-theme="dark"] .gradient-layer-3 {
  opacity: 0.08;
}

[data-bs-theme="dark"] .gradient-sphere {
  opacity: 0.2;
}

[data-bs-theme="dark"] .wave-element {
  opacity: 0.08;
}

[data-bs-theme="dark"] .particle {
  background: var(--primary-light);
}

/* Performance Optimizations */
@media (prefers-reduced-motion: reduce) {
  .gradient-sphere,
  .wave-element,
  .particle {
    animation: none;
  }
}

/* Mobile Optimizations */
@media (max-width: 768px) {
  .gradient-sphere {
    filter: blur(40px);
  }
  
  .wave-element {
    height: 50px;
  }
} 