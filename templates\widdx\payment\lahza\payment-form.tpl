{*
 * Lahza.io Payment Form Template for WHMCS WIDDX
 * 
 * Enhanced payment form template with modern UI/UX
 * Supports both popup and redirect payment methods
 * 
 * <AUTHOR> Development Team
 * @version 2.0.0
 *}

{* Include custom CSS for Lahza payment *}
<link href="{$WEB_ROOT}/templates/{$template}/payment/lahza/lahza-payment.css" rel="stylesheet">

{* Payment Container *}
<div class="lahza-payment-container card border-0 shadow-lg">
    <div class="card-body p-0">
        
        {* Payment Header *}
        <div class="payment-header">
            <div class="payment-logo">
                <i class="fas fa-credit-card"></i>
            </div>
            <h4 class="mb-2">
                <i class="fas fa-lock me-2"></i>
                {if $language == 'arabic'}دفع آمن{else}Secure Payment{/if}
            </h4>
            <p class="mb-0">
                {if $language == 'arabic'}مدعوم بواسطة Lahza.io{else}Powered by <PERSON>hza.io{/if}
            </p>
        </div>
        
        {* Payment Details *}
        <div class="payment-details">
            <div class="row">
                <div class="col-md-6">
                    <small class="text-muted">
                        {if $language == 'arabic'}رقم الفاتورة{else}Invoice{/if}
                    </small>
                    <div class="fw-bold">#{$invoiceId}</div>
                </div>
                <div class="col-md-6 text-md-end">
                    <small class="text-muted">
                        {if $language == 'arabic'}المبلغ{else}Amount{/if}
                    </small>
                    <div class="fw-bold text-primary">{$amount} {$currency}</div>
                </div>
            </div>
            
            {if $description}
            <div class="row mt-3">
                <div class="col-12">
                    <small class="text-muted">
                        {if $language == 'arabic'}الوصف{else}Description{/if}
                    </small>
                    <div class="fw-bold">{$description|escape}</div>
                </div>
            </div>
            {/if}
            
            {if $clientName}
            <div class="row mt-3">
                <div class="col-12">
                    <small class="text-muted">
                        {if $language == 'arabic'}العميل{else}Client{/if}
                    </small>
                    <div class="fw-bold">{$clientName|escape}</div>
                </div>
            </div>
            {/if}
        </div>

        {* Payment Method Selection (if multiple channels available) *}
        {if $allowedChannels && count($allowedChannels) > 1}
        <div class="payment-methods px-4 mb-3">
            <small class="text-muted d-block mb-2">
                {if $language == 'arabic'}طرق الدفع المتاحة{else}Available Payment Methods{/if}
            </small>
            <div class="row g-2">
                {foreach $allowedChannels as $channel}
                <div class="col-6 col-md-4">
                    <div class="payment-method-item text-center p-2 border rounded">
                        {if $channel == 'card'}
                            <i class="fas fa-credit-card text-primary"></i>
                            <small class="d-block mt-1">
                                {if $language == 'arabic'}بطاقة{else}Card{/if}
                            </small>
                        {elseif $channel == 'bank'}
                            <i class="fas fa-university text-success"></i>
                            <small class="d-block mt-1">
                                {if $language == 'arabic'}بنك{else}Bank{/if}
                            </small>
                        {elseif $channel == 'mobile_money'}
                            <i class="fas fa-mobile-alt text-info"></i>
                            <small class="d-block mt-1">
                                {if $language == 'arabic'}محفظة{else}Mobile{/if}
                            </small>
                        {elseif $channel == 'qr'}
                            <i class="fas fa-qrcode text-warning"></i>
                            <small class="d-block mt-1">QR</small>
                        {/if}
                    </div>
                </div>
                {/foreach}
            </div>
        </div>
        {/if}

        {* Payment Button *}
        <div class="px-4 pb-2">
            <button type="button" 
                    id="lahza-pay-btn" 
                    class="btn btn-primary btn-lg w-100 lahza-pay-btn"
                    data-public-key="{$publicKey}"
                    data-email="{$email}"
                    data-mobile="{$mobile}"
                    data-first-name="{$firstName}"
                    data-last-name="{$lastName}"
                    data-amount="{$amountInCents}"
                    data-currency="{$currency}"
                    data-reference="{$transactionRef}"
                    data-channels="{$channelsJson}"
                    data-metadata="{$metadataJson}">
                <i class="fas fa-credit-card me-2"></i>
                {if $language == 'arabic'}
                    ادفع الآن - {$amount} {$currency}
                {else}
                    {$langPayNow} - {$amount} {$currency}
                {/if}
            </button>
        </div>
        
        {* Security Notice *}
        <div class="payment-security text-center pb-4">
            <small class="text-muted">
                <i class="fas fa-shield-alt text-success"></i>
                {if $language == 'arabic'}
                    دفعتك محمية بتشفير SSL 256-bit
                {else}
                    Your payment is secured with 256-bit SSL encryption
                {/if}
            </small>
        </div>
        
        {* Error Messages *}
        <div id="lahza-payment-errors" class="alert alert-danger mx-4 mb-4" style="display: none;">
            <i class="fas fa-exclamation-triangle"></i>
            <span class="error-message"></span>
        </div>
        
        {* Loading State *}
        <div id="lahza-payment-loading" class="text-center py-4" style="display: none;">
            <div class="spinner-border text-primary mb-3" role="status">
                <span class="visually-hidden">
                    {if $language == 'arabic'}جاري التحميل...{else}Loading...{/if}
                </span>
            </div>
            <div>
                <small class="text-muted">
                    {if $language == 'arabic'}جاري معالجة الدفع...{else}Processing payment...{/if}
                </small>
            </div>
        </div>
    </div>
</div>

{* Support Information *}
<div class="text-center mt-4">
    <small class="text-muted">
        {if $language == 'arabic'}
            تحتاج مساعدة؟ <a href="{$WEB_ROOT}/contact.php" class="text-decoration-none">اتصل بنا</a>
        {else}
            Need help? <a href="{$WEB_ROOT}/contact.php" class="text-decoration-none">Contact us</a>
        {/if}
    </small>
</div>

{* Include Lahza.io JavaScript Library *}
<script src="https://js.lahza.io/inline.min.js"></script>

{* Include Custom JavaScript *}
<script src="{$WEB_ROOT}/templates/{$template}/payment/lahza/lahza-payment.js"></script>

{* Initialize Payment Data *}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Set global payment configuration
    window.lahzaPaymentData = {
        key: '{$publicKey}',
        email: '{$email}',
        mobile: '{$mobile}',
        firstName: '{$firstName}',
        lastName: '{$lastName}',
        amount: {$amountInCents},
        currency: '{$currency}',
        ref: '{$transactionRef}',
        channels: {$channelsJson},
        metadata: {$metadataJson}
    };
    
    window.lahzaReturnUrl = '{$returnUrl}';
    window.lahzaEnableLogging = {if $enableLogging}true{else}false{/if};
    
    {if $enableLogging}
    console.log('Lahza payment initialized:', window.lahzaPaymentData);
    {/if}
});
</script>

{* Custom CSS if provided *}
{if $customCSS}
<style>
{$customCSS}
</style>
{/if}

{* RTL Support *}
{if $language == 'arabic' || $language == 'hebrew' || $language == 'farsi'}
<style>
.payment-header h4 {
    direction: rtl;
}
.payment-details {
    direction: rtl;
    text-align: right;
}
.payment-details .text-md-end {
    text-align: right !important;
}
.lahza-pay-btn {
    direction: rtl;
}
.payment-security {
    direction: rtl;
}
</style>
{/if}
