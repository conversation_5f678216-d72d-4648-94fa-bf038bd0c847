<?php
/**
 * Lahza.io Payment Gateway Test Script for WHMCS WIDDX
 * 
 * This script helps test the Lahza.io payment gateway integration
 * Use this for debugging and validation purposes only
 * 
 * <AUTHOR> Development Team
 * @version 2.0.0
 * 
 * IMPORTANT: Remove this file from production environment
 */

// Security check - only allow in development/test environments
if (!defined('LAHZA_TEST_MODE') && !isset($_GET['test_mode'])) {
    die('Test mode not enabled. Add ?test_mode=1 to URL or define LAHZA_TEST_MODE constant.');
}

// Include WHMCS configuration if available
if (file_exists(__DIR__ . '/../../../../init.php')) {
    require_once __DIR__ . '/../../../../init.php';
}

// Include Lahza config
require_once __DIR__ . '/config.php';

?>
<!DOCTYPE html>
<html lang="en" dir="ltr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Lahza.io Payment Gateway Test - WHMCS WIDDX</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="lahza-payment.css" rel="stylesheet">
    
    <style>
        body { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; }
        .test-container { max-width: 800px; margin: 2rem auto; }
        .test-card { background: white; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); }
        .test-header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 2rem; border-radius: 15px 15px 0 0; }
        .status-badge { padding: 0.5rem 1rem; border-radius: 20px; font-size: 0.9rem; }
        .status-success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .status-error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .status-warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .test-section { margin: 2rem 0; padding: 1.5rem; border: 1px solid #e9ecef; border-radius: 10px; }
        .code-block { background: #f8f9fa; padding: 1rem; border-radius: 5px; font-family: monospace; font-size: 0.9rem; }
    </style>
</head>
<body>
    <div class="container test-container">
        <div class="test-card">
            <!-- Header -->
            <div class="test-header text-center">
                <h1><i class="fas fa-credit-card me-3"></i>Lahza.io Payment Gateway Test</h1>
                <p class="mb-0">WHMCS WIDDX Template Integration Test</p>
            </div>
            
            <div class="p-4">
                <!-- System Information -->
                <div class="test-section">
                    <h3><i class="fas fa-info-circle text-primary me-2"></i>System Information</h3>
                    <div class="row">
                        <div class="col-md-6">
                            <strong>PHP Version:</strong> <?php echo PHP_VERSION; ?><br>
                            <strong>Server:</strong> <?php echo $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown'; ?><br>
                            <strong>HTTPS:</strong> 
                            <?php if (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on'): ?>
                                <span class="status-badge status-success">Enabled</span>
                            <?php else: ?>
                                <span class="status-badge status-warning">Disabled</span>
                            <?php endif; ?>
                        </div>
                        <div class="col-md-6">
                            <strong>cURL:</strong> 
                            <?php if (function_exists('curl_init')): ?>
                                <span class="status-badge status-success">Available</span>
                            <?php else: ?>
                                <span class="status-badge status-error">Not Available</span>
                            <?php endif; ?><br>
                            
                            <strong>JSON:</strong> 
                            <?php if (function_exists('json_encode')): ?>
                                <span class="status-badge status-success">Available</span>
                            <?php else: ?>
                                <span class="status-badge status-error">Not Available</span>
                            <?php endif; ?><br>
                            
                            <strong>OpenSSL:</strong> 
                            <?php if (extension_loaded('openssl')): ?>
                                <span class="status-badge status-success">Available</span>
                            <?php else: ?>
                                <span class="status-badge status-error">Not Available</span>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                
                <!-- File Structure Test -->
                <div class="test-section">
                    <h3><i class="fas fa-folder text-primary me-2"></i>File Structure Test</h3>
                    <?php
                    $requiredFiles = [
                        'Gateway Module' => '../../../../modules/gateways/lahza.php',
                        'Callback Handler' => '../../../../modules/gateways/callback/lahza.php',
                        'Payment Template' => 'payment-form.tpl',
                        'CSS Styles' => 'lahza-payment.css',
                        'JavaScript' => 'lahza-payment.js',
                        'Configuration' => 'config.php'
                    ];
                    
                    foreach ($requiredFiles as $name => $file):
                        $exists = file_exists(__DIR__ . '/' . $file);
                    ?>
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span><strong><?php echo $name; ?>:</strong> <?php echo basename($file); ?></span>
                        <?php if ($exists): ?>
                            <span class="status-badge status-success"><i class="fas fa-check"></i> Found</span>
                        <?php else: ?>
                            <span class="status-badge status-error"><i class="fas fa-times"></i> Missing</span>
                        <?php endif; ?>
                    </div>
                    <?php endforeach; ?>
                </div>
                
                <!-- Configuration Test -->
                <div class="test-section">
                    <h3><i class="fas fa-cog text-primary me-2"></i>Configuration Test</h3>
                    
                    <!-- Currency Support -->
                    <h5>Supported Currencies:</h5>
                    <div class="row mb-3">
                        <?php foreach (LahzaPaymentConfig::SUPPORTED_CURRENCIES as $code => $info): ?>
                        <div class="col-md-4 mb-2">
                            <div class="border rounded p-2 text-center">
                                <strong><?php echo $code; ?></strong><br>
                                <small><?php echo $info['name']; ?></small><br>
                                <span class="text-muted"><?php echo $info['symbol']; ?></span>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                    
                    <!-- Payment Channels -->
                    <h5>Payment Channels:</h5>
                    <div class="row">
                        <?php foreach (LahzaPaymentConfig::PAYMENT_CHANNELS as $code => $info): ?>
                        <div class="col-md-6 mb-2">
                            <div class="border rounded p-2">
                                <i class="<?php echo $info['icon']; ?> me-2"></i>
                                <strong><?php echo $info['name']; ?></strong><br>
                                <small class="text-muted"><?php echo $info['description']; ?></small>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>
                
                <!-- API Connectivity Test -->
                <div class="test-section">
                    <h3><i class="fas fa-globe text-primary me-2"></i>API Connectivity Test</h3>
                    
                    <?php
                    // Test API connectivity
                    $apiTests = [
                        'Lahza API Base URL' => 'https://api.lahza.io',
                        'JavaScript Library' => 'https://js.lahza.io/inline.min.js'
                    ];
                    
                    foreach ($apiTests as $name => $url):
                        $ch = curl_init();
                        curl_setopt($ch, CURLOPT_URL, $url);
                        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
                        curl_setopt($ch, CURLOPT_NOBODY, true);
                        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
                        
                        $result = curl_exec($ch);
                        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                        $error = curl_error($ch);
                        curl_close($ch);
                        
                        $status = ($httpCode >= 200 && $httpCode < 400) ? 'success' : 'error';
                    ?>
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span><strong><?php echo $name; ?>:</strong> <?php echo $url; ?></span>
                        <?php if ($status === 'success'): ?>
                            <span class="status-badge status-success"><i class="fas fa-check"></i> Reachable</span>
                        <?php else: ?>
                            <span class="status-badge status-error"><i class="fas fa-times"></i> Error (<?php echo $httpCode; ?>)</span>
                        <?php endif; ?>
                    </div>
                    <?php endforeach; ?>
                </div>
                
                <!-- Sample Payment Form -->
                <div class="test-section">
                    <h3><i class="fas fa-credit-card text-primary me-2"></i>Sample Payment Form</h3>
                    <p class="text-muted">This is a sample of how the payment form will look:</p>
                    
                    <!-- Include sample payment form -->
                    <div id="sample-payment-form">
                        <!-- This would be populated by the actual payment form template -->
                        <div class="lahza-payment-container card border-0 shadow-sm">
                            <div class="card-body p-4">
                                <div class="payment-header text-center mb-4">
                                    <div class="payment-logo mb-3">
                                        <i class="fas fa-credit-card text-primary" style="font-size: 2.5rem;"></i>
                                    </div>
                                    <h4 class="card-title mb-2">
                                        <i class="fas fa-lock text-success"></i> Secure Payment
                                    </h4>
                                    <p class="text-muted mb-0">Powered by Lahza.io</p>
                                </div>
                                
                                <div class="payment-details bg-light rounded p-3 mb-4">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <small class="text-muted">Invoice</small>
                                            <div class="fw-bold">#TEST-001</div>
                                        </div>
                                        <div class="col-md-6 text-md-end">
                                            <small class="text-muted">Amount</small>
                                            <div class="fw-bold text-primary">100.00 USD</div>
                                        </div>
                                    </div>
                                    <div class="row mt-2">
                                        <div class="col-12">
                                            <small class="text-muted">Description</small>
                                            <div class="fw-bold">Test Payment for WHMCS Integration</div>
                                        </div>
                                    </div>
                                </div>

                                <button type="button" class="btn btn-primary btn-lg w-100 mb-3" disabled>
                                    <i class="fas fa-credit-card me-2"></i>
                                    Pay Now - 100.00 USD (Test Mode)
                                </button>
                                
                                <div class="payment-security text-center">
                                    <small class="text-muted">
                                        <i class="fas fa-shield-alt text-success"></i>
                                        Your payment is secured with 256-bit SSL encryption
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Next Steps -->
                <div class="test-section">
                    <h3><i class="fas fa-list-check text-primary me-2"></i>Next Steps</h3>
                    <ol>
                        <li><strong>Configure Gateway:</strong> Go to WHMCS Admin → Setup → Payments → Payment Gateways</li>
                        <li><strong>Add API Keys:</strong> Enter your Lahza.io Public and Secret keys</li>
                        <li><strong>Test Payment:</strong> Create a test invoice and try the payment process</li>
                        <li><strong>Configure Webhook:</strong> Set up webhook URL in your Lahza.io dashboard</li>
                        <li><strong>Go Live:</strong> Switch to live keys when ready for production</li>
                    </ol>
                </div>
                
                <!-- Security Notice -->
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>Security Notice:</strong> This test file should be removed from production environments. 
                    It's intended for development and testing purposes only.
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
